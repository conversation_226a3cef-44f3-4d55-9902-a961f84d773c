
-- kf_grid.nsalary_monthsalary_detail definition

-- CREATE TABLE `nsalary_monthsalary_detail` (
--   `id` bigint(20) NOT NULL AUTO_INCREMENT,
--   `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员分公司',
--   `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员支公司',
--   `operid` bigint(20) DEFAULT NULL COMMENT '操作员id',
--   `position_id` bigint(20) DEFAULT NULL COMMENT '岗位id',
--   `smonth` bigint(20) DEFAULT NULL COMMENT '月份，YYYYMM',
--   `wage` decimal(20,2) DEFAULT NULL COMMENT '薪酬金额',
--   `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
--   `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
--   `item_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目大类,SYS_NSALSRY_TYPE',
--   `item_code` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '薪酬项目编码',
--   `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
--   `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
--   `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--   `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
--   `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
--   `stadate` varchar(8) COLLATE utf8mb4_general_ci DEFAULT NULL,
--   PRIMARY KEY (`id`) USING BTREE
-- ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='员工月度薪酬明细表';




-- kf_grid.nsalary_oper_dailysalary definition

CREATE TABLE `nsalary_oper_dailysalary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员支公司',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员id',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位id',
  `stadate` bigint(20) DEFAULT NULL COMMENT '月份，YYYYMM',
  `wage` decimal(20,2) DEFAULT NULL COMMENT '薪酬金额',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='员工月度薪酬表';


-- kf_grid.nsalary_dailysalary_detail definition

CREATE TABLE `nsalary_dailysalary_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员支公司',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员id',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位id',
  `stadate` bigint(20) DEFAULT NULL COMMENT '天，YYYYMMDD',
  `wage` decimal(20,2) DEFAULT NULL COMMENT '薪酬金额',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `item_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目大类,SYS_NSALSRY_TYPE',
  `item_code` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '薪酬项目编码',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='员工月度薪酬明细表';



-- kf_grid.nsalary_oper_monthsalary definition

CREATE TABLE `nsalary_oper_monthsalary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员支公司',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员id',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位id',
  `smonth` bigint(20) DEFAULT NULL COMMENT '月份，YYYYMM',
  `wage` decimal(20,2) DEFAULT NULL COMMENT '薪酬金额',
  `status` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态,0 未提交审核，1审核中，2审核通过，3审核不通过',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `stadate` varchar(8) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='员工月度薪酬表';



-- kf_grid.prv_position_level definition

CREATE TABLE `prv_position_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支公司',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位id',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '评级条件，0-当日自营现金流',
  `scontrol` bigint(20) DEFAULT NULL COMMENT '起始控制线，>=',
  `econtrol` bigint(20) DEFAULT NULL COMMENT '截止控制线，<',
  `levelname` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '级别名称',
  `leveldetail` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '细则说明',
  `levelicon` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '等级图标',
  `stime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效时间',
  `etime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失效时间',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='岗位等级配置表';


-- kf_grid.nsalary_rank_config definition

CREATE TABLE `nsalary_rank_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `city` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分公司 可多选，逗号拼串，*代表所有',
  `area` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '业务区 可多选，逗号拼串，*代表所有',
  `listType` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '榜单类型 武汉续费率1、武汉自营现金流2、完单率3、武汉勤劳小蜜蜂4、全省统一现金流5、全省统一续费率6',
  `rankType` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '排名类型 支公司area、分公司city、省公司prov',
  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `sort_list_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '榜单展示先后顺序',
  `list_type_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '榜单名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='配置榜单的查看权限';



-- kf_grid.nsalary_item_info definition

CREATE TABLE `nsalary_item_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目大类,SYS_NSALSRY_TYPE',
  `item_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目名称',
  `item_showname` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机端显示名称',
  `item_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目代码，唯一性，对应后台的取数逻辑',
  `source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据来源，0-	系统自动统计，1-人工导入数据',
  `edit_flag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否允许调整，Y-允许调整，N-不允许调整',
  `paramjson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '存放跑数逻辑所需的参数模板，json格式',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `sta_flag` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '统计频率，D-每日统计月底汇总，M-月底统计',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='薪酬项目配置表';


-- kf_grid.nsalary_position_item definition

CREATE TABLE `nsalary_position_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位id',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目id',
  `intro_info` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '介绍信息',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开始生效月份，YYYYMM',
  `emonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结束生效月份，YYYYMM',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='岗位薪酬项目表';


-- kf_grid.tw_nsalary_maintain_reward definition

CREATE TABLE `tw_nsalary_maintain_reward` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `gridcode` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格ID（逗号拼串）',
  `gridname` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称（逗号拼串）',
  `masterdev_cnts` bigint(20) DEFAULT NULL COMMENT '主终端数',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价（元/户）',
  `maintenance_reward` decimal(10,2) DEFAULT NULL COMMENT '维护薪酬金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='维护薪酬';


-- kf_grid.tw_nsalary_telphone_reward_m definition

CREATE TABLE `tw_nsalary_telphone_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `order_cnts` bigint(20) DEFAULT NULL COMMENT '电话营销收费成功工单笔数',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费总金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励总金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电话营销奖励月汇总表';


-- kf_grid.tw_nsalary_telphone_reward_d definition

CREATE TABLE `tw_nsalary_telphone_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `order_cnts` bigint(20) DEFAULT NULL COMMENT '电话营销收费成功工单笔数',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费总金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励总金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电话营销奖励日汇总表';


-- kf_grid.tw_nsalary_grid_manager_reward_am definition

CREATE TABLE `tw_nsalary_grid_manager_reward_am` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='网格经理区域营销奖励月汇总明细表';


-- kf_grid.tw_nsalary_grid_manager_reward_ad definition

CREATE TABLE `tw_nsalary_grid_manager_reward_ad` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `market_reward_proportion` decimal(10,4) DEFAULT NULL COMMENT '区域现金流提成比例',
  `position_level_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位等级编码',
  `level_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位等级中文',
  `position_rate` decimal(10,4) DEFAULT NULL COMMENT '岗位系数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='网格经理区域营销奖励日汇总明细表';

-- kf_grid.tw_nsalary_grid_manager_reward_sm definition

CREATE TABLE `tw_nsalary_grid_manager_reward_sm` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='网格经理自营营销奖励月汇总明细表';


-- kf_grid.tw_nsalary_grid_manager_reward_sd definition

CREATE TABLE `tw_nsalary_grid_manager_reward_sd` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `market_reward_proportion` decimal(10,4) DEFAULT NULL COMMENT '自营现金流提成比例',
  `position_level_code` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位等级编码',
  `level_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位等级中文',
  `position_rate` decimal(10,4) DEFAULT NULL COMMENT '岗位系数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='网格经理自营营销奖励日汇总明细表';


-- kf_grid.nsalary_monthsalary_adjust definition

CREATE TABLE `nsalary_monthsalary_adjust` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员支公司',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员id',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位id',
  `month` bigint(20) DEFAULT NULL COMMENT '鱼粉，YYYYMM',
  `adjust_wage` decimal(20,2) DEFAULT NULL COMMENT '调整薪酬金额',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `item_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目大类,SYS_NSALSRY_TYPE',
  `item_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '薪酬项目编码',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='月度薪酬调整表';


-- kf_grid.tw_nsalary_rankcomplete_reward definition

CREATE TABLE `tw_nsalary_rankcomplete_reward` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理名称',
  `grid_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `complete_rate` decimal(20,4) DEFAULT NULL COMMENT '完单率',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `standard_reward` decimal(10,2) DEFAULT NULL COMMENT '标准奖励金',
  `prov_rank` bigint(20) DEFAULT NULL,
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='完单率排名奖月表';


-- kf_grid.tw_nsalary_rankcash_reward definition

CREATE TABLE `tw_nsalary_rankcash_reward` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理名称',
  `grid_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `oper_fees` decimal(20,4) DEFAULT NULL COMMENT '当月累计自营现金流',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `standard_reward` decimal(10,2) DEFAULT NULL COMMENT '标准奖励金',
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='现金流排名奖月表';


-- kf_grid.tw_nsalary_rankren_reward definition

CREATE TABLE `tw_nsalary_rankren_reward` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理名称',
  `grid_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `renewal_rate` decimal(20,4) DEFAULT NULL COMMENT '续费率',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `standard_reward` decimal(10,2) DEFAULT NULL COMMENT '标准奖励金',
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='续费率排名奖月表';


-- kf_grid.tw_nsalary_work_out_fine_m definition

CREATE TABLE `tw_nsalary_work_out_fine_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` bigint(20) DEFAULT NULL COMMENT '超时工单笔数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='超时限未完单扣罚月汇总表';


-- kf_grid.tw_nsalary_work_out_fine_d definition

CREATE TABLE `tw_nsalary_work_out_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` bigint(20) DEFAULT NULL COMMENT '超时工单笔数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='营销单超时扣罚日汇总表';


-- kf_grid.tw_nsalary_sv_maintain_fine_m definition

CREATE TABLE `tw_nsalary_sv_maintain_fine_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` decimal(10,2) DEFAULT NULL COMMENT '维修工单个数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导维修单扣罚月汇总表';


-- kf_grid.tw_nsalary_sv_maintain_fine_d definition

CREATE TABLE `tw_nsalary_sv_maintain_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` decimal(10,2) DEFAULT NULL COMMENT '维修工单个数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导维修扣罚单日汇总表';

-- kf_grid.tw_nsalary_online_cust_fine_m2 definition

CREATE TABLE `tw_nsalary_online_cust_fine_m2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码（多个网格时逗号拼串）',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文（多个网格时逗号拼串）',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当月扣罚总金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='在线户数扣罚月汇总表2';


-- kf_grid.tw_nsalary_online_cust_fine_d2 definition

CREATE TABLE `tw_nsalary_online_cust_fine_d2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码（多个网格时逗号拼串）',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文（多个网格时逗号拼串）',
  `increase_cnts` bigint(20) DEFAULT NULL COMMENT '当日增长户数',
  `increase_unit_price` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励单价',
  `increase_reward` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励',
  `reduce_cnts` bigint(20) DEFAULT NULL COMMENT '当日减少户数',
  `reduce_unit_price` decimal(10,2) DEFAULT NULL COMMENT '用户减少单价',
  `reduce_fine` decimal(10,2) DEFAULT NULL COMMENT '用户减少扣罚',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当日扣罚总金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='在线户数扣罚日汇总表2';


-- kf_grid.tw_nsalary_refund_fine_m definition

CREATE TABLE `tw_nsalary_refund_fine_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `refund_cnts` bigint(20) DEFAULT NULL COMMENT '退费户数',
  `refund_fees` decimal(10,2) DEFAULT NULL COMMENT '退费金额',
  `fine_proportion` decimal(10,4) DEFAULT NULL COMMENT '扣罚比例',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退费扣罚月汇总表';


-- kf_grid.tw_nsalary_refund_fine_d definition

CREATE TABLE `tw_nsalary_refund_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `refund_cnts` bigint(20) DEFAULT NULL COMMENT '退费户数',
  `refund_fees` decimal(10,2) DEFAULT NULL COMMENT '退费金额',
  `fine_proportion` decimal(10,4) DEFAULT NULL COMMENT '扣罚比例',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退费扣罚日汇总表';


-- kf_grid.tw_nsalary_grid_manager_reward_sdet definition

CREATE TABLE `tw_nsalary_grid_manager_reward_sdet` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `bossserialno` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作流水号',
  `orderid` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `paytime` datetime DEFAULT NULL COMMENT '业务操作时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务操作部门ID',
  `deptname` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作部门中文',
  `opcode` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型编码',
  `opcodename` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型中文',
  `custid` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `city` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `fees` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `market_reward_proportion` decimal(10,4) DEFAULT NULL COMMENT '自营现金流提成比例',
  `position_rate` decimal(10,4) DEFAULT NULL COMMENT '岗位系数',
  `level_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '岗位等级中文',
  `position_level_code` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位等级编码',
  `orderno` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_subtype_name` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='网格经理自营营销奖励用户记录明细表';


-- kf_grid.tw_nsalary_grid_manager_reward_adet definition

CREATE TABLE `tw_nsalary_grid_manager_reward_adet` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `bossserialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作流水号',
  `orderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `paytime` datetime DEFAULT NULL COMMENT '业务操作时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务操作部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作部门中文',
  `opcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型编码',
  `opcodename` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型中文',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `fees` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `market_reward_proportion` decimal(10,4) DEFAULT NULL COMMENT '区域现金流提成比例',
  `position_rate` decimal(10,4) DEFAULT NULL COMMENT '岗位系数',
  `level_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '岗位等级中文',
  `position_level_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位等级编码',
  `renewal_rate` decimal(20,4) DEFAULT NULL COMMENT '续费率',
  `renewal_rate_ratio` decimal(10,2) DEFAULT NULL COMMENT '续费率',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='网格经理区域营销奖励用户记录明细表';



-- kf_grid.tw_nsalary_telphone_reward_det definition

CREATE TABLE `tw_nsalary_telphone_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `porderno` bigint(20) DEFAULT NULL COMMENT '电话营销工单编号',
  `order_subtype` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话营销工单类型编码',
  `order_subtype_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话营销工单类型名称',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `cust_areaname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户业务区名称',
  `cust_patchname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户网格名称',
  `finish_time` datetime DEFAULT NULL COMMENT '电话营销完单时间',
  `sorderno` bigint(20) DEFAULT NULL COMMENT '收费单工单编号',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '收费单完单人操作员ID',
  `bizopername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收费单完单人姓名',
  `paytime` datetime DEFAULT NULL COMMENT '收费单完单时间',
  `bossserialno` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收费单业务流水号',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费单收费金额',
  `award_rate` decimal(10,2) DEFAULT NULL COMMENT '电话营销提成比例',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电话营销奖励用户记录明细表';


-- kf_grid.tw_nsalary_refund_fine_det definition

CREATE TABLE `tw_nsalary_refund_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `bossserialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务流水号',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `paytime` datetime DEFAULT NULL COMMENT '业务收费时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务收费部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务收费部门中文',
  `refund_fees` decimal(10,2) DEFAULT NULL COMMENT '退费金额',
  `fine_proportion` decimal(10,4) DEFAULT NULL COMMENT '扣罚比例',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退费扣罚用户明细表';



-- kf_grid.tw_nsalary_online_cust_fine_d definition

CREATE TABLE `tw_nsalary_online_cust_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `increase_cnts` bigint(20) DEFAULT NULL COMMENT '当日增长户数',
  `increase_unit_price` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励单价',
  `increase_reward` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励',
  `reduce_cnts` bigint(20) DEFAULT NULL COMMENT '当日减少户数',
  `reduce_unit_price` decimal(10,2) DEFAULT NULL COMMENT '用户减少单价',
  `reduce_fine` decimal(10,2) DEFAULT NULL COMMENT '用户减少扣罚',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当日扣罚总金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='在线户数扣罚日汇总表';



-- kf_grid.tw_nsalary_sv_maintain_fine_det definition

CREATE TABLE `tw_nsalary_sv_maintain_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` bigint(20) DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `install_orderid` bigint(20) DEFAULT NULL COMMENT '工单编号',
  `backtime` datetime DEFAULT NULL COMMENT '工单回单时间',
  `backoperid` bigint(20) DEFAULT NULL COMMENT '工单回单操作员ID',
  `backopername` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单回单操作员姓名',
  `nettype` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单网络类型',
  `faulttype` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单故障类型',
  `chargefaulttype` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单计费故障类型',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '实得扣罚金额',
  `gridid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单所属网格ID',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单所属网格中文',
  `managerid` bigint(20) DEFAULT NULL COMMENT '网格经理',
  `managername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理中文',
  `transfertype` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转单类型',
  `super_rate` decimal(18,2) DEFAULT NULL COMMENT '超时转单比例',
  `award_rate` decimal(18,2) DEFAULT NULL COMMENT '非工作时间奖励系数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导维修单扣罚明细表';



-- kf_grid.tw_nsalary_market_trans_fine_det definition

CREATE TABLE `tw_nsalary_market_trans_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` bigint(20) DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `orderid` bigint(20) DEFAULT NULL COMMENT '工单编号',
  `transfer_optime` datetime DEFAULT NULL COMMENT '工单转派时间',
  `gridid` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单网格ID',
  `gridname` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单网格中文',
  `order_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单类型',
  `linkaddr` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户地址',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '实得扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='营销单转单扣罚明细表';


-- kf_grid.tw_nsalary_work_out_fine_det definition

CREATE TABLE `tw_nsalary_work_out_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` bigint(20) DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `orderid` bigint(20) DEFAULT NULL COMMENT '工单编号',
  `current_leader` bigint(20) DEFAULT NULL COMMENT '工单当前责任人ID',
  `current_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单当前责任人姓名',
  `create_time` datetime DEFAULT NULL COMMENT '工单创建时间',
  `dispatch_time` datetime DEFAULT NULL COMMENT '派单时间',
  `receive_time` datetime DEFAULT NULL COMMENT '接单时间',
  `outtime` datetime DEFAULT NULL COMMENT '超时时间',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '实得扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='超时限未完单扣罚明细表';



-- kf_grid.nsalary_prov_rankren_daily definition

CREATE TABLE `nsalary_prov_rankren_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计日期 YYYYMMDD',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '统计月份',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '网格经理id',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理名称',
  `gridcode` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `renewal_rate` decimal(20,4) DEFAULT NULL COMMENT '续费率',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `prov_rank` bigint(20) DEFAULT NULL COMMENT '全省排名',
  `estimated_reward` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='全省统一续费率榜单';


-- kf_grid.nsalary_prov_rankcash_daily definition

CREATE TABLE `nsalary_prov_rankcash_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计日期 YYYYMMDD',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '统计月份',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '网格经理id',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理名称',
  `gridcode` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `fees` decimal(20,4) DEFAULT NULL COMMENT '当月累计自营现金流',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `prov_rank` bigint(20) DEFAULT NULL COMMENT '全省排名',
  `estimated_reward` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='全省统一现金流榜单 ';

-- kf_grid.nsalary_rankwork_daily definition

CREATE TABLE `nsalary_rankwork_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计日期 YYYYMMDD',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '月份 YYYYMM',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员姓名',
  `grid_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `distance` decimal(20,3) DEFAULT NULL COMMENT '行程里程数',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `estimated_reward` decimal(10,2) DEFAULT NULL COMMENT '预估奖励金',
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='勤劳小蜜蜂榜单';

-- kf_grid.nsalary_rankcomplete_daily definition

CREATE TABLE `nsalary_rankcomplete_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计日期 YYYYMMDD',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '月份 YYYYMM',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员姓名',
  `grid_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `complete_rate` decimal(20,4) DEFAULT NULL COMMENT '完单率',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `estimated_reward` decimal(10,2) DEFAULT NULL COMMENT '预估奖励金',
  `prov_rank` bigint(20) DEFAULT NULL,
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='完单率榜单';

-- kf_grid.nsalary_rankcash_daily definition

CREATE TABLE `nsalary_rankcash_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计日期 YYYYMMDD',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '月份 YYYYMM',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员姓名',
  `grid_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `oper_fees` decimal(20,4) DEFAULT NULL COMMENT '当月累计自营现金流',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `estimated_reward` decimal(10,2) DEFAULT NULL COMMENT '预估奖励金',
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='现金流榜单';

-- kf_grid.nsalary_rankren_daily definition

CREATE TABLE `nsalary_rankren_daily` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计日期 YYYYMMDD',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '月份 YYYYMM',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作员姓名',
  `grid_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `renewal_rate` decimal(20,4) DEFAULT NULL COMMENT '续费率',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `estimated_reward` decimal(10,2) DEFAULT NULL COMMENT '标准奖励金',
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='续费率榜单';

-- kf_grid.nsalary_rank_dailydetail definition

CREATE TABLE `nsalary_rank_dailydetail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sta_day` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'YYYYMMDD',
  `month` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'YYYYMM',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL,
  `target_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '指标类型',
  `target_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `value` decimal(20,4) DEFAULT NULL,
  `rate` decimal(20,4) DEFAULT NULL,
  `point` decimal(20,2) DEFAULT NULL,
  `target_rank` bigint(20) DEFAULT NULL COMMENT '续费率排名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- kf_grid.prv_level_rate definition

CREATE TABLE `prv_level_rate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支公司',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位id',
  `rate_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系数类型，0-根据岗位级别',
  `rate_condition` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系数条件，rate_type=0时,这个字段取值为岗位等级，岗位等级配置表prv_position_level中的id',
  `position_rate` decimal(20,2) DEFAULT NULL COMMENT '岗位系数',
  `stime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效时间',
  `etime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失效时间',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='岗位系数配置表';

-- kf_grid.nsalary_prd_config definition

CREATE TABLE `nsalary_prd_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '可多选，逗号拼串，*代表所有',
  `area` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '可多选，逗号拼串，*代表所有',
  `objtype` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0 产品 1 套餐',
  `objcode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品编码/套餐编码',
  `objname` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品名称/套餐名称',
  `price` decimal(10,2) DEFAULT NULL COMMENT '单位：元',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开始生效月份，YYYYMM',
  `emonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结束生效月份，YYYYMM',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='全省续费率用户';

-- kf_grid.nsalary_monthsalary_recal definition

CREATE TABLE `nsalary_monthsalary_recal` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工所属分公司，从prv_oper_position取值，*代表所有\n',
  `areaid` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工所属业务区，从prv_oper_position取值，*代表所有\n',
  `month` bigint(20) DEFAULT NULL COMMENT 'YYYYMM\n 月份',
  `status` varchar(2) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态：\n0 未处理\n1 已处理\n',
  `remark` varchar(1024) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注\n',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- kf_grid.nsalary_monthsalary_adjustlog definition

CREATE TABLE `nsalary_monthsalary_adjustlog` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `city` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工所属分公司，从prv_oper_position取值\n',
  `areaid` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工所属业务区，从prv_oper_position取值\n',
  `operid` bigint(20) DEFAULT NULL COMMENT '操作员表的ID\n',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID\n',
  `month` bigint(20) DEFAULT NULL COMMENT '月份 YYYYMM\n',
  `adjust_wage` decimal(10,2) DEFAULT NULL COMMENT '调整金额\n',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目配置表nsalary_item_info的ID\n',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬向项目配置子表nsalary_item_detail的ID\n',
  `optype` varchar(2) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型\nI 新增\nU 更新\nD 删除\n',
  `optime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- kf_grid.nsalary_item_detail definition

CREATE TABLE `nsalary_item_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `detail_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子薪酬名称',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司,多选，*表示所有',
  `areaid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支公司，多选，*代表所有',
  `item_id` bigint(20) DEFAULT NULL COMMENT '所属薪酬项目，nsalary_item_info的ID',
  `paramjson` longtext CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '存放跑数逻辑所需的参数，json格式',
  `remark` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='薪酬项目配置子表';

-- kf_grid.tw_nsalary_prd_pkg_reward_det definition

CREATE TABLE `tw_nsalary_prd_pkg_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `bossserialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作流水号',
  `bossserialno_r` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退订业务流水号',
  `pkgtype` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品类型',
  `pkgcode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品编码/套餐编码',
  `pkgname` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品名称/套餐名称',
  `nums` bigint(20) DEFAULT NULL COMMENT '订购数量',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `paytime` datetime DEFAULT NULL COMMENT '业务操作时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务操作部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作部门中文',
  `opcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型编码',
  `opcodename` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型中文',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='套餐专项奖励用户订购退订明细表';

-- kf_grid.sta_prov_5g_user_chg definition

CREATE TABLE `sta_prov_5g_user_chg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '薪酬日期',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户分公司编码',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户业务区编码',
  `gridid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户网格编码',
  `servid` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `kpiid` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '1新增-1减少',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `telphone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号码',
  `operator` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='全省5G用户变化明细表';

-- kf_grid.tw_nsalary_prov_5gchr_reward_det definition

CREATE TABLE `tw_nsalary_prov_5gchr_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `billmon` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `bossserialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作流水号',
  `orderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `paytime` datetime DEFAULT NULL COMMENT '业务操作时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务操作部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作部门中文',
  `opcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型编码',
  `opcodename` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型中文',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `city` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `telphone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号码',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '当月出账金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='5G首次预存提成用户记录明细表';

-- kf_grid.tw_nsalary_prov_5gpre_reward_det definition

CREATE TABLE `tw_nsalary_prov_5gpre_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `bossserialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作流水号',
  `orderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `paytime` datetime DEFAULT NULL COMMENT '业务操作时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务操作部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作部门中文',
  `opcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型编码',
  `opcodename` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型中文',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `telphone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号码',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '首次预存金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='5G首次预存提成用户记录明细表';

-- kf_grid.tw_nsalary_prov_5g_reward_d1 definition

CREATE TABLE `tw_nsalary_prov_5g_reward_d1` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码（多个网格时逗号拼串）',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文（多个网格时逗号拼串）',
  `up_cnts` bigint(20) DEFAULT NULL COMMENT '当日增长户数',
  `up_price` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励单价',
  `up_reward` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励',
  `down_cnts` bigint(20) DEFAULT NULL COMMENT '当日减少户数',
  `down_price` decimal(10,2) DEFAULT NULL COMMENT '用户减少单价',
  `down_fine` decimal(10,2) DEFAULT NULL COMMENT '用户减少扣罚',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当日奖惩总金额 ',
  `kpicode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'KPICODE',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电视用户保有奖惩日汇总表';

-- kf_grid.tw_nsalary_prov_cm_reward_d1 definition

CREATE TABLE `tw_nsalary_prov_cm_reward_d1` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码（多个网格时逗号拼串）',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文（多个网格时逗号拼串）',
  `up_cnts` bigint(20) DEFAULT NULL COMMENT '当日增长户数',
  `up_price` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励单价',
  `up_reward` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励',
  `down_cnts` bigint(20) DEFAULT NULL COMMENT '当日减少户数',
  `down_price` decimal(10,2) DEFAULT NULL COMMENT '用户减少单价',
  `down_fine` decimal(10,2) DEFAULT NULL COMMENT '用户减少扣罚',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当日奖惩总金额 ',
  `kpicode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'KPICODE',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电视用户保有奖惩日汇总表';

-- kf_grid.tw_nsalary_prov_digit_reward_d1 definition

CREATE TABLE `tw_nsalary_prov_digit_reward_d1` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码（多个网格时逗号拼串）',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文（多个网格时逗号拼串）',
  `up_cnts` bigint(20) DEFAULT NULL COMMENT '当日增长户数',
  `up_price` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励单价',
  `up_reward` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励',
  `down_cnts` bigint(20) DEFAULT NULL COMMENT '当日减少户数',
  `down_price` decimal(10,2) DEFAULT NULL COMMENT '用户减少单价',
  `down_fine` decimal(10,2) DEFAULT NULL COMMENT '用户减少扣罚',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当日奖惩总金额 ',
  `kpicode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'KPICODE',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电视用户保有奖惩日汇总表';


-- kf_grid.tw_nsalary_renewal_fine_det definition

CREATE TABLE `tw_nsalary_renewal_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` bigint(20) DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `cust_patchid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `cust_patchname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `logicdevno` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '卡号',
  `linkaddr` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地址',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='当期应续费明细表';

-- kf_grid.tw_nsalary_market_sec_fine_det definition

CREATE TABLE `tw_nsalary_market_sec_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `cust_patchid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户网格编码',
  `cust_patchname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户网格名称',
  `main_orderno` bigint(20) DEFAULT NULL COMMENT '主工单编号',
  `main_finish_operator` bigint(20) DEFAULT NULL COMMENT '主工单完单人ID',
  `main_operatorname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主工单完单人姓名',
  `main_finish_time` datetime DEFAULT NULL COMMENT '主工单完单时间',
  `main_action_result` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主工单回单结果',
  `sec_orderno` bigint(20) DEFAULT NULL COMMENT '子工单编号',
  `sec_finish_operator` bigint(20) DEFAULT NULL COMMENT '子工单完单人ID',
  `sec_operatorname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子工单完单人姓名',
  `sec_finish_time` datetime DEFAULT NULL COMMENT '子工单完单时间',
  `serialno` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收费单业务流水号',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费单收费金额',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='二次营销单扣罚明细表';


-- kf_grid.tw_nsalary_market_out_fine_det definition

CREATE TABLE `tw_nsalary_market_out_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` bigint(20) DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `orderid` bigint(20) DEFAULT NULL COMMENT '工单编号',
  `transfer_optime` datetime DEFAULT NULL COMMENT '工单超时转派时间',
  `gridid` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单网格ID',
  `gridname` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单网格中文',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '实得扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='营销单超时扣罚明细表';

-- kf_grid.tw_nsalary_eybojin_fine_det definition

CREATE TABLE `tw_nsalary_eybojin_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `refund_serialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退订流水号',
  `salescode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐编码',
  `salesname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐名称',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `paytime` datetime DEFAULT NULL COMMENT '业务收费时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务收费部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务收费部门中文',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  `rewarddate` datetime DEFAULT NULL COMMENT '奖励日期',
  `rewardorderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '奖励订单号',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼Tv铂金会员扣罚用户明细表';


-- kf_grid.tw_nsalary_eyhuangjin_fine_det definition

CREATE TABLE `tw_nsalary_eyhuangjin_fine_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `refund_serialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退订流水号',
  `salescode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐编码',
  `salesname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐名称',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `paytime` datetime DEFAULT NULL COMMENT '业务收费时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务收费部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务收费部门中文',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  `rewarddate` datetime DEFAULT NULL COMMENT '奖励日期',
  `rewardorderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '奖励订单号',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼Tv黄金会员扣罚用户明细表';


-- kf_grid.tw_nsalary_eybojin_reward_det definition

CREATE TABLE `tw_nsalary_eybojin_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `bossserialno` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务流水号',
  `orderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `salescode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐编码',
  `salesname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐名称',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `paytime` datetime DEFAULT NULL COMMENT '业务收费时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务收费部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务收费部门中文',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼Tv铂金会员奖励用户明细表';


-- kf_grid.tw_nsalary_eyhuangjin_reward_det definition

CREATE TABLE `tw_nsalary_eyhuangjin_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `bossserialno` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务流水号',
  `orderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `salescode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐编码',
  `salesname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '套餐名称',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `paytime` datetime DEFAULT NULL COMMENT '业务收费时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务收费部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务收费部门中文',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼Tv黄金会员奖励用户明细表';

-- kf_grid.tw_nsalary_installorder_reward_det definition

CREATE TABLE `tw_nsalary_installorder_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `install_orderid` bigint(20) DEFAULT NULL COMMENT '工单编号',
  `backtime` datetime DEFAULT NULL COMMENT '工单回单时间',
  `backoperid` bigint(20) DEFAULT NULL COMMENT '工单回单操作员ID',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '实得薪酬',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='安装工单用户明细表';

-- kf_grid.tw_nsalary_sv_maintain_reward_det definition

CREATE TABLE `tw_nsalary_sv_maintain_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `custid` bigint(20) DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `install_orderid` bigint(20) DEFAULT NULL COMMENT '工单编号',
  `backtime` datetime DEFAULT NULL COMMENT '工单回单时间',
  `backoperid` bigint(20) DEFAULT NULL COMMENT '工单回单操作员ID',
  `backopername` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单回单操作员姓名',
  `nettype` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单网络类型',
  `faulttype` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单故障类型',
  `chargefaulttype` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单计费故障类型',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '实得奖励金额',
  `gridid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单所属网格ID',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '维修单所属网格中文',
  `managerid` bigint(20) DEFAULT NULL COMMENT '网格经理',
  `managername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理中文',
  `transfertype` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转单类型',
  `super_rate` decimal(18,2) DEFAULT NULL COMMENT '超时转单比例',
  `award_rate` decimal(18,2) DEFAULT NULL COMMENT '非工作时间奖励系数',
  `reward_fine` decimal(18,2) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导维修单明细表';

-- kf_grid.tw_nsalary_prov_region_reward_det definition

CREATE TABLE `tw_nsalary_prov_region_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `bossserialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作流水号',
  `orderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `paytime` datetime DEFAULT NULL COMMENT '业务操作时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务操作部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作部门中文',
  `opcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型编码',
  `opcodename` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型中文',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '其他渠道收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '区域现金流提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='其他渠道收入提成用户记录明细表';


-- kf_grid.tw_nsalary_prov_self_reward_det definition

CREATE TABLE `tw_nsalary_prov_self_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `bossserialno` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作流水号',
  `orderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `paytime` datetime DEFAULT NULL COMMENT '业务操作时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务操作部门ID',
  `deptname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作部门中文',
  `opcode` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型编码',
  `opcodename` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型中文',
  `custid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '业务收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '自营现金流提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='全省统一自营收入提成收入记录明细表';

-- kf_grid.tw_nsalary_sv_market_reward_det definition

CREATE TABLE `tw_nsalary_sv_market_reward_det` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `bizoperid` bigint(20) DEFAULT NULL COMMENT '业务操作员ID',
  `opername` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作员姓名',
  `orderid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `bossserialno` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `paytime` datetime DEFAULT NULL COMMENT '业务操作时间',
  `deptid` bigint(20) DEFAULT NULL COMMENT '业务操作部门ID',
  `deptname` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作部门中文',
  `opcode` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型编码',
  `opcodename` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务操作类型中文',
  `custid` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户编号',
  `custname` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户姓名',
  `city` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码',
  `gridname` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文',
  `rate_type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收费类型（普通收费单、督导营销单）',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '业务收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `bizfee_byself_rate` decimal(10,4) DEFAULT NULL COMMENT '自营收入奖励比例',
  `monitor_rate` decimal(10,4) DEFAULT NULL COMMENT '督导单奖励比例',
  `award_rate` decimal(10,4) DEFAULT NULL COMMENT '特殊工作时段奖励比例',
  `orderno` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_subtype_name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `position_rate` decimal(10,4) DEFAULT NULL COMMENT '岗位系数',
  `level_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '岗位等级中文',
  `position_level_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位等级编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导营销奖励明细';

-- kf_grid.tw_nsalary_prd_pkg_reward_d definition

CREATE TABLE `tw_nsalary_prd_pkg_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='套餐专项奖励日汇总表';

-- kf_grid.tw_nsalary_prd_pkg_reward_m definition

CREATE TABLE `tw_nsalary_prd_pkg_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='套餐专项奖励月汇总表';

-- kf_grid.tw_nsalary_prov_5gchr_reward_m definition

CREATE TABLE `tw_nsalary_prov_5gchr_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `billmon` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '当月出账金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='5G首次预存提成月汇总表';

-- kf_grid.tw_nsalary_prov_5gpre_reward_m definition

CREATE TABLE `tw_nsalary_prov_5gpre_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '首次预存金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='5G首次预存提成月汇总表';

-- kf_grid.tw_nsalary_prov_5gpre_reward_d definition

CREATE TABLE `tw_nsalary_prov_5gpre_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '首次预存金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='5G首次预存提成日汇总表';

-- kf_grid.tw_nsalary_prov_5g_reward_d2 definition

CREATE TABLE `tw_nsalary_prov_5g_reward_d2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码（多个网格时逗号拼串）',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文（多个网格时逗号拼串）',
  `up_cnts` bigint(20) DEFAULT NULL COMMENT '当日增长户数',
  `up_price` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励单价',
  `up_reward` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励',
  `down_cnts` bigint(20) DEFAULT NULL COMMENT '当日减少户数',
  `down_price` decimal(10,2) DEFAULT NULL COMMENT '用户减少单价',
  `down_fine` decimal(10,2) DEFAULT NULL COMMENT '用户减少扣罚',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当日扣罚总金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电视用户保有奖惩日汇总表2';

-- kf_grid.tw_nsalary_prov_cm_reward_d2 definition

CREATE TABLE `tw_nsalary_prov_cm_reward_d2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码（多个网格时逗号拼串）',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文（多个网格时逗号拼串）',
  `up_cnts` bigint(20) DEFAULT NULL COMMENT '当日增长户数',
  `up_price` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励单价',
  `up_reward` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励',
  `down_cnts` bigint(20) DEFAULT NULL COMMENT '当日减少户数',
  `down_price` decimal(10,2) DEFAULT NULL COMMENT '用户减少单价',
  `down_fine` decimal(10,2) DEFAULT NULL COMMENT '用户减少扣罚',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当日扣罚总金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电视用户保有奖惩日汇总表2';

-- kf_grid.tw_nsalary_prov_digit_reward_d2 definition

CREATE TABLE `tw_nsalary_prov_digit_reward_d2` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码',
  `cityname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司编码中文',
  `areaid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区编码',
  `areaname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务区中文',
  `gridcode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格编码（多个网格时逗号拼串）',
  `gridname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格中文（多个网格时逗号拼串）',
  `up_cnts` bigint(20) DEFAULT NULL COMMENT '当日增长户数',
  `up_price` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励单价',
  `up_reward` decimal(10,2) DEFAULT NULL COMMENT '用户增长奖励',
  `down_cnts` bigint(20) DEFAULT NULL COMMENT '当日减少户数',
  `down_price` decimal(10,2) DEFAULT NULL COMMENT '用户减少单价',
  `down_fine` decimal(10,2) DEFAULT NULL COMMENT '用户减少扣罚',
  `total_fine` decimal(10,2) DEFAULT NULL COMMENT '当日扣罚总金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='电视用户保有奖惩日汇总表2';


-- kf_grid.tw_nsalary_renewal_fine_m definition

CREATE TABLE `tw_nsalary_renewal_fine_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `dyrenewal_nums` bigint(20) DEFAULT NULL COMMENT '当期已续费客户',
  `dyonline_nums` bigint(20) DEFAULT NULL COMMENT '当期应续费客户',
  `dynot_nums` bigint(20) DEFAULT NULL COMMENT '当期未续费客户',
  `renewal_rate` decimal(10,4) DEFAULT NULL COMMENT '当期续费率',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '扣罚单价',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='当期应续费汇总表';


-- kf_grid.tw_nsalary_market_sec_fine_d definition

CREATE TABLE `tw_nsalary_market_sec_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` bigint(20) DEFAULT NULL COMMENT '工单笔数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='二次营销单扣罚日表';

-- kf_grid.tw_nsalary_market_sec_fine_m definition

CREATE TABLE `tw_nsalary_market_sec_fine_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` bigint(20) DEFAULT NULL COMMENT '工单笔数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='二次营销单扣罚月表';

-- kf_grid.tw_nsalary_market_trans_fine_d definition

CREATE TABLE `tw_nsalary_market_trans_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `trans_cnt` bigint(20) DEFAULT NULL COMMENT '营销单超时单数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='营销单转单扣罚日汇总表';

-- kf_grid.tw_nsalary_market_out_fine_d definition

CREATE TABLE `tw_nsalary_market_out_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` bigint(20) DEFAULT NULL COMMENT '营销单超时单数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  `etl_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='营销单超时扣罚日汇总表';

-- kf_grid.tw_nsalary_eybojin_fine_d definition

CREATE TABLE `tw_nsalary_eybojin_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fine_cnts` bigint(20) DEFAULT NULL COMMENT '铂金会员扣罚户数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼TV铂金会员扣罚日汇总表';


-- kf_grid.tw_nsalary_eybojin_fine_m definition

CREATE TABLE `tw_nsalary_eybojin_fine_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fine_cnts` bigint(20) DEFAULT NULL COMMENT '铂金会员扣罚户数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼TV铂金会员扣罚月汇总表';

-- kf_grid.tw_nsalary_eyhuangjin_fine_d definition

CREATE TABLE `tw_nsalary_eyhuangjin_fine_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fine_cnts` bigint(20) DEFAULT NULL COMMENT '黄金会员扣罚户数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼TV黄金会员扣罚日汇总表';


-- kf_grid.tw_nsalary_eyhuangjin_fine_m definition

CREATE TABLE `tw_nsalary_eyhuangjin_fine_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fine_cnts` bigint(20) DEFAULT NULL COMMENT '黄金会员扣罚户数',
  `fine` decimal(10,2) DEFAULT NULL COMMENT '扣罚金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼TV黄金会员扣罚月汇总表';


-- kf_grid.tw_nsalary_prov_rankcash_reward definition

CREATE TABLE `tw_nsalary_prov_rankcash_reward` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计日期 YYYYMMDD',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '网格经理id',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理名称',
  `gridcode` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `fees` decimal(20,4) DEFAULT NULL COMMENT '当月累计自营现金流',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `prov_rank` bigint(20) DEFAULT NULL COMMENT '全省排名',
  `standard_reward` decimal(10,2) DEFAULT NULL COMMENT '标准奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='全省统一现金流排名奖月表';


-- kf_grid.tw_nsalary_prov_rankren_reward definition

CREATE TABLE `tw_nsalary_prov_rankren_reward` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统计日期 YYYYMMDD',
  `smonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '员工岗位配置表prv_position_info的ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '网格经理id',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `opername` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格经理名称',
  `gridcode` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格id',
  `gridname` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网格名称',
  `renewal_rate` decimal(20,4) DEFAULT NULL COMMENT '续费率',
  `areaid_rank` bigint(20) DEFAULT NULL COMMENT '支公司排名',
  `city_rank` bigint(20) DEFAULT NULL COMMENT '分公司排名',
  `prov_rank` bigint(20) DEFAULT NULL COMMENT '全省排名',
  `standard_reward` decimal(10,2) DEFAULT NULL COMMENT '标准奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='全省统一续费率排名奖月表';


-- kf_grid.tw_nsalary_monthlyrank_reward definition

CREATE TABLE `tw_nsalary_monthlyrank_reward` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `ranking` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '排名',
  `points` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分值',
  `standard_reward` decimal(10,2) DEFAULT NULL COMMENT '标准奖励金',
  `list_reward` decimal(10,2) DEFAULT NULL COMMENT '排行榜奖励金',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='月排名奖汇总表';

-- kf_grid.tw_nsalary_eybojin_reward_d definition

CREATE TABLE `tw_nsalary_eybojin_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `new_cnts` bigint(20) DEFAULT NULL COMMENT '铂金会员新开户数',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼TV铂金会员奖励日汇总表';

-- kf_grid.tw_nsalary_eybojin_reward_m definition

CREATE TABLE `tw_nsalary_eybojin_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `new_cnts` bigint(20) DEFAULT NULL COMMENT '铂金会员新开户数',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼TV铂金会员奖励月汇总表';


-- kf_grid.tw_nsalary_eyhuangjin_reward_d definition

CREATE TABLE `tw_nsalary_eyhuangjin_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `new_cnts` bigint(20) DEFAULT NULL COMMENT '黄金会员新开户数',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼TV黄金会员奖励日汇总表';

-- kf_grid.tw_nsalary_eyhuangjin_reward_m definition

CREATE TABLE `tw_nsalary_eyhuangjin_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `new_cnts` bigint(20) DEFAULT NULL COMMENT '黄金会员新开户数',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='鳄鱼TV黄金会员奖励月汇总表';

-- kf_grid.tw_nsalary_installorder_reward_d definition

CREATE TABLE `tw_nsalary_installorder_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `ordernum` bigint(20) DEFAULT NULL COMMENT '工单个数',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '实得薪酬',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='安装工单日汇总明细表';

-- kf_grid.tw_nsalary_installorder_reward_m definition

CREATE TABLE `tw_nsalary_installorder_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `ordernum` bigint(20) DEFAULT NULL COMMENT '工单个数',
  `unit_price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '实得薪酬',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='安装工单月汇总明细表';

-- kf_grid.tw_nsalary_sv_maintain_reward_d definition

CREATE TABLE `tw_nsalary_sv_maintain_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` bigint(10) DEFAULT NULL COMMENT '维修工单个数',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导维修单日汇总表';

-- kf_grid.tw_nsalary_sv_maintain_reward_m definition

CREATE TABLE `tw_nsalary_sv_maintain_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '统计月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `order_cnts` bigint(10) DEFAULT NULL COMMENT '维修工单个数',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导维修单月汇总表';


-- kf_grid.tw_nsalary_prov_region_reward_d definition

CREATE TABLE `tw_nsalary_prov_region_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '其他渠道收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '区域现金流提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='其他渠道收入提成日汇总表';

-- kf_grid.tw_nsalary_prov_region_reward_m definition

CREATE TABLE `tw_nsalary_prov_region_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '其他渠道收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='其他渠道收入提成月汇总表';

-- kf_grid.tw_nsalary_prov_self_reward_d definition

CREATE TABLE `tw_nsalary_prov_self_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '业务收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `reward_rate` decimal(10,4) DEFAULT NULL COMMENT '自营现金流提成比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='全省统一自营收入提成日汇总明细表';

-- kf_grid.tw_nsalary_prov_self_reward_m definition

CREATE TABLE `tw_nsalary_prov_self_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '业务收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='全省统一自营收入提成月汇总明细表';

-- kf_grid.tw_nsalary_sv_market_reward_d definition

CREATE TABLE `tw_nsalary_sv_market_reward_d` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  `position_rate` decimal(10,4) DEFAULT NULL COMMENT '岗位系数',
  `level_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '岗位等级中文',
  `position_level_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位等级编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导营销奖励日汇总';

-- kf_grid.tw_nsalary_base_wage definition

CREATE TABLE `tw_nsalary_base_wage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `base_wage` decimal(10,2) DEFAULT NULL COMMENT '基薪金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='基薪';


-- kf_grid.tw_nsalary_allowance definition

CREATE TABLE `tw_nsalary_allowance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `allowance` decimal(10,2) DEFAULT NULL COMMENT '岗位补贴金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='岗位补贴';



-- kf_grid.tw_nsalary_sv_market_reward_m definition

CREATE TABLE `tw_nsalary_sv_market_reward_m` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stadate` bigint(20) DEFAULT NULL COMMENT '统计日期',
  `smonth` bigint(20) DEFAULT NULL COMMENT '薪酬月份',
  `position_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `operid` bigint(20) DEFAULT NULL COMMENT '薪酬结算操作员ID',
  `item_id` bigint(20) DEFAULT NULL COMMENT '薪酬项目ID',
  `item_detail_id` bigint(20) DEFAULT NULL COMMENT '薪酬子项目ID',
  `fees` decimal(10,2) DEFAULT NULL COMMENT '收费金额',
  `reward` decimal(10,2) DEFAULT NULL COMMENT '奖励金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='督导营销奖励月汇总';




select * from prv_sysparam ps where gcode ='SYS_NSALSRY_TYPE'

INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'SYS_NSALSRY_TYPE', 'A', '岗位得分', NULL, NULL, 3, NULL, NULL, NULL);
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'SYS_NSALSRY_TYPE', 'B', '收入得分(固网)', NULL, NULL, 4, NULL, NULL, NULL);
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'SYS_NSALSRY_TYPE', 'C', '装维得分', NULL, NULL, 5, NULL, NULL, NULL);
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'SYS_NSALSRY_TYPE', 'D', '其他奖励', NULL, NULL, 6, NULL, NULL, NULL);
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'SYS_NSALSRY_TYPE', 'E', '扣减', NULL, NULL, 7, NULL, NULL, NULL);
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'SYS_NSALSRY_TYPE', 'F', '人工调整', NULL, NULL, 8, NULL, NULL, NULL);
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'SYS_NSALSRY_TYPE', 'G', '收入得分(5G)', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'SYS_NSALSRY_TYPE', 'H', '营销奖励', NULL, NULL, 2, NULL, NULL, NULL);

select * from nsalary_item_info

INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(5, 'A', '基本得分', '基本得分', 'A001', '0', 'Y', '{
	"base_wage":{
	"name":"基薪（元）",
	"value":"1750"
}
}
', NULL, 0, '2020-10-18 22:24:46', 0, '2024-07-03 15:53:14', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(6, 'A', '岗位补贴得分', '岗位补贴得分', 'A002', '0', 'Y', '{
	"post_subsidy":{
	"name":"岗位补贴（元）",
	"value":""
}
}

', NULL, 0, '2020-10-18 22:34:29', 0, '2024-07-03 15:53:14', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(7, 'A', '维护薪酬', '维护得分', 'A003', '0', 'N', '{
	"mainserv_price":{
	"name":"在用主终端单价（元/户）",
	"value":""
}
}
', NULL, 0, '2020-10-18 22:37:06', 0, '2024-07-03 15:51:59', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(8, 'B', '督导营销得分', '督导营销得分', 'B001', '0', 'N', '{
	"bizfee_byself_rate":{
	"name":" 自营奖励比例",
	"value":""
},
"monitor_rate":{
	"name":" 督导单奖励比例",
	"value":""
},
"bizfee_huigui_rate":{
	"name":" 离网回归工单奖励比例",
	"value":""
}
}', NULL, 0, '2020-10-18 22:48:56', 0, '2024-07-03 15:53:14', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(9, 'B', '收入得分(自营)', '收入得分(自营)', 'B002', '0', 'N', '{
	"bizfee_byself_rate":{
	"name":" 自营奖励比例",
	"value":""
},
"bizfee_grid_rate":{
	"name":" 区域奖励比例",
	"value":""
},
"bizfee_huigui_rate":{
	"name":" 离网回归工单奖励比例",
	"value":""
}
}
', NULL, 0, '2020-10-18 23:05:08', 0, '2024-07-03 15:55:14', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(10, 'D', '鳄鱼TV黄金会员奖励', '鳄鱼TV黄金会员奖励', 'D001', '0', 'N', '{
	"price":{
	"name":"单价（元/户）",
	"value":""
}
}
', NULL, 0, '2020-10-18 23:45:57', 0, '2020-11-05 16:21:18', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(11, 'D', '鳄鱼TV铂金会员奖励', '鳄鱼TV铂金会员奖励', 'D002', '0', 'N', '{
	"price":{
	"name":"单价（元/户）",
	"value":""
}
}
', NULL, 0, '2020-10-18 23:52:06', 0, '2020-11-05 16:21:19', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(12, 'C', '督导维修单奖励', '有偿装维收入', 'C001', '0', 'N', '{
	“super_rate":{
	“name":"强制转单奖励比例",
	“value":""
}
}

 ', NULL, 0, '2020-10-18 23:53:24', 0, '2020-12-18 14:24:38', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(13, 'C', '安装得分', '安装得分', 'C002', '0', 'N', '{
	"installorder_price":{
	"name":"安装单价格（元/单）",
	"value":""
}
}
', NULL, 0, '2020-10-18 23:54:28', 0, '2024-07-03 15:53:14', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(14, 'D', '月排名奖', '月排名奖', 'D003', '0', 'N', '"xf_rate":{
	"name":"续费率百分比",
	"value":"0.3"
},
"zx_rate":{
	"name":"在线率百分比",
	"value":"0.3"
},
"xx_rate":{
	"name":"信息收集率百分比",
	"value":"0.2"
},
"xy_rate":{
	"name":" CMP工单营销成功率百分比",
	"value":"0.2"
},
"reward_amount1":{
	"name":" 第一名奖励金额",
	"value":"1000"
},
"reward_amount2":{
	"name":" 第二名奖励金额",
	"value":"800"
},
"reward_amount3":{
	"name":" 第三名奖励金额",
	"value":"600"
},
"reward_amount4":{
	"name":" 第四名奖励金额",
	"value":"400"
}

 ', NULL, 0, '2020-10-18 23:55:56', 0, '2020-11-05 18:07:06', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(15, 'E', '鳄鱼TV黄金会员扣罚', '鳄鱼TV黄金会员扣罚', 'E001', '0', 'N', '', NULL, 0, '2020-10-18 23:57:23', 0, '2020-11-05 16:21:24', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(16, 'E', '鳄鱼TV铂金会员扣罚', '鳄鱼TV铂金会员扣罚', 'E002', '0', 'N', '', NULL, 0, '2020-10-18 23:57:58', 0, '2020-11-05 16:21:25', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(17, 'E', '退费扣罚', '退费扣罚', 'E003', '0', 'N', '{
	"rate":{
	"name":" 退费扣罚比例",
	"value":""
}
}
', NULL, 0, '2020-10-19 00:08:00', 0, '2020-11-05 16:21:27', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(18, 'E', '在线户数扣罚', '在线户数奖惩', 'E004', '0', 'N', '{
	"up_price":{
	"name":" 用户减少奖励金额（元/户）",
	"value":""
},
"down_price":{
	"name":" 用户增长奖励金额（元/户）",
	"value":""
}
}

', NULL, 0, '2020-10-19 00:20:25', 0, '2020-12-18 14:24:46', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(19, 'E', '督办单扣罚', '有偿装维支出', 'E005', '0', 'N', '', NULL, 0, '2020-10-19 00:22:14', 0, '2020-12-18 14:24:35', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(21, 'B', '收入得分(其他渠道)', '收入得分(其他渠道)', 'B003', '0', 'N', '{
“bizfee_grid_rate":{
	“name":" 区域奖励比例",
	“value":""
}
}
', NULL, 0, '2020-10-27 14:10:33', 0, '2024-07-03 15:55:21', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(22, 'F', '月度奖励1', '月度奖励1', 'F001', '1', 'Y', '', NULL, 0, '2020-10-29 15:43:48', 0, '2020-11-09 16:14:01', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(23, 'F', '月度奖励2', '月度奖励2', 'F002', '1', 'Y', '', NULL, 0, '2020-10-29 15:43:48', 0, '2020-11-09 16:14:03', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(24, 'F', '月度奖励3', '月度奖励3', 'F003', '1', 'Y', '', NULL, 0, '2020-10-29 15:43:48', 0, '2020-11-09 16:14:06', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(25, 'F', '月度扣罚4', '月度扣罚4', 'F004', '1', 'Y', '', NULL, 0, '2020-10-29 15:43:48', 0, '2020-11-09 16:13:42', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(26, 'F', '月度扣罚5', '月度扣罚5', 'F005', '1', 'Y', '', NULL, 0, '2020-10-29 15:43:48', 0, '2020-11-09 16:13:44', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(27, 'D', '续费率排名奖励', '续费率排名奖励', 'D004', '0', 'N', '{"reward_amount1":{"name":"第一名奖励金额","value":"500"},"reward_amount2":{"name":"第二名奖励金额","value":"300"},"reward_amount3":{"name":"第三名奖励金额","value":"200"}}', NULL, 0, '2020-12-17 11:12:14', 0, '2024-07-03 15:55:26', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(28, 'D', '现金流排名奖励', '现金流排名奖励', 'D005', '0', 'N', '{"reward_amount1":{"name":"第一名奖励金额","value":"500"},"reward_amount2":{"name":"第二名奖励金额","value":"300"},"reward_amount3":{"name":"第三名奖励金额","value":"200"}}', NULL, 0, '2020-12-17 11:12:14', 0, '2024-07-03 15:55:27', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(29, 'E', '网格经理营销单超时扣罚', '营销单超时扣罚', 'E006', '0', 'N', '{"price":{"name":"营销单超时扣罚单价（元/单）","value":"5"}}', NULL, NULL, '2020-12-24 20:06:14', NULL, '2023-02-14 21:57:43', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(30, 'E', '网格经理转单扣罚', '网格经理转单扣罚', 'E007', '0', 'N', '{"price":{"name":"转单扣罚单价（元/单）","value":"5"}}', NULL, NULL, '2021-02-26 14:08:01', NULL, '2021-02-26 14:08:01', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(31, 'D', '完单率排名奖励', '完单率排名奖励', 'D006', '0', 'N', '{"reward_amount1":{"name":"第一名奖励金额","value":"0"},"reward_amount2":{"name":"第二名奖励金额","value":"0"},"reward_amount3":{"name":"第三名奖励金额","value":"0"}}', NULL, 0, '2021-03-04 11:37:05', 0, '2021-03-05 15:13:37', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(32, 'E', '超时限未完单扣罚', '超时限未完单扣罚', 'E008', '0', 'N', '{"price":{"name":"工单超时扣罚单价（元/单）","value":"5"}}', NULL, NULL, '2021-03-05 14:53:22', NULL, '2021-03-05 14:54:25', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(33, 'E', '二次营销扣罚', '二次营销扣罚', 'E009', '0', 'N', '{}', NULL, NULL, '2021-04-22 16:27:36', NULL, '2021-04-22 16:27:36', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(34, 'B', '电话营销成功奖励', '电话营销成功奖励', 'B004', '0', 'N', '{
"rate":{
	"name":"电话营销成功奖励比例",
	"value":"0.02"
},
"huigui_rate":{
	"name":"离网回归电话营销成功奖励比例",
	"value":"0.04"
}', NULL, 0, '2021-04-23 14:10:33', 0, '2021-04-23 16:21:32', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(35, 'D', '勤劳小蜜蜂排名奖励', '勤劳小蜜蜂排名奖励', 'D007', '0', 'N', '{}', NULL, NULL, '2021-11-02 09:02:49', NULL, '2021-11-02 09:02:49', NULL);
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(36, 'E', '当期续费奖罚', '当期续费奖罚', 'E010', '0', 'N', '{
"down_price":{
	"name":" 扣罚单价（元/户）",
	"value":"-25"
},
"up_price":{
	"name":" 奖励单价（元/户）",
	"value":"0"
},
"line_rate":"0.7"
}', NULL, NULL, '2021-11-02 09:07:25', NULL, '2021-11-02 09:07:25', NULL);
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(37, 'B', '全省统一收入得分（自营）', '全省统一收入得分（自营）', 'B005', '0', 'N', '{"bizfee_byself_rate":{"name":"自营收入提成比例","value":""}}', NULL, NULL, '2023-02-14 22:04:05', NULL, '2024-07-03 15:53:57', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(38, 'B', '全省统一收入得分（自营）', '全省统一收入得分（自营）', 'B006', '0', 'N', '{"bizfee_grid_rate":{"name":"其它渠道收入提成比例","value":""}}', NULL, NULL, '2023-02-14 22:10:21', NULL, '2024-07-03 15:53:57', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(39, 'D', '全省统一续费率排名奖励', '续费率排名奖励', 'D008', '0', 'N', '{"reward_amount1":{"name":" 第一名奖励金额","value":""},"reward_amount2":{"name":" 第二名奖励金额","value":""},"reward_amount3":{"name":" 第三名奖励金额","value":""}}', NULL, NULL, '2023-02-14 22:12:53', NULL, '2023-02-14 22:24:40', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(40, 'D', '全省统一现金流排名奖励', '现金流排名奖励', 'D009', '0', 'N', '{"reward_amount1":{"name":" 第一名奖励金额","value":""},"reward_amount2":{"name":" 第二名奖励金额","value":""},"reward_amount3":{"name":" 第三名奖励金额","value":""}}', NULL, NULL, '2023-02-14 22:16:08', NULL, '2023-02-14 22:24:39', 'M');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(41, 'E', '全省统一电视用户保有奖惩', '电视用户保有奖惩', 'E011', '0', 'N', '{"up_price":{"name":" 用户增长奖励金额（元/户）",	"value":""},"down_price":{"name":" 用户减少奖励金额（元/户）","value":""}}', NULL, NULL, '2023-02-14 22:18:17', NULL, '2023-02-14 22:18:17', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(42, 'E', '全省统一宽带保有奖惩', '宽带保有奖惩', 'E012', '0', 'N', '{"up_price":{"name":" 用户增长奖励金额（元/户）",	"value":""},"down_price":{"name":" 用户减少奖励金额（元/户）","value":""}}', NULL, NULL, '2023-02-14 22:19:24', NULL, '2023-02-14 22:19:24', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(43, 'E', '全省统一5G用户保有奖惩', '5G用户保有奖惩', 'E013', '0', 'N', '{"up_price":{"name":" 用户增长奖励金额（元/户）",	"value":""},"down_price":{"name":" 用户减少奖励金额（元/户）","value":""}}', NULL, NULL, '2023-02-14 22:19:56', NULL, '2023-02-14 22:19:56', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(44, 'G', '5G首次预存得分', '5G首次预存得分', 'G001', '0', 'N', '{"5gnew_rate":{"name":"5G首次预存提成比例","value":""}}', NULL, NULL, '2023-02-14 22:22:05', NULL, '2024-07-03 15:54:19', 'D');
INSERT INTO nsalary_item_info (id, item_type, item_name, item_showname, item_code, source, edit_flag, paramjson, remark, create_by, create_at, update_by, update_at, sta_flag) VALUES(45, 'G', '5G月出账收入得分', '5G月出账收入得分', 'G002', '0', 'N', '{"5g_month_rate":{"name":"5G月出账提成比例","value":""}}', NULL, NULL, '2023-02-14 22:23:40', NULL, '2024-07-03 15:54:19', 'M');


