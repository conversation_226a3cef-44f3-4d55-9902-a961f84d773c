-- 是否在即将到期用户查询中展示字段
ALTER TABLE prv_menudef ADD `toshow` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL COMMENT '是否在即将到期用户查询中展示，有值就展示，排序填入数字';


-- 是否在即将到期用户查询中展示
UPDATE prv_menudef SET premenuid=11170031, name='固网开户', toshow='1' WHERE menuid=11170045;
UPDATE prv_menudef SET premenuid=11170031, name='产品订购',  toshow='2' WHERE menuid=11170080;
UPDATE prv_menudef SET premenuid=11170031, name='固网充值', toshow='3' WHERE menuid=11170108;
UPDATE prv_menudef SET premenuid=11170031, name='缴欠费', toshow='4' WHERE menuid=11170115;


-- 跟进结果类型
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'FOLLOW_TRACK', '0', '其他渠道已办理', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'FOLLOW_TRACK', '1', '未联系到客户', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'FOLLOW_TRACK', '2', '网格已办理', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'FOLLOW_TRACK', '3', '用户有意向', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'FOLLOW_TRACK', '4', '用户已拒绝', NULL, NULL, NULL, NULL, NULL, NULL);


-- 添加通用业务类型
INSERT INTO prv_sysparam (paramid, gcode, mcode, mname, `data`, fmt, sort, `scope`, `type`, memo) VALUES(NULL, 'GW_ACCT_BUSINESSTYPE', '100', '通用业务', NULL, NULL, 3, NULL, NULL, NULL);
