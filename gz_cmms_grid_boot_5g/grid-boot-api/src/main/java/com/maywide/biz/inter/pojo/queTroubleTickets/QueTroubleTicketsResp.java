package com.maywide.biz.inter.pojo.queTroubleTickets;

import com.maywide.biz.survey.entity.BizPhotoList;

import java.util.List;

/**
 * @Author: zhouzhixiang
 * @Date: 2021/10/18
 */
public class QueTroubleTicketsResp {
    private Long ticketId;
    private String areaName;//        '地址区域id',      app传
    private String peopleName;//    '上报人名称',  不用传
    private String peopleTime;//    '上报时间',    不用传
    private String faultAddress;//  '故障地址',    app传
    private String faultRemark;//   '故障说明',    app传
    private String faultReason;//   '故障原因',    app传
    private String faultTerminal;// '故障终端',    app传
    private String imgUrl;//        '现场图片',    app传
    private String informantGrid;// '地址填报网格',    app传
    private String informant;//      '填报人',      app传
    private String repairImgUrl; //修复后图片
    private Long custid;//定位客户id




    private  List<BizPhotoList> bizPhotoList;

    private  List<BizPhotoList> repairImgList;


    public Long getCustid() {
        return custid;
    }

    public void setCustid(Long custid) {
        this.custid = custid;
    }

    public List<BizPhotoList> getRepairImgList() {
        return repairImgList;
    }

    public void setRepairImgList(List<BizPhotoList> repairImgList) {
        this.repairImgList = repairImgList;
    }

    public String getRepairImgUrl() {
        return repairImgUrl;
    }

    public void setRepairImgUrl(String repairImgUrl) {
        this.repairImgUrl = repairImgUrl;
    }

    public Long getTicketId() {
        return ticketId;
    }

    public void setTicketId(Long ticketId) {
        this.ticketId = ticketId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getPeopleName() {
        return peopleName;
    }

    public void setPeopleName(String peopleName) {
        this.peopleName = peopleName;
    }

    public String getPeopleTime() {
        return peopleTime;
    }

    public void setPeopleTime(String peopleTime) {
        this.peopleTime = peopleTime;
    }

    public String getFaultAddress() {
        return faultAddress;
    }

    public void setFaultAddress(String faultAddress) {
        this.faultAddress = faultAddress;
    }

    public String getFaultRemark() {
        return faultRemark;
    }

    public void setFaultRemark(String faultRemark) {
        this.faultRemark = faultRemark;
    }

    public String getFaultReason() {
        return faultReason;
    }

    public void setFaultReason(String faultReason) {
        this.faultReason = faultReason;
    }

    public String getFaultTerminal() {
        return faultTerminal;
    }

    public void setFaultTerminal(String faultTerminal) {
        this.faultTerminal = faultTerminal;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getInformantGrid() {
        return informantGrid;
    }

    public void setInformantGrid(String informantGrid) {
        this.informantGrid = informantGrid;
    }

    public String getInformant() {
        return informant;
    }

    public void setInformant(String informant) {
        this.informant = informant;
    }

    public List<BizPhotoList> getBizPhotoList() {
        return bizPhotoList;
    }

    public void setBizPhotoList(List<BizPhotoList> bizPhotoList) {
        this.bizPhotoList = bizPhotoList;
    }
}
