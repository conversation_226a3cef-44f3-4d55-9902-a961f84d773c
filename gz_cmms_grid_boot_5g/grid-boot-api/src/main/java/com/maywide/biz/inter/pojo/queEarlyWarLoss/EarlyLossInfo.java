package com.maywide.biz.inter.pojo.queEarlyWarLoss;

import java.io.Serializable;

/**
 * Created by lisongkang on 2019/6/27 0001.
 */
public class EarlyLossInfo implements Serializable {
    private Long id;
    private String biId;//bi标识
    private String tableName;//bi表名
    private String custid;//客户编号
    private String custName;//客户姓名
    private String whgridCode;//网格编号
    private String whgridName;//网格名称
    private String address;//地址
    private String addressCode;//地址编号 唯一标识一个大标题下
    //private String phoneNumber;//手机号码
    private int status;//状态  0未处理 1待处理 2已完成
    private String time;//对应时间
    //private Double tomothArreas;//往月欠费
    //private Double thisArreas;//本月欠费
    private String city;//地市
    private String areaid;//业务区


    // 后期增补字段 --- lzt
    private String iphone; // 手机号码
    private Double tomontharrrars; // 往月欠费
    private Double thisarrrars; // 本月欠费
    private String updatetime; // 客户更新时间
    private String remark; // 客户备注
    private Integer phonenums; // 客户拨打次数
    private Long updateoperid; // 记录操作员

    public String getIphone() {
        return iphone;
    }

    public void setIphone(String iphone) {
        this.iphone = iphone;
    }

    public Double getTomontharrrars() {
        return tomontharrrars;
    }

    public void setTomontharrrars(Double tomontharrrars) {
        this.tomontharrrars = tomontharrrars;
    }

    public Double getThisarrrars() {
        return thisarrrars;
    }

    public void setThisarrrars(Double thisarrrars) {
        this.thisarrrars = thisarrrars;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getPhonenums() {
        return phonenums;
    }

    public void setPhonenums(Integer phonenums) {
        this.phonenums = phonenums;
    }

    public Long getUpdateoperid() {
        return updateoperid;
    }

    public void setUpdateoperid(Long updateoperid) {
        this.updateoperid = updateoperid;
    }

    public String getAreaid() {
        return areaid;
    }

    public void setAreaid(String areaid) {
        this.areaid = areaid;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustid() {
        return custid;
    }

    public void setCustid(String custid) {
        this.custid = custid;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getBiId() {
        return biId;
    }

    public void setBiId(String biId) {
        this.biId = biId;
    }

    public String getWhgridCode() {
        return whgridCode;
    }

    public void setWhgridCode(String whgridCode) {
        this.whgridCode = whgridCode;
    }


    public String getAddressCode() {
        return addressCode;
    }

    public void setAddressCode(String addressCode) {
        this.addressCode = addressCode;
    }


    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getWhgridName() {
        return whgridName;
    }

    public void setWhgridName(String whgridName) {
        this.whgridName = whgridName;
    }

}
