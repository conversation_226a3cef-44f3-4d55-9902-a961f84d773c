package com.maywide.biz.inter.service;

import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.TokenReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.constant.QueConstant;
import com.maywide.biz.inter.entity.BizPunchCard;
import com.maywide.biz.inter.pojo.quePunchCardReported.QueReportedPunchCardReq;
import com.maywide.biz.inter.pojo.queReportedPushCard.QuePunchCardsResp;
import com.maywide.biz.inter.pojo.queReportedPushCard.QuePushCardReportedResp;
import com.maywide.biz.inter.pojo.queReportedPushCard.QueReportedInfo;
import com.maywide.biz.inter.pojo.queReportedPushCard.QueSceneReportedsResp;
import com.maywide.biz.inter.pojo.savePunchCard.SavePunchCardReq;
import com.maywide.biz.survey.entity.BizPhotoList;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.service.PersistentService;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: zhouzhixiang
 * @Date: 2021/11/9
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class BizPunchCardService extends CommonService {

    @Autowired
    private PersistentService persistentService;


    //金U家签到
    public TokenReturnInfo savePunchCard(SavePunchCardReq req)throws Exception{
        CheckUtils.checkNull(req, "请求信息不能为空");
        TokenReturnInfo token = new TokenReturnInfo();
        token.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        token.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        CheckUtils.checkEmpty(req.getFaultAddress(),"请选择地址再上报");

        CheckUtils.checkEmpty(req.getImgUrl(),"");
        CheckUtils.checkEmpty(req.getImgUrl(),"请提交图片");
        CheckUtils.checkEmpty(req.getImgUrl(),"请提交图片");
        CheckUtils.checkNull(req.getInformantId(),"请选择填报人");
        CheckUtils.checkEmpty(req.getInformant(),"请选择填报人");
        CheckUtils.checkEmpty(req.getFaultRemark(),"请填写签到说明");
        LoginInfo loginInfo = getLoginInfo();
        BizPunchCard biz =   beanTo(loginInfo,req);
        getDAO().save(biz);
        return token;
    }

    private BizPunchCard beanTo(LoginInfo login, SavePunchCardReq req){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String day = format.format(new Date());
        BizPunchCard bizInfo = new BizPunchCard();
        bizInfo.setPeopleId(login.getOperid());
        bizInfo.setPeopleName(login.getName());
        bizInfo.setPeopleTime(day);
        bizInfo.setCreateBy(login.getOperid());
        bizInfo.setCreateAt(day);
        bizInfo.setUpdateBy(login.getOperid());
        bizInfo.setUpdateAt(day);
        bizInfo.setFaultAddress(req.getFaultAddress());
        bizInfo.setLatitude(req.getLatitude());
        bizInfo.setImgUrl(req.getImgUrl());
        bizInfo.setInformantId(req.getInformantId());
        bizInfo.setInformant(req.getInformant());
        bizInfo.setFaultRemark(req.getFaultRemark());
        bizInfo.setLongitude(req.getLongitude());
        bizInfo.setAreaId(login.getAreaid());
        return bizInfo;
    }

    //查询

    //签到人查询自己全部上报内容
    public TokenReturnInfo quePunchCards(QuePunchCardsResp resp)throws Exception{
        TokenReturnInfo token = new TokenReturnInfo();
        token.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        token.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = getLoginInfo();
        List<QueSceneReportedsResp> resps = queReporteds(loginInfo.getOperid(),null);
        resp.setResp(resps);
        token.setData(resp);
        return token;
    }

    //签到人查询需要自己填报
    public TokenReturnInfo quePunchCardInformants()throws Exception{

        TokenReturnInfo token = new TokenReturnInfo();
        token.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        token.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = getLoginInfo();
        List<QueSceneReportedsResp> resp = queReporteds(null,loginInfo.getOperid());
        token.setData(resp);
        return token;
    }

    private List<QueSceneReportedsResp> queReporteds(Long reportId,Long infomantId) throws Exception {
        StringBuffer sqlBuffer = new StringBuffer();
        List<Object> params = Collections.synchronizedList(new ArrayList<>());
        sqlBuffer.append(" select id,people_id peopleId,people_name peopleName,people_time peopleTime,");
        sqlBuffer.append(" fault_address faultAddress,latitude,longitude,fault_remark faultRemark,img_url imgUrl,informant_id informantId,");
        sqlBuffer.append(" informant from biz_punch_card ");
        sqlBuffer.append(" where 1=1");
        if(reportId!=null){//上报人查询
            sqlBuffer.append(" and people_id = ?");
            params.add(reportId);
        }
        if(infomantId!=null){//填报人查询
            sqlBuffer.append(" and informant_id = ?");
            params.add(infomantId);
        }
        sqlBuffer.append(" order by people_time desc");
        List<QueSceneReportedsResp> resp =  getDAO().find(sqlBuffer.toString(),QueSceneReportedsResp.class,params.toArray());
        if(resp!=null && resp.size()>0){
            for(QueSceneReportedsResp que : resp){
                que.setBizPhotoList(selectImg(que));
            }
        }
        return resp;
    }

    public List<BizPhotoList> selectImg(QueSceneReportedsResp que) throws Exception {
        List<BizPhotoList> imgPhotos  = new ArrayList<>();
        if(que.getImgUrl()!=null&&que.getImgUrl().length()>3){
            String[] imgUrl = que.getImgUrl().split(",");
            if(imgUrl!=null && imgUrl.length>0){
                StringBuffer imgSql = new StringBuffer();
                List<Object> imgParams = Collections.synchronizedList(new ArrayList<>());
                imgSql.append(" Select * from biz_photo_list where fileid in ( ");
                for(int i = 0,size = imgUrl.length; i < size;i++){
                    imgSql.append("?");
                    imgParams.add(Long.valueOf(imgUrl[i]));
                    if (i < size - 1) {
                        imgSql.append(",");
                    }
                }
                imgSql.append(")");
                imgPhotos = persistentService.find(imgSql.toString(), BizPhotoList.class,imgParams.toArray());
            }
        }
        return imgPhotos;
    }






    /**
     * 查找填报人员集合
     * @param req
     * @param
     * @return
     * @throws Exception
     */

    public TokenReturnInfo quePunchCardReported(QueReportedPunchCardReq req, QuePushCardReportedResp resp) throws Exception{
        TokenReturnInfo returnInfo = new TokenReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);
        CheckUtils.checkNull(req, "请求对象不能为空");

        List<Object> params = new ArrayList();
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT operid,loginname,name from prv_operator WHERE status = '0' and  operid in ( ");
        sqlBuffer.append(" SELECT DISTINCT(operid) from ( ");
        sqlBuffer.append(" SELECT * from PRV_OPERROLE  where deptid in( ");
        sqlBuffer.append(" SELECT DISTINCT(deptid) from prv_department where city = ? and areaid = ?)) a where a.roleid in ( ");
        sqlBuffer.append(" SELECT roleid from prv_roleprivs where menuid in (SELECT menuid from prv_menudef  where bizcode = 'BIZ_ZSXEGCSQ'))) ");
        params.add(req.getCity());
        if(!StringUtils.isBlank(req.getAreaId())){
            params.add(Long.valueOf(req.getAreaId()));
        }else{
            params.add(loginInfo.getAreaid());
        }
        List<QueReportedInfo> queReportedInfoList  = getDAO().find(sqlBuffer.toString(),
                QueReportedInfo.class, params.toArray());

        if(queReportedInfoList!=null && queReportedInfoList.size()>1){
            ListIterator<QueReportedInfo> list = queReportedInfoList.listIterator();
            QueReportedInfo usrw = new QueReportedInfo();
            try {
                while (list.hasNext()){
                    QueReportedInfo info = list.next();
                    if(info.getOperid().equals(loginInfo.getOperid())){
                        usrw = info;
                        list.remove();
                    }
                }
            }catch (Exception e){}
            queReportedInfoList.add(0,usrw);
        }
        resp.setResp(queReportedInfoList);
        return returnInfo;
    }
}
