package com.maywide.biz.inter.pojo.quqeSubmitBizLogInfo;

import com.maywide.biz.core.pojo.api.BaseApiRequest;

/**
 * @Author: zhouzhixiang
 * @Date: 2022/3/4
 */
public class QueSubmitBizLogInfoReq extends BaseApiRequest {

    //必填
    private String isback;// 是否已回退
    private String stime;//开始时间 : "2021-10-01 00:00:00",
    private String etime;//结束时间 : "2021-10-30 24:59:59",
    private String deptid;// 部门
    //选填
    private String name;//客户名称
    private String operid;//操作工号
    private String custid;//客户编号
    private String permarks;//业务类型
    private String depttype;// 部门类型 业务区areaid为100直属时，不需传值.其他业务区需传 0受理部门 1收费部门
    private String pageNo;//请求页编号
    private String pageSize;// 每页记录数
    private String opcode;//业务操作

    //判断业务区为100 时可传字段
    private String isloginaccept;//是否无纸化
    private String primaryGrid;//区域
    private String secondaryGrid;//中网格
    private String tertiaryGrid;//小网格
    private String system;//受理渠道
    private String chkflag;//确认状态
    private String cmtype;//宽带子类型  选填，permarks=2时必传    CM:U宽频,COM:普通宽带


    public String getOpcode() {
        return opcode;
    }

    public void setOpcode(String opcode) {
        this.opcode = opcode;
    }

    public String getCmtype() {
        return cmtype;
    }

    public void setCmtype(String cmtype) {
        this.cmtype = cmtype;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOperid() {
        return operid;
    }

    public void setOperid(String operid) {
        this.operid = operid;
    }

    public String getCustid() {
        return custid;
    }

    public void setCustid(String custid) {
        this.custid = custid;
    }

    public String getChkflag() {
        return chkflag;
    }

    public void setChkflag(String chkflag) {
        this.chkflag = chkflag;
    }

    public String getIsloginaccept() {
        return isloginaccept;
    }

    public void setIsloginaccept(String isloginaccept) {
        this.isloginaccept = isloginaccept;
    }

    public String getPrimaryGrid() {
        return primaryGrid;
    }

    public void setPrimaryGrid(String primaryGrid) {
        this.primaryGrid = primaryGrid;
    }

    public String getSecondaryGrid() {
        return secondaryGrid;
    }

    public void setSecondaryGrid(String secondaryGrid) {
        this.secondaryGrid = secondaryGrid;
    }

    public String getTertiaryGrid() {
        return tertiaryGrid;
    }

    public void setTertiaryGrid(String tertiaryGrid) {
        this.tertiaryGrid = tertiaryGrid;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getPermarks() {
        return permarks;
    }

    public void setPermarks(String permarks) {
        this.permarks = permarks;
    }

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    public String getDepttype() {
        return depttype;
    }

    public void setDepttype(String depttype) {
        this.depttype = depttype;
    }

    public String getStime() {
        return stime;
    }

    public void setStime(String stime) {
        this.stime = stime;
    }

    public String getEtime() {
        return etime;
    }

    public void setEtime(String etime) {
        this.etime = etime;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getIsback() {
        return isback;
    }

    public void setIsback(String isback) {
        this.isback = isback;
    }
}
