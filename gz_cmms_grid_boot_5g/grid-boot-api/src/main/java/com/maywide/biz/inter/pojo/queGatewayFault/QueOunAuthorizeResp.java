package com.maywide.biz.inter.pojo.queGatewayFault;

import java.io.Serializable;

/**
 * Created by lisongkang on 2019/10/30 0001.
 */
public class QueOunAuthorizeResp implements Serializable {
    private Long id;
    private int eid;
    private int shelfNo;
    private int slotNo;
    private int ponNo;
    private int onuNo;
    private String recTime;
    private int percent;
    private String remark;
    private String mac;
    private String sn;
    private String loid;
    private String serialNo;
    private String ipAddress;
    private Boolean isGateway;
    private String tippsThree;
    private String tippsFour;
    private String tippsFive;
    private String tippsSix;
    private String username;
    private String usertel;
    private String useraddr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getEid() {
        return eid;
    }

    public void setEid(int eid) {
        this.eid = eid;
    }

    public int getShelfNo() {
        return shelfNo;
    }

    public void setShelfNo(int shelfNo) {
        this.shelfNo = shelfNo;
    }

    public int getSlotNo() {
        return slotNo;
    }

    public void setSlotNo(int slotNo) {
        this.slotNo = slotNo;
    }

    public int getPonNo() {
        return ponNo;
    }

    public void setPonNo(int ponNo) {
        this.ponNo = ponNo;
    }

    public int getOnuNo() {
        return onuNo;
    }

    public void setOnuNo(int onuNo) {
        this.onuNo = onuNo;
    }

    public String getRecTime() {
        return recTime;
    }

    public void setRecTime(String recTime) {
        this.recTime = recTime;
    }

    public int getPercent() {
        return percent;
    }

    public void setPercent(int percent) {
        this.percent = percent;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getLoid() {
        return loid;
    }

    public void setLoid(String loid) {
        this.loid = loid;
    }

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Boolean getGateway() {
        return isGateway;
    }

    public void setGateway(Boolean gateway) {
        isGateway = gateway;
    }

    public String getTippsThree() {
        return tippsThree;
    }

    public void setTippsThree(String tippsThree) {
        this.tippsThree = tippsThree;
    }

    public String getTippsFour() {
        return tippsFour;
    }

    public void setTippsFour(String tippsFour) {
        this.tippsFour = tippsFour;
    }

    public String getTippsFive() {
        return tippsFive;
    }

    public void setTippsFive(String tippsFive) {
        this.tippsFive = tippsFive;
    }

    public String getTippsSix() {
        return tippsSix;
    }

    public void setTippsSix(String tippsSix) {
        this.tippsSix = tippsSix;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUsertel() {
        return usertel;
    }

    public void setUsertel(String usertel) {
        this.usertel = usertel;
    }

    public String getUseraddr() {
        return useraddr;
    }

    public void setUseraddr(String useraddr) {
        this.useraddr = useraddr;
    }
}
