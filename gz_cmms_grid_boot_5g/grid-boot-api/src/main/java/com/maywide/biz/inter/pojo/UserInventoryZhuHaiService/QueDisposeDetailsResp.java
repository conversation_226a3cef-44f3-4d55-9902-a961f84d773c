package com.maywide.biz.inter.pojo.UserInventoryZhuHaiService;

/**
 * @ClassName QueDisposeDetailsResp
 * @Description 查询处理详情
 * <AUTHOR>
 * @Date 2021-11-18 16:16
 * @Version 1.0
 **/
public class QueDisposeDetailsResp implements java.io.Serializable {

    private String table_name;    // BI表名

    private int status; // 状态：0未处理 1待处理 2已完成

    private String cust_id; // 客户编号

    private String cust_name; // 客户姓名

    private String whgridcode; // 网格编号


    private String whgridname;    // 所属网格

    private String address; // 客户地址

    private String address_code; // 地址编号BI唯一标示

    private String iphone; // 手机号码

    private String time; // 相关时间



    private String city;    // 地市

    private Double tomonth_arrrars; // 往月欠费

    private Double this_arrrars; // 本月欠费

    private String areaid; // 业务区id

    private String updatetime; // 更新状态时间


    private String remark; // 客户备注

    private int phonenums; // 客户打电话次数

    private Long update_operid; // 记录操作员


    public String getTable_name() {
        return table_name;
    }

    public void setTable_name(String table_name) {
        this.table_name = table_name;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getCust_id() {
        return cust_id;
    }

    public void setCust_id(String cust_id) {
        this.cust_id = cust_id;
    }

    public String getCust_name() {
        return cust_name;
    }

    public void setCust_name(String cust_name) {
        this.cust_name = cust_name;
    }

    public String getWhgridcode() {
        return whgridcode;
    }

    public void setWhgridcode(String whgridcode) {
        this.whgridcode = whgridcode;
    }

    public String getWhgridname() {
        return whgridname;
    }

    public void setWhgridname(String whgridname) {
        this.whgridname = whgridname;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress_code() {
        return address_code;
    }

    public void setAddress_code(String address_code) {
        this.address_code = address_code;
    }

    public String getIphone() {
        return iphone;
    }

    public void setIphone(String iphone) {
        this.iphone = iphone;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Double getTomonth_arrrars() {
        return tomonth_arrrars;
    }

    public void setTomonth_arrrars(Double tomonth_arrrars) {
        this.tomonth_arrrars = tomonth_arrrars;
    }

    public Double getThis_arrrars() {
        return this_arrrars;
    }

    public void setThis_arrrars(Double this_arrrars) {
        this.this_arrrars = this_arrrars;
    }

    public String getAreaid() {
        return areaid;
    }

    public void setAreaid(String areaid) {
        this.areaid = areaid;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getPhonenums() {
        return phonenums;
    }

    public void setPhonenums(int phonenums) {
        this.phonenums = phonenums;
    }

    public Long getUpdate_operid() {
        return update_operid;
    }

    public void setUpdate_operid(Long update_operid) {
        this.update_operid = update_operid;
    }
}
