package com.maywide.biz.inter.service;

import com.maywide.biz.ass.topatch.entity.BizGridInfo;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.pojo.TokenReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.service.ParamService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.constant.QueConstant;
import com.maywide.biz.inter.pojo.getDataParam.GetDataParamResp;
import com.maywide.biz.inter.pojo.getParamsOrderBy.GetParamsOrderByReq;
import com.maywide.biz.inter.pojo.queGridCascadeData.QueGridCascadeData;
import com.maywide.biz.inter.pojo.queGridCascadeData.QueGridCascadeDataResp;
import com.maywide.biz.inter.pojo.quqeSubmitBizLogInfo.QueSubmitBizLogInfoReq;
import com.maywide.biz.inter.pojo.quqeSubmitBizLogInfo.QueSubmitBizLogInfoResp;
import com.maywide.biz.inter.pojo.quqeSubmitBizLogInfo.SubmitBizLog;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.util.BeanUtil;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: zhouzhixiang
 * @Date: 2022/3/3
 */
@Service
public class QueSubmitBizLogService extends CommonService {

    @Autowired
    private ParamService paramService;

    public TokenReturnInfo queSubmitBizLogInfo(QueSubmitBizLogInfoReq req, QueSubmitBizLogInfoResp resp)throws Exception{
        TokenReturnInfo token = new TokenReturnInfo();
        token.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        token.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);
        CheckUtils.checkEmpty(req.getStime(),"请选择开始时间");
        CheckUtils.checkEmpty(req.getEtime(),"请选择结束时间");
        CheckUtils.checkEmpty(req.getIsback(),"请选择回退类型");
//        CheckUtils.checkEmpty(req.getDeptid(),"请选择部门");
        req.setDeptid(loginInfo.getDeptid()+"");
        if(req.getPermarks().equals("*")){
            req.setPermarks(null);
        }
//        条件判断
        if(loginInfo.getAreaid()==100){
           req.setDepttype(null);
           if(StringUtils.isNotBlank(req.getPermarks())){
               if(req.getPermarks().equals("2")){
                   CheckUtils.checkEmpty(req.getChkflag(),"请选择宽带子类型");
               }
           }

        }else{
            CheckUtils.checkEmpty(req.getDepttype(),"请选择部门类型");
            req.setIsloginaccept(null);
            req.setPrimaryGrid(null);
            req.setSecondaryGrid(null);
            req.setTertiaryGrid(null);
            req.setSystem(null);
            req.setChkflag(null);
        }
        String bossOutput = getBossHttpInfOutput(req.getBizorderid(), BizConstant.BossInterfaceService.QUE_SUBMITBIZLOG,
                req, loginInfo);
        JSONObject array = new JSONObject(bossOutput);
        QueSubmitBizLogInfoResp respBoss = (QueSubmitBizLogInfoResp) BeanUtil.jsonToObject(array, QueSubmitBizLogInfoResp.class);
        if(respBoss!=null){
            resp.setPageNo(respBoss.getPageNo());
            resp.setPageSize(respBoss.getPageSize());
            resp.setTotalCount(respBoss.getTotalCount());
            resp.setResult(respBoss.getResult());
            if(respBoss.getResult()!=null && respBoss.getResult().size()>0){
                StringBuffer sql = new StringBuffer("select * from Prv_Sysparam c where  c.gcode='SYS_OPCODE'");
                List<PrvSysparam> sysList = getDAO().find(sql.toString(), PrvSysparam.class,null);

                List<PrvSysparam> isB = paramService.getData("BIZ_ISBACK");
                List<PrvSysparam> systems = paramService.getData("SYS_SYSTEM");
                List<PrvSysparam> permarksList = paramService.getData("PRD_PERMARK");
                for(SubmitBizLog info :respBoss.getResult()){
                    if(StringUtils.isNotBlank(info.getOpcode())){
                        for(PrvSysparam sys : sysList){
                            if(info.getOpcode().equals(sys.getMcode())){
                                info.setOpcodeName(sys.getMname());
                                break;
                            }
                        }
                    }
                    if(StringUtils.isNotBlank(info.getPermarks())){//业务类型
                        if(permarksList!=null && permarksList.size()>0){
                            StringBuffer permarksStr = new StringBuffer();
                            String permark[] = info.getPermarks().split(",");
                            for(int i=0;i<permark.length;i++){
                                for(PrvSysparam pers : permarksList){
                                    if(permark[i].equals(pers.getMcode())){
                                        permarksStr.append(pers.getMname());
                                        if(i<permark.length){
                                            permarksStr.append(",");
                                        }
                                        break;
                                    }
                                }
                            }
                            info.setPermarks(permarksStr.toString());
                        }
                    }
                    if(StringUtils.isNotBlank(info.getIsloginaccept())){//是否无纸化单
                        if(info.getIsloginaccept().equals("Y")){
                            info.setIsloginaccept("是");
                        }else if(info.getIsloginaccept().equals("N")){
                            info.setIsloginaccept("否");
                        }
                    }
                    if(StringUtils.isNotBlank(info.getIsback())){//是否已回退
                        if(isB!=null && isB.size()>0){
                            for(PrvSysparam is : isB){
                                if(info.getIsback().equals(is.getMcode())){
                                    info.setIsback(is.getMname());
                                    break;
                                }
                            }
                        }
                    }
                    if(StringUtils.isNotBlank(info.getSystem())){//渠道
                        if(systems!=null && systems.size()>0){
                            for(PrvSysparam is : systems){
                                if(info.getSystem().equals(is.getMcode())){
                                    info.setSystem(is.getMname());
                                    break;
                                }
                            }
                        }
                    }
                    if(StringUtils.isNotBlank(info.getOmode())){//开户模式
                        if(info.getOmode().equals("0")){
                            info.setOmode("新设备");
                        }else if(info.getOmode().equals("1")){
                            info.setOmode("以旧换新");
                        }else if(info.getOmode().equals("2")){
                            info.setOmode("设备增开户");
                        }
                    }
                    if(StringUtils.isNotBlank(info.getOptime())){
                        info.setOptime(info.getOptime().replace("T"," "));
                    }
                }

                resp.setResult(respBoss.getResult());
            }
        }
        return token;
    }


    /**
     * 获取当前操作员网格信息
     *
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queGridCascadeData(QueGridCascadeDataResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = getLoginInfo();
        List params = new ArrayList();
        StringBuffer sb = new StringBuffer();
        sb.append("		SELECT A.GRIDID gridId, A.GRIDCODE gridCode,A.GRIDNAME gridName,A.GTYPE gType");
        sb.append("		FROM BIZ_GRID_INFO A ,BIZ_GRID_MANAGER B");
        sb.append("		WHERE 1 = 1 AND ");
        sb.append("		A.GRIDID = B.GRIDID AND ");
        sb.append("		B.OPERID = ? AND");
        sb.append("		A.CITY = ? ");

        params.add(loginInfo.getOperid());
        params.add(loginInfo.getCity());
        sb.append("		AND ( A.GTYPE = ? ");
        sb.append("		OR A.GTYPE = ? )");
        params.add(BizConstant.BizGridObjObjtype.PATCH);
        params.add(BizConstant.BizGridObjObjtype.ADDR);
        List<QueGridCascadeData> datas = getDAO().find(sb.toString(), QueGridCascadeData.class, params.toArray());
        if (datas == null || datas.isEmpty())
            CheckUtils.checkNull(null, "查询不到当前操作员网格信息");

        hanlderManagerGrid(datas, loginInfo);
        resp.setList(datas);
        return returnInfo;
    }

    private void hanlderManagerGrid(List<QueGridCascadeData> datas, LoginInfo loginInfo) throws Exception {
        if (datas.size() > 0) {
            for (QueGridCascadeData child : datas) {
                BizGridInfo bizInfo = new BizGridInfo();
                bizInfo.setCity(loginInfo.getCity());
                bizInfo.setPrevid(child.getGridId());
                List<BizGridInfo> mChilds = getDAO().find(bizInfo);
                if (mChilds != null && !mChilds.isEmpty()) {
                    List<QueGridCascadeData> list = getGridInfoResp4BizGridInfo(mChilds);
                    child.setNodes(list);
                    hanlderManagerGrid(list, loginInfo);
                }
            }
        }
    }

    private List<QueGridCascadeData> getGridInfoResp4BizGridInfo(List<BizGridInfo> mChilds) {
        List<QueGridCascadeData> datas = new ArrayList<>();
        for (BizGridInfo info : mChilds) {
            QueGridCascadeData resp = new QueGridCascadeData();
            resp.setGridCode(info.getGridcode());
            resp.setGridId(info.getId());
            resp.setGridName(info.getGridname());
            resp.setgType(info.getGtype()+"");
            resp.setPrvid(info.getPrevid()+"");
            datas.add(resp);
        }
        return datas;
    }


    public TokenReturnInfo getParamsOrderBy(GetParamsOrderByReq req, GetDataParamResp resp)throws Exception{
        TokenReturnInfo tokenReturnInfo = new  TokenReturnInfo();
        tokenReturnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        tokenReturnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        CheckUtils.checkEmpty(req.getGcode(), "参数为null");
        LoginInfo loginInfo = getLoginInfo();
        if(req.getGcode().equals("SYS_OPCODE")){

            if(loginInfo.getCity().equals("GZ")){
                StringBuffer sql = new StringBuffer("select * from Prv_Sysparam c where c.mname like '%开户%' and c.gcode='SYS_OPCODE'");
                List<PrvSysparam> sysList = getDAO().find(sql.toString(), PrvSysparam.class,null);
                resp.setParamdata(sysList);
                return tokenReturnInfo;
            }else{
                StringBuffer sql = new StringBuffer("select * from prv_sysparam t where t.gcode like 'SYS_OPCODE_BIZ' ");
                List<PrvSysparam> sysList = getDAO().find(sql.toString(), PrvSysparam.class,null);
                if(sysList!=null && sysList.size()>0){
                    String sqldeta = sysList.get(0).getData();
                    List<PrvSysparam> sysLists = getDAO().find(sqldeta, PrvSysparam.class,null);
                    resp.setParamdata(sysLists);
                    return tokenReturnInfo;
                }
            }

        }
        List<PrvSysparam> list = paramService.getDataOrderBySort(req.getGcode());
        if(list==null || list.size()==0){
             CheckUtils.checkEmpty(null, req.getGcode()+"参数有误");
        }
        resp.setParamdata(list);
        return tokenReturnInfo;
    }
}
