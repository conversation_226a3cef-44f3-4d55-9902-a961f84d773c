package com.maywide.biz.inter.pojo.bizWeChatCallback;

import com.maywide.biz.core.pojo.api.BaseApiRequest;

/**
 * @ClassName QueWeChatQRcodeURLReq
 * @Description 微信厅二维码url
 * <AUTHOR>
 * @Date 2022-04-08 11:09
 * @Version 1.0
 **/
public class QueWeChatQRcodeURLReq extends BaseApiRequest {

    private String chlid;   // 客户类型

    private String orderid;   // 工单id

    private String usercustid;   // 客户编号(用户编号)

    private String username;   // 用户名(客户名称)

    private String usermainnbr;   // 主叫号码(可以为空值【null】，微信端留空显示表名即可。)

    private String usermobiletel;   // 手机号码

    private String worktypeid;   // 工单类型ID：1安装、2故障

    private String rebackreason;   // 故障现象,当工单类型ID：1安装的时候需要有值。例如：网关无信号

    private String rebackresult;   // 处理结果,当工单类型ID：1安装的时候需要有值。统一传值：故障已修复

    private String rebackservice;   // 服务内容,当工单类型ID：2故障的时候需要有值。例如：网关业务（中山）-安装

    private String reviewsult;   // 满意度评价情况(客户填写)未参评 0、评价中 1 、满意 2 、不满意 3、系统默认满意 4

    private String revieworderid; // 订单号，微信厅交互的orderid


    // 补充字段
    private Long id;// 序号
    private String workorderid;// 工单编号
    private String usercity;// 地市标识ID,佛山:FS,广州,GZ
    private String userareaid;// 业务区标识ID,禅城区:681,直属,100
    private String netgrid_code;// 网格编号

    private String netgrid_name;// 网格名称
    private String userlogicdevno;// 用户智能卡号信息
    private String revisitmod;// 满意度方式名称：wx微信
    private String reviewresdesc;// 满意度评价内容(客户填写)
    private String reviewdate;// 满意度时间,微信回调数据给CMMS时间点

    private String reviewmcropenid;// 满意度扫码二维码的微信id(扫码的openid)
    private String reviewscantime;// 满意度扫码二维码时间
    private String reviewevent;// 满意度扫码二维码事件(SCAN为扫码前就有关注，subscribe为本次扫码关注)


    public String getChlid() {
        return chlid;
    }

    public void setChlid(String chlid) {
        this.chlid = chlid;
    }

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getUsercustid() {
        return usercustid;
    }

    public void setUsercustid(String usercustid) {
        this.usercustid = usercustid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUsermainnbr() {
        return usermainnbr;
    }

    public void setUsermainnbr(String usermainnbr) {
        this.usermainnbr = usermainnbr;
    }

    public String getUsermobiletel() {
        return usermobiletel;
    }

    public void setUsermobiletel(String usermobiletel) {
        this.usermobiletel = usermobiletel;
    }

    public String getWorktypeid() {
        return worktypeid;
    }

    public void setWorktypeid(String worktypeid) {
        this.worktypeid = worktypeid;
    }

    public String getRebackreason() {
        return rebackreason;
    }

    public void setRebackreason(String rebackreason) {
        this.rebackreason = rebackreason;
    }

    public String getRebackresult() {
        return rebackresult;
    }

    public void setRebackresult(String rebackresult) {
        this.rebackresult = rebackresult;
    }

    public String getRebackservice() {
        return rebackservice;
    }

    public void setRebackservice(String rebackservice) {
        this.rebackservice = rebackservice;
    }

    public String getReviewsult() {
        return reviewsult;
    }

    public void setReviewsult(String reviewsult) {
        this.reviewsult = reviewsult;
    }

    public String getRevieworderid() {
        return revieworderid;
    }

    public void setRevieworderid(String revieworderid) {
        this.revieworderid = revieworderid;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWorkorderid() {
        return workorderid;
    }

    public void setWorkorderid(String workorderid) {
        this.workorderid = workorderid;
    }

    public String getUsercity() {
        return usercity;
    }

    public void setUsercity(String usercity) {
        this.usercity = usercity;
    }

    public String getUserareaid() {
        return userareaid;
    }

    public void setUserareaid(String userareaid) {
        this.userareaid = userareaid;
    }

    public String getNetgrid_code() {
        return netgrid_code;
    }

    public void setNetgrid_code(String netgrid_code) {
        this.netgrid_code = netgrid_code;
    }

    public String getNetgrid_name() {
        return netgrid_name;
    }

    public void setNetgrid_name(String netgrid_name) {
        this.netgrid_name = netgrid_name;
    }

    public String getUserlogicdevno() {
        return userlogicdevno;
    }

    public void setUserlogicdevno(String userlogicdevno) {
        this.userlogicdevno = userlogicdevno;
    }

    public String getRevisitmod() {
        return revisitmod;
    }

    public void setRevisitmod(String revisitmod) {
        this.revisitmod = revisitmod;
    }

    public String getReviewresdesc() {
        return reviewresdesc;
    }

    public void setReviewresdesc(String reviewresdesc) {
        this.reviewresdesc = reviewresdesc;
    }

    public String getReviewdate() {
        return reviewdate;
    }

    public void setReviewdate(String reviewdate) {
        this.reviewdate = reviewdate;
    }

    public String getReviewmcropenid() {
        return reviewmcropenid;
    }

    public void setReviewmcropenid(String reviewmcropenid) {
        this.reviewmcropenid = reviewmcropenid;
    }

    public String getReviewscantime() {
        return reviewscantime;
    }

    public void setReviewscantime(String reviewscantime) {
        this.reviewscantime = reviewscantime;
    }

    public String getReviewevent() {
        return reviewevent;
    }

    public void setReviewevent(String reviewevent) {
        this.reviewevent = reviewevent;
    }


}
