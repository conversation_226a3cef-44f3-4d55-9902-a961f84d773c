package com.maywide.grid.boot.baseweb.config.filter;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@WebFilter(filterName = "requestBodyHandleFilter", urlPatterns = "/*")
@Order(110)
public class RequestBodyHandleFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOEx<PERSON>, ServletException {
        HttpServletRequest req = (HttpServletRequest) servletRequest;
        HttpServletResponse resp = (HttpServletResponse) servletResponse;
        String contentType = req.getContentType();

        if (StringUtils.contains(contentType, MediaType.APPLICATION_JSON_VALUE)) {
            CmmsRequestWrapper wrapper = new CmmsRequestWrapper(req);
            filterChain.doFilter(wrapper, resp);
            return;
        }

        filterChain.doFilter(req, resp);
    }

    @Override
    public void destroy() {
    }
}
