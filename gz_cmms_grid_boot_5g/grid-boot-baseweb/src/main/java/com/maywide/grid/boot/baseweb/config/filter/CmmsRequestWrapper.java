package com.maywide.grid.boot.baseweb.config.filter;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class CmmsRequestWrapper extends HttpServletRequestWrapper {

    /**
     * 请求的inputstream字符串
     */
    private String body;

    public String getBody() {
        return body;
    }

    public void setBody(HttpServletRequest request) throws IOException {
        // 读取body的报文
        StringBuilder postJson = new StringBuilder();
        BufferedReader httpReader = request.getReader();
        if (httpReader != null) {
            String line = null;
            while ((line = httpReader.readLine()) != null) {
                postJson.append(line);
            }
        }
        this.body = postJson.toString();
    }

    public CmmsRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        setBody(request);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        // 创建字节数组输入流
        final ByteArrayInputStream bais = new ByteArrayInputStream(body.getBytes(StandardCharsets.UTF_8));

        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() throws IOException {
                return bais.read();
            }
        };

    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }
}
