spring.profiles.active=sysconfig,pdb,log,core

server.port=8011
server.servlet.context-path=/gridOcr

spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=25MB

grid.boot.apiInterceptor.exclude=/cmmsOcr/**

ocr.paddle.py.path=/usr1/cmms_ocr/python3/id_ocr.py
ocr.paddle.py.command=python
ocr.file.uplaod.temp.path=/usr1/cmms_ocr/service/img_temp

ocr.wz.api=http://*************/gdzs/client/ocr.action