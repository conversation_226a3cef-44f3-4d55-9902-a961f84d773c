package com.maywide.cmms.ocr.bean.ocr;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PaddleOcrRet implements Serializable {

    private String savePath;
    private List<PaddleOcrData> data;

    public String getSavePath() {
        return savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }

    public List<PaddleOcrData> getData() {
        return data;
    }

    public void setData(List<PaddleOcrData> data) {
        this.data = data;
    }
}
