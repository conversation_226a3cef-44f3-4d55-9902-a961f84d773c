package com.maywide.cmms.ocr.api;

import com.alibaba.fastjson.JSON;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.cmms.ocr.bean.api.OcrRet;
import com.maywide.cmms.ocr.service.OcrService;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.util.ThreadPoolUtil;
import com.maywide.grid.boot.baseweb.bean.ResponseMvcWarpper;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cmmsOcr")
public class OcrApi {

    private Logger log = LoggerFactory.getLogger(OcrApi.class);

    private static String MODEL_WZ = "wz";
    private static String MODEL_COMM = "comm";

    private ThreadPoolTaskExecutor threadPool = ThreadPoolUtil.getThreadPool("ocr-log-");

    @Autowired
    private OcrService ocrService;

    @Value("${ocr.file.uplaod.temp.path}")
    private String ocrFilePathTemp;

    @RequestMapping("/idCard")
    public ResponseMvcWarpper cmmsIdCardOcr(String model, @RequestParam("file") MultipartFile file) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        // 保存图片
        String img = saveTempImg(file);

        OcrRet ret = null;
        if (StringUtils.equalsIgnoreCase(MODEL_COMM, model)) {
            log.info("=>执行paddle ocr");
            ret = ocrService.getIdCardOcrData(img);

        } else if (StringUtils.equalsIgnoreCase(MODEL_WZ, model)) {
            log.info("=>执行无纸化Ocr接口");
            ret = ocrService.getWzApiData("2", img);

        } else {
            throw new BusinessException("MODEL错误. ");
        }

        File temp = new File(img);
        if (temp.exists()) {
            temp.delete();
        }

        // 保存日志
        saveLog(ret);

        return ResponseMvcWarpper.toResponse(returnInfo, ret);

    }

    @RequestMapping("/bankCard")
    public ResponseMvcWarpper cmmsBankCardOcr(String model, @RequestParam("file") MultipartFile file) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        // 保存图片
        String img = saveTempImg(file);

        OcrRet ret = null;
        if (StringUtils.equalsIgnoreCase(MODEL_COMM, model)) {
            log.info("=>执行paddle ocr");
            ret = ocrService.getBankCardOcrData(img);

        } else if (StringUtils.equalsIgnoreCase(MODEL_WZ, model)) {
            log.info("=>执行无纸化Ocr接口");
            ret = ocrService.getWzApiData("17", img);

        } else {
            throw new BusinessException("MODEL错误. ");
        }

        File temp = new File(img);
        if (temp.exists()) {
            temp.delete();
        }

        // 保存日志
        saveLog(ret);

        return ResponseMvcWarpper.toResponse(returnInfo, ret);

    }

    private void saveLog(OcrRet ret) {
        try {
            threadPool.execute(new Runnable() {
                @Override
                public void run() {
                    String retJsonStr = JSON.toJSONString(ret);
                    ocrService.saveLog(retJsonStr);
                }
            });
        } catch (Exception e) {
            log.error("=>OCR日志保存失败. ", e);
        }
    }

    private String saveTempImg(MultipartFile file) throws IOException {
        File dic = new File(this.ocrFilePathTemp);
        if (!dic.exists()) {
            dic.mkdirs();
        }

        String uuid = UUID.randomUUID().toString();
        String filename = file.getOriginalFilename();
        String ext = StringUtils.substring(filename, filename.lastIndexOf("."));
        String newFileName = uuid + ext;
        String newFile = this.ocrFilePathTemp + "/" + newFileName;

        FileOutputStream fos = new FileOutputStream(newFile);
        IOUtils.copy(file.getInputStream(), fos);
        return newFile;
    }

}
