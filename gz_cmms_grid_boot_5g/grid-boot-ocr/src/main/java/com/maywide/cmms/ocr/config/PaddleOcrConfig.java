package com.maywide.cmms.ocr.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.maywide.core.exception.BusinessException;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Order(10)
public class PaddleOcrConfig implements ApplicationRunner {

    private Logger log = LoggerFactory.getLogger(PaddleOcrConfig.class);

    public static final String OCR_CONFIG_FILE = "orc_config.json";
    private static final String CLASS_PATH = "classpath:";

    @Value("${paddle.ocr.config.path:classpath:config/ocr/}")
    private String path;
    @Value("${paddle.ocr.config.file.encode:utf-8}")
    private String fileEncode;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        InputStream is = getConfigInputStream();
        String string = IOUtils.toString(is, fileEncode);
        toObjConfig(string);
    }

    private InputStream getConfigInputStream() throws BusinessException, FileNotFoundException {
        log.info("=>读取{}: {}", OCR_CONFIG_FILE, path);
        if (StringUtils.startsWith(this.path, CLASS_PATH)) {
            String cpath = StringUtils.replace(this.path, CLASS_PATH, "");
            return this.getClass().getClassLoader().getResourceAsStream(cpath + OCR_CONFIG_FILE);
        } else {
            File file = new File(this.path);
            if (!file.exists()) {
                throw new BusinessException(String.format("=>[%s%s]文件不存在", path, OCR_CONFIG_FILE));
            }
            return new FileInputStream(file);
        }
    }

    private void toObjConfig(String json) {
        // 获得身份证配置
        JSONObject jsonObject = JSON.parseObject(json);
        String idCardConf = jsonObject.getString(PaddleOcrObjConfig.ID_CARD);
        log.info("=>身份证配置: {}", idCardConf);
        List<String> idTitle = JSON.parseObject(idCardConf, new TypeReference<List<String>>() {
        });
        PaddleOcrObjConfig.idCardTitle = idTitle;
    }
}
