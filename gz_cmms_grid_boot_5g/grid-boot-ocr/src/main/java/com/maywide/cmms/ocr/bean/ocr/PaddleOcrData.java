package com.maywide.cmms.ocr.bean.ocr;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PaddleOcrData implements Serializable {

    private String text;
    private String confidence;
    private List<List<String>> textBoxPosition;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getConfidence() {
        return confidence;
    }

    public void setConfidence(String confidence) {
        this.confidence = confidence;
    }

    public List<List<String>> getTextBoxPosition() {
        return textBoxPosition;
    }

    public void setTextBoxPosition(List<List<String>> textBoxPosition) {
        this.textBoxPosition = textBoxPosition;
    }
}
