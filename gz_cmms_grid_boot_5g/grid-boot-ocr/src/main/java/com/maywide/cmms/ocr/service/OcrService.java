package com.maywide.cmms.ocr.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.cmms.ocr.bean.api.OcrRet;
import com.maywide.cmms.ocr.bean.ocr.PaddleOcrData;
import com.maywide.cmms.ocr.bean.ocr.PaddleOcrRet;
import com.maywide.cmms.ocr.config.PaddleOcrObjConfig;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.util.resttemplate.ApiCallUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Service
public class OcrService extends CommonService {

    private Logger log = LoggerFactory.getLogger(OcrService.class);

    @Value("${ocr.paddle.py.path:}")
    private String ocrPaddlePy;
    @Value("${ocr.paddle.py.command:}")
    private String ocrPaddlePyCommand;

    @Value("${ocr.paddle.ret.start:#==start==#}")
    private String ocrRetStart;
    @Value("${ocr.paddle.ret.start:#==end==#}")
    private String ocrRetEnd;
    @Value("${ocr.paddle.ret.encode:utf-8}")
    private String ocrRetEncode;

    @Value("${ocr.wz.api}")
    private String ocrWzApi;

    /**
     * 获得 身份证的Ocr数据
     * @param imgPath
     * @return
     */
    public OcrRet getIdCardOcrData(String imgPath) throws BusinessException, IOException, InterruptedException {
        OcrRet ocrRet = new OcrRet();
        PaddleOcrRet paddleOcrRet = callPaddleOcr(imgPath);

        ocrRet.setCardType("身份证");
        Map<String, String> info = new LinkedHashMap<>();
        ocrRet.setCardInfo(info);

        String text = toOcrDataStr(paddleOcrRet);
        if (StringUtils.isNotBlank(text)) {
            putInToInfo(info, text);
        }

        // 优化
        String sex = info.get("性别");
        if (StringUtils.isNotBlank(sex) && StringUtils.length(sex) > 1) {
            info.put("性别", sex.substring(0, 1));
        }

        return ocrRet;
    }

    private void putInToInfo(Map<String, String> info, String text) {
        if (PaddleOcrObjConfig.idCardTitle != null && !PaddleOcrObjConfig.idCardTitle.isEmpty()) {

            int lastIndex = 0;
            String lastText = "";

            Iterator<String> iterator = PaddleOcrObjConfig.idCardTitle.iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                int index = StringUtils.indexOf(text, next);
                if (index >= 0) {
                    if (StringUtils.isNotBlank(lastText)) {
                        info.put(lastText, StringUtils.substring(text, lastIndex + lastText.length(), index));
                    }
                    lastText = next;
                    lastIndex = index;
                } else {
                    info.put(next, "");
                }
            }

            if (info.get(lastText) == null && StringUtils.isNotBlank(lastText)) {
                info.put(lastText, StringUtils.substring(text, lastIndex + lastText.length()));
            }

        }
    }

    private String toOcrDataStr(PaddleOcrRet paddleOcrRet) {
        if (paddleOcrRet != null) {
            List<PaddleOcrData> data = paddleOcrRet.getData();
            if (data != null && !data.isEmpty()) {
                StringBuffer textBuf = new StringBuffer();
                Iterator<PaddleOcrData> iterator = data.iterator();
                while (iterator.hasNext()) {
                    PaddleOcrData next = iterator.next();
                    String text = next.getText();
                    textBuf.append(text);
                }
                if (textBuf.length() > 0) {
                    return textBuf.toString();
                }
            }
        }

        return null;
    }

    /**
     * 调用 paddle ocr
     * @param imgPath
     * @return
     */
    public PaddleOcrRet callPaddleOcr(String imgPath) throws BusinessException, IOException, InterruptedException {
        // 检查执行脚本是否存在
        checkScript();
        if (StringUtils.isBlank(imgPath) || !new File(imgPath).exists()) {
            throw new BusinessException("=>Ocr图片不存在. ");
        }
        // 获得 Runtime
        Runtime run = Runtime.getRuntime();
        String command = this.ocrPaddlePyCommand + " " + this.ocrPaddlePy + " " + imgPath;
        log.info("=>执行脚本: {}", command);
        Process process = run.exec(command);
        process.waitFor();
        log.info("=>执行完成, 读取结果...");
        InputStream inputStream = process.getInputStream();
        List<String> lines = IOUtils.readLines(inputStream, this.ocrRetEncode);
        StringBuffer ocrRetJsonStr = new StringBuffer();
        // 是否是需要的字符串
        boolean f = false;
        Iterator<String> iterator = lines.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            log.info("=>读取结果: {}", next);
            if (StringUtils.equals(next, this.ocrRetEnd)) {
                f = false;
            }

            if (f) {
                ocrRetJsonStr.append(next);
            }

            if (StringUtils.equals(next, this.ocrRetStart)) {
                f = true;
            }
        }
        log.info("=>获得Ocr结果字符串: {}", ocrRetJsonStr.toString());
        if (ocrRetJsonStr.length() > 0) {
            List<PaddleOcrRet> rets = JSON.parseObject(ocrRetJsonStr.toString(), new TypeReference<List<PaddleOcrRet>>() {
            });
            return rets == null || rets.isEmpty() ? null : rets.get(0);
        }

        return null;
    }

    private void checkScript() throws BusinessException {
        if (StringUtils.isBlank(this.ocrPaddlePy)) {
            throw new BusinessException("=>PaddleOcr Python Script is null. ");
        }

        if (StringUtils.isBlank(this.ocrPaddlePyCommand)) {
            throw new BusinessException("=>PaddleOcr Python Env is null. ");
        }
    }

    public void saveLog(String retJsonStr) {

    }

    public OcrRet getWzApiData(String type, String img) throws Exception {
        return callWzApi(type, img);
    }

    private OcrRet callWzApi(String type, String img) throws Exception {
        // 文件
        File file = new File(img);
        // imgType
        String imgType = StringUtils.substring(img, img.lastIndexOf(".") + 1);

        // 转出form参数
        LinkedMultiValueMap<String, Object> valueMap = new LinkedMultiValueMap<>();
        valueMap.add("ocrImg", new FileSystemResource(file));
        valueMap.add("imgType", imgType);
        valueMap.add("cardType", type);

        // 调用接口
        String excute = ApiCallUtil.build().post(ocrWzApi).formData(valueMap).excute();
        // 返回数据
        if (StringUtils.isBlank(excute)) {
            throw new BusinessException("=>无纸化OCR接口异常,无数据返回");
        } else {
            return JSON.parseObject(excute, OcrRet.class);
        }

    }

    /**
     * 获得银行卡号
     * @param imgPath
     * @return
     */
    public OcrRet getBankCardOcrData(String imgPath) throws BusinessException, IOException, InterruptedException {
        OcrRet ocrRet = new OcrRet();
        PaddleOcrRet paddleOcrRet = callPaddleOcr(imgPath);

        ocrRet.setCardType("银行卡");
        Map<String, String> info = new LinkedHashMap<>();
        ocrRet.setCardInfo(info);

        String bankno = getBankNo(paddleOcrRet);

        info.put("卡号", bankno);

        return ocrRet;
    }

    /**
     * 获得银行卡号
     * @param paddleOcrRet
     * @return
     */
    private String getBankNo(PaddleOcrRet paddleOcrRet) {
        // 银行卡规则 62 开头，最大19位
        StringBuffer sb = new StringBuffer();

        boolean start = false;
        List<PaddleOcrData> data = paddleOcrRet.getData();
        for (PaddleOcrData datum : data) {
            String text = datum.getText();
            if (StringUtils.isBlank(text)) {
                continue;
            }
            if (start) {
                // 判断 大于19 就停止了
                int l1 = sb.length();
                int l2 = text.length();
                if (l1 + l2 > 19) {
                    start = false;
                    break;
                }
            }

            // b 换成 6
            text = StringUtils.replace(text, "b", "6");
            // 将字母去掉
            text = text.replaceAll("[^0-9]", "");

            // 62开头的开始使用
            if (StringUtils.startsWithIgnoreCase(text, "62")) {
                start = true;
            }

            if (start) {
                sb.append(text);
            }

        }

        String bankno = sb.toString();

        return bankno;
    }

}
