package com.maywide.cmms.ocr;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan("com.maywide")
@ImportResource({"classpath:config/xml/spring-context.xml"})
public class OcrApplication {

    public static void main(String[] args) {
        SpringApplication.run(OcrApplication.class);
    }

}
