package com.maywide.biz.inter.pojo.sendAcOrderToCust;

import com.maywide.biz.core.pojo.api.BaseApiRequest;

/**
 * <AUTHOR>
 * @date 2021/6/21 0021
 */
public class SendAcOrderToCustReq extends BaseApiRequest {

    /**
     * 订单号
     */
    private String ordercode;
    /**
     * 发送渠道 0-短信，1-微信
     */
    private String channel;
    /**
     * 流水号
     */
    private String serialno;

    /**
     * 手机号
     * */
    private String mobile;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOrdercode() {
        return ordercode;
    }

    public void setOrdercode(String ordercode) {
        this.ordercode = ordercode;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getSerialno() {
        return serialno;
    }

    public void setSerialno(String serialno) {
        this.serialno = serialno;
    }
}
