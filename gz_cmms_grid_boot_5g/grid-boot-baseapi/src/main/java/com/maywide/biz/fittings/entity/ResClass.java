package com.maywide.biz.fittings.entity;



import java.io.Serializable;

/**
 * Created by lisongkang on 2020/8/6 0001.
 */
public class ResClass implements Serializable {
    private String subkind;
    private String name;
    private String kind;
    private String restype;
    private String restypename;

    public String getSubkind() {
        return subkind;
    }

    public void setSubkind(String subkind) {
        this.subkind = subkind;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getRestype() {
        return restype;
    }

    public void setRestype(String restype) {
        this.restype = restype;
    }

    public String getRestypename() {
        return restypename;
    }

    public void setRestypename(String restypename) {
        this.restypename = restypename;
    }
}
