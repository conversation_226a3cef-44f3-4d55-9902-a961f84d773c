package com.maywide.biz.pay.unify.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.core.servlet.SysConfig;
import com.maywide.biz.inter.constant.QueConstant.CommonNotice;
import com.maywide.biz.inter.pojo.quecustorder.CustordersBO;
import com.maywide.biz.inter.pojo.quecustorder.QueCustorderInterReq;
import com.maywide.biz.inter.pojo.quecustorder.QueCustorderInterResp;
import com.maywide.biz.inter.service.PubService;
import com.maywide.biz.market.entity.BizPortalOrder;
import com.maywide.biz.pay.unify.pojo.*;
import com.maywide.biz.pay.unify.pojo.localWeixinPay.LocalWeixinPayBean;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.util.BeanUtils;
import com.maywide.core.util.CheckUtils;
import com.maywide.core.util.DateUtils;
import com.maywide.payplat.security.SecureLink;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * 本地微信支付--张耀其 -2020-12 跟统一支付差不多，部分参数有差别
 */
@Service
public class LocalWeixinPayService extends CommonService {

	@Autowired
	private PubService pubService;

	public ReturnInfo queLocalWeixinPayInfo(QueUnifyPayInfoReq req, QueUnifyPayInfoResp resp) throws Exception {
		ReturnInfo returnInfo = new ReturnInfo();
		returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
		returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

		LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
		CheckUtils.checkNull(loginInfo, CommonNotice.LOGIN_OUT_NOTICE);

		CheckUtils.checkNull(req, "请求对象不能为空");
		CheckUtils.checkNull(req.getCustorderid(), "订单号不能为空");
		CheckUtils.checkNull(req.getCondition(), "订单条件不能为空");

		QueCustorderInterResp queOrderResp = new QueCustorderInterResp();
		QueCustorderInterReq queOrderReq = new QueCustorderInterReq();
		queOrderReq.setDetail(false);
		queOrderReq.setCustorderid(req.getCustorderid());
		ReturnInfo queOrderInfo = pubService.queCustorder(queOrderReq, queOrderResp);
		if (queOrderInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException(queOrderInfo.getMessage(), queOrderInfo.getCode());
		}
		if (queOrderResp.getCustorders() == null || queOrderResp.getCustorders().size() == 0) {
			throw new BusinessException("查询订单失败");
		}
		CustordersBO order = queOrderResp.getCustorders().get(0);
		BizPortalOrder bizPortalOrder = (BizPortalOrder) getDAO().find(BizPortalOrder.class,Long.parseLong(order.getCustorderid()));
		CheckUtils.checkNull(bizPortalOrder,"缴费失败,查询不到对应的订单信息!");
		// 更改为不执行sql，改到回调执行
        Long payid = getSnowflakeOrderId();
		QueUnifyPayInfoResp quResp = generatePayReq(order, req.getCondition(),req.getUnifyPayWechatBingBean(),req.getMultipayBean(), payid);
		
		BeanUtils.copyProperties(resp, quResp);
		return returnInfo;
	}

	public QueUnifyPayInfoResp generatePayReq(CustordersBO order, UnifyPayCondition condition,
                                              UnifyPayWechatBingBean unifyPayWechatBingBean, MultiPayBean multiPayBean, Long payid) throws BusinessException {

		String requestId = getRequestId();
		LocalWeixinPayBean payBean = getLocalWeixinPayBean(order, requestId, condition,unifyPayWechatBingBean, multiPayBean,payid);

		HashMap<String, String> map = new HashMap<String, String>();
//		map.put("clienttype", BizConstant.PAY_CLIENT_TYPE.MERCHANT_MOBILE);
		map.put("clienttype", "02");

		map.put("servicecode", BizConstant.Platform_Service.PAY);
		map.put("clientcode", SysConfig.getPlatform_code());
		map.put("clientpwd", SysConfig.getPlatform_pwd());
		map.put("requestid", requestId);
		String requestContent = new Gson().toJson(payBean);
		System.out.println("==dataSign的requestContent为:====="+requestContent);
		map.put("requestContent", requestContent);

		String dataSign = getDataSign(map);
		map.put("dataSign", dataSign);

		String url = getUrlWithMap(SysConfig.getPlatform_outer_url(), map);
		QueUnifyPayInfoResp resp = new QueUnifyPayInfoResp(requestId, requestContent, url);
		return resp;
	}

	private static LocalWeixinPayBean getLocalWeixinPayBean(CustordersBO order, String requestId,
                                                            UnifyPayCondition condition, UnifyPayWechatBingBean unifyPayWechatBingBean, MultiPayBean multiPayBean,
                                                            Long payid)
			throws BusinessException {
		LocalWeixinPayBean payBean = new LocalWeixinPayBean();
		payBean.setIsOrder("N");
		String totalFees = order.getFees();
		if(multiPayBean != null) {
			if(StringUtils.isNotBlank(multiPayBean.getMultipaywayflag())
					&& multiPayBean.getMultipaywayflag().equalsIgnoreCase("Y")) {
				if(StringUtils.isNotBlank(multiPayBean.getCashe())) {
					double casheFees = Double.parseDouble(multiPayBean.getCashe());
					double orderFees = Double.parseDouble(order.getFees());
					if(casheFees >= orderFees) {
						throw new BusinessException("当前账本余额大于订单金额,无需使用第三方支付！");
					}
					double totalfees = orderFees - casheFees;
					totalFees = Double.toString(totalfees);
				}
			}
		}
//		totalFees = "0.01";
//		totalFees = "1";
		payBean.setTotalFee(totalFees);
		String redirectUrl = String.format("%s?orderNo=%s&payDetail=%s", SysConfig.getPlatform_redirect_url(),
				payid, order.getOpcodename());

		payBean.setRedirectUrl(redirectUrl);

		LoginInfo loginInfo = AuthContextHolder.getLoginInfo();

		HashMap<String, String> noticeParam = new HashMap<String, String>();
		noticeParam.put("clientId", loginInfo.getLoginname());
		noticeParam.put("requestid", requestId);
		noticeParam.put("orderid", order.getCustorderid());
		noticeParam.put("payid",payid.toString());
		noticeParam.put("loginInfo", getLoginInfoJson(loginInfo));
		noticeParam.put("biztype", condition.getBizType());
		noticeParam.put("city",order.getCity());
		noticeParam.put("areaid",order.getAreaid());
		if(null != multiPayBean && StringUtils.isNotBlank(multiPayBean.getMultipaywayflag())
				&& StringUtils.isNotBlank(multiPayBean.getCashe())) {
			if(multiPayBean.getMultipaywayflag().equalsIgnoreCase("Y")) {
				noticeParam.put("multipaywayflag","Y");
				noticeParam.put("cashe",multiPayBean.getCashe());
			}
		}

		if(unifyPayWechatBingBean != null && !"{}".equals(unifyPayWechatBingBean))
		{
			if(unifyPayWechatBingBean.getDevno() != null && !"{}".equals(unifyPayWechatBingBean.getDevno())){
				noticeParam.put("devno",unifyPayWechatBingBean.getDevno());
				noticeParam.put("permark",unifyPayWechatBingBean.getPermark());
			}
		}

		if (condition.getExtra() != null && !"{}".equals(condition.getExtra())) {
			noticeParam.put("extra", condition.getExtra());
		}
		String noticeAction = getUrlWithMap(SysConfig.getPlatform_notice_url(), noticeParam);
		//番禺CMMS由于TOMCAT未升级 不能转码
		if(SysConfig.getSysyemIdentification().trim().equals("py-product")){
			noticeAction = noticeAction.replace("\"", "\\\""); //tomcat6
		}else {
			noticeAction = encodeUrl(noticeAction.replace("\"", "\\\""));
		}

		payBean.setNoticeAction(noticeAction);
//		payBean.setOrderNum(order.getCustorderid());
		payBean.setOrderNum(payid.toString());

		LocalWeixinPayBean.CustInfo custInfo = new LocalWeixinPayBean.CustInfo();
		custInfo.setCustid(order.getCustid());
		custInfo.setCustname(order.getCustname());
		custInfo.setCity(order.getCity());
		custInfo.setArea(order.getAreaid());
		payBean.setCustInfo(custInfo);

		LocalWeixinPayBean.OrderInfo orderInfo = new LocalWeixinPayBean.OrderInfo();
//		orderInfo.setOrderNo(order.getCustorderid());
		orderInfo.setOrderNo(payid.toString());

		LocalWeixinPayBean.OrderInfo.Product product = new LocalWeixinPayBean.OrderInfo.Product();
		product.setProductName(order.getOpcodename());
		product.setFee(order.getFees());

		List<LocalWeixinPayBean.OrderInfo.Product> productList = new ArrayList<LocalWeixinPayBean.OrderInfo.Product>();
		productList.add(product);
		orderInfo.setProductList(productList);
		payBean.setOrderInfo(orderInfo);
		return payBean;
	}

	private static String getLoginInfoJson(LoginInfo loginInfo) {
		JsonObject jsonObj = new JsonObject();
		jsonObj.addProperty("operid", loginInfo.getOperid());
		jsonObj.addProperty("loginname", loginInfo.getLoginname());
		jsonObj.addProperty("deptid", loginInfo.getDeptid());
		jsonObj.addProperty("areaid", loginInfo.getAreaid());
		jsonObj.addProperty("city", loginInfo.getCity());
		jsonObj.addProperty("roleid", loginInfo.getRoleid());
		return jsonObj.toString();
	}

	private static String encodeUrl(String url) {
		try {
			return URLEncoder.encode(url, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			return url;
		}
	}

	private static String getUrlWithMap(String url, HashMap<String, String> map) {
		String params = getUrlParamWithMap(map);
		return String.format("%s?%s", url, params);
	}

	private static String getUrlParamWithMap(HashMap<String, String> map) {
		StringBuilder sb = new StringBuilder();
		boolean isFirst = true;
		for (Map.Entry<String, String> entry : map.entrySet()) {
			if (isFirst) {
				isFirst = false;
			} else {
				sb.append("&");
			}
			sb.append(entry.getKey()).append("=").append(entry.getValue());
		}
		return sb.toString();
	}

	private static String getDataSign(HashMap<String, String> map) {
		String dataSign = null;
		System.out.println(map);
		try {
			dataSign = SecureLink.sign(map, SysConfig.getPlatform_MD5());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return dataSign;
	}

	private static String getRequestId() {
		return SysConfig.getPlatform_code() + DateUtils.formatDate(new Date(), DateUtils.FORMAT_YYYYMMDD)
				+ UUID.randomUUID().toString().substring(0, 8);
	}


}
