package com.maywide.biz.prd.service;

import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.prd.pojo.FgbMainOfferBo;
import com.maywide.core.dao.BaseDao;
import com.maywide.core.dao.support.Page;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.service.BaseService;
import com.maywide.core.service.PersistentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class PrdProductCmmsService extends BaseService<FgbMainOfferBo, Long> {

	@Autowired
	private PersistentService persistentService;

	@Override
	protected BaseDao<FgbMainOfferBo, Long> getEntityDao() {
		return null;
	}

	public PageImpl<FgbMainOfferBo> findPrdRuleByPage(Pageable pageable,String objtype) throws Exception {
		LoginInfo loginInfo = AuthContextHolder.getLoginInfo();

		PageImpl<FgbMainOfferBo> pageResult = null;
		Page<FgbMainOfferBo> page = new Page<FgbMainOfferBo>();
		page.setPageNo(pageable.getPageNumber() + 1);
		page.setPageSize(pageable.getPageSize());

		List params = new ArrayList();

		StringBuffer queSql = new StringBuffer();
		if("0".equals(objtype)){
			queSql.append("select \n" +
					"t1.salesid,\n" +
					"t.offercode, \n" +
					"t.offername, \n" +
					"t.offerdesc, \n" +
					"t.fixedcharge price, \n" +
					"t1.offertype,  \n" +
					"'4' unit \n" +
					"from fgb_mainoffer t \n" +
					"left join prd_sales_mobile t1 on t.offercode = t1.offercode \n" +
					"where t1.ismain = '0'");
		}else if("1".equals(objtype)){
			queSql.append("select \n" +
					"t1.salesid, \n" +
					"t.offercode, \n" +
					"t.offername, \n" +
					"t.offerdesc, \n" +
					"t.price, \n" +
					"t1.offertype, \n" +
					"t.termunit unit \n" +
					"from fgb_suboffer t \n" +
					"left join prd_sales_mobile t1 on t.offercode = t1.offercode \n" +
					"where t1.ismain = '1'");
		}else if("2".equals(objtype)){
			return pageResult;
		}



		page = persistentService.find(page, queSql.toString(), entityClass, params.toArray());
		if (page != null && page.getResult() != null) {
			pageResult = new PageImpl<FgbMainOfferBo>(page.getResult(), pageable, page.getTotalCount());
		} else {
			pageResult = new PageImpl<FgbMainOfferBo>(new ArrayList<FgbMainOfferBo>(), pageable, 0);
		}
		return pageResult;
	}

}
