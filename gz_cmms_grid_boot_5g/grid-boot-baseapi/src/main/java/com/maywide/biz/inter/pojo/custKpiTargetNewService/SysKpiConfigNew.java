package com.maywide.biz.inter.pojo.custKpiTargetNewService;

import javax.persistence.*;

/**
 * 用户类指标配置(新)
 *
 * <AUTHOR>
 */
@Table(name = "sys_kpi_config_new")
public class SysKpiConfigNew {

    private Long recid; //bigint(20) NOT NULL AUTO_INCREMENT,
    private String kpicode; //varchar(255) NOT null COMMENT 'kpicode',
    private String kpiname; //varchar(255) NOT null COMMENT 'kpi名称',
    private String serviceid; //varchar(255) NOT null COMMENT 'DC接口serviceid',
    private String tjtype; //varchar(16) DEFAULT null COMMENT '统计类型: D-日, M-月',
    private String reqparams; //varchar(128) DEFAULT null COMMENT '请求参数',
    private String respmapping; //varchar(512) DEFAULT null COMMENT '响应参数映射',
    private String detserviceid;
    private String detreqparams;
    private String detrespmapping;
    private String dettype;
    private String needkpicode;
    private String custtype;
    private String isdetail; //varchar(255) NOT null COMMENT '是否有明细',
    private String isshow; //varchar(16) NOT NULL DEFAULT 'Y' COMMENT '是否显示',
    private String city; //varchar(255) NOT null COMMENT '地市, *-全部地市',
    private String meno; //varchar(255) DEFAULT null COMMENT '备注',
    private Long sort; //bigint(3) DEFAULT null COMMENT '排序',

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "recid")
    public Long getRecid() {
        return recid;
    }

    public void setRecid(Long recid) {
        this.recid = recid;
    }

    public String getKpicode() {
        return kpicode;
    }

    public void setKpicode(String kpicode) {
        this.kpicode = kpicode;
    }

    public String getKpiname() {
        return kpiname;
    }

    public void setKpiname(String kpiname) {
        this.kpiname = kpiname;
    }

    public String getServiceid() {
        return serviceid;
    }

    public void setServiceid(String serviceid) {
        this.serviceid = serviceid;
    }

    public String getTjtype() {
        return tjtype;
    }

    public void setTjtype(String tjtype) {
        this.tjtype = tjtype;
    }

    public String getReqparams() {
        return reqparams;
    }

    public void setReqparams(String reqparams) {
        this.reqparams = reqparams;
    }

    public String getRespmapping() {
        return respmapping;
    }

    public void setRespmapping(String respmapping) {
        this.respmapping = respmapping;
    }

    public String getDetreqparams() {
        return detreqparams;
    }

    public void setDetreqparams(String detreqparams) {
        this.detreqparams = detreqparams;
    }

    public String getDetrespmapping() {
        return detrespmapping;
    }

    public void setDetrespmapping(String detrespmapping) {
        this.detrespmapping = detrespmapping;
    }

    public String getNeedkpicode() {
        return needkpicode;
    }

    public void setNeedkpicode(String needkpicode) {
        this.needkpicode = needkpicode;
    }

    public String getIsdetail() {
        return isdetail;
    }

    public void setIsdetail(String isdetail) {
        this.isdetail = isdetail;
    }

    public String getIsshow() {
        return isshow;
    }

    public void setIsshow(String isshow) {
        this.isshow = isshow;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getMeno() {
        return meno;
    }

    public void setMeno(String meno) {
        this.meno = meno;
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public String getCusttype() {
        return custtype;
    }

    public void setCusttype(String custtype) {
        this.custtype = custtype;
    }

    public String getDettype() {
        return dettype;
    }

    public void setDettype(String dettype) {
        this.dettype = dettype;
    }

    public String getDetserviceid() {
        return detserviceid;
    }

    public void setDetserviceid(String detserviceid) {
        this.detserviceid = detserviceid;
    }
}
