package com.maywide.biz.pay.gzboss.pojo;

import com.alibaba.fastjson.JSONObject;

import javax.persistence.Transient;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class BScanCPayResp implements Serializable {

    public static final String STATUS_SUCCESS = "1";
    public static final String STATUS_FAIL = "0";

    private String orderNo;
    /**
     * 应付金额
     */
    private String orderAmount;
    /**
     * 实付金额
     */
    private String outTradeNo;
    /**
     * 优惠金额
     */
    private String payway;
    private String paycode;
    private String payname;
    private String paytime;
    private JSONObject bizCustomInfo;
    /**
     * 交易状态： 1成功 0 失败
     */
    private String status;
    private String scanUrl;//支付二维码
    private Long poid;

    // 支付状态
    private String payStatus;

    // 提交状态
    private String submitStatus;

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getSubmitStatus() {
        return submitStatus;
    }

    public void setSubmitStatus(String submitStatus) {
        this.submitStatus = submitStatus;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getPayway() {
        return payway;
    }

    public void setPayway(String payway) {
        this.payway = payway;
    }

    public String getPaycode() {
        return paycode;
    }

    public void setPaycode(String paycode) {
        this.paycode = paycode;
    }

    public String getPayname() {
        return payname;
    }

    public void setPayname(String payname) {
        this.payname = payname;
    }

    public String getPaytime() {
        return paytime;
    }

    public void setPaytime(String paytime) {
        this.paytime = paytime;
    }

    public JSONObject getBizCustomInfo() {
        return bizCustomInfo;
    }

    public void setBizCustomInfo(JSONObject bizCustomInfo) {
        this.bizCustomInfo = bizCustomInfo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getScanUrl() {
        return scanUrl;
    }

    public void setScanUrl(String scanUrl) {
        this.scanUrl = scanUrl;
    }

    public Long getPoid() {
        return poid;
    }

    public void setPoid(Long poid) {
        this.poid = poid;
    }
}
