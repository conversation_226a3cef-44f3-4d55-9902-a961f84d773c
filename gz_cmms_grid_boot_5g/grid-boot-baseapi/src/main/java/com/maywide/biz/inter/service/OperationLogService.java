package com.maywide.biz.inter.service;

import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.prv.entity.OperAction;
import com.maywide.biz.prv.entity.OptActionRecordDetail;
import com.maywide.biz.prv.entity.PrvDepartment;
import com.maywide.core.util.DateUtils;
import com.maywide.util.SqlTplUtils;
import com.maywide.util.StringObjectMapBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * OperationLogService
 *
 * <AUTHOR>
 * @date 2022/12/29 16:47
 * @since 0.0.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class OperationLogService extends CommonService {

    // 异步保存
    @Async
    public void saveOperationLog(LoginInfo loginInfo, String bizCode) {
        try {
            if (StringUtils.isNotBlank(bizCode)) {

                // 保存用户行为（按地市保存）
                OperAction action = new OperAction();
                action.setOpcode(bizCode);
                action.setCity(loginInfo.getCity());
                List<OperAction> operActions = getDAO().find(action);
                if (operActions != null && !operActions.isEmpty()) {
                    action = operActions.get(0);
                    action.setNums(action.getNums() + 1);
                    action.setUpdateTime(new Date());
                    getDAO().update(action);
                } else {
                    action.setNums(1L);
                    action.setUpdateTime(new Date());
                    getDAO().save(action);
                }

                // 保存明细
                OptActionRecordDetail detail = new OptActionRecordDetail();
                detail.setOperid(loginInfo.getOperid().toString());
                detail.setLoginname(loginInfo.getLoginname());
                detail.setOpcode(bizCode);
                detail.setAreaid(loginInfo.getAreaid().toString());
                // 查询用户部门与分公司数据
                String branchOfficeSql = SqlTplUtils.getSql("branch_office.find", StringObjectMapBuilder.builder().build());
                List<PrvDepartment> branchOffices = getDAO().find(branchOfficeSql, PrvDepartment.class, loginInfo.getDeptid());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(branchOffices)) {
                    PrvDepartment branchOffice = branchOffices.get(0);
                    if (branchOffice.getId() != null) {
                        detail.setPreid(branchOffice.getId().toString());
                    }
                }
                detail.setDeptid(loginInfo.getDeptid().toString());
                detail.setCity(loginInfo.getCity());
                detail.setRoleid(loginInfo.getRoleid().toString());
                detail.setCreatetime(new Date());

                // 处理年月日
                String dateStr = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
                String[] split = dateStr.split("-");
                detail.setYear(Long.valueOf(split[0]));
                detail.setMonth(Long.valueOf(split[0] + split[1]));
                detail.setDay(Long.valueOf(split[0] + split[1] + split[2]));

                getDAO().save(detail);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
