package com.maywide.biz.inter.pojo.interCommonService.getPayWay;

import com.maywide.biz.core.pojo.api.BaseApiRequest;

public class ApplyAggregationCodeReq extends BaseApiRequest {
    private String custorderid;
    private String payway;
    private String subpayway;
    private String order_fee;
    private String expire_time;
    private String pay_active_time;
    private String callback_url;
    private String notify_url;
    private String attach;



    public String getCustorderid() {
        return custorderid;
    }

    public void setCustorderid(String custorderid) {
        this.custorderid = custorderid;
    }

    public String getPayway() {
        return payway;
    }

    public void setPayway(String payway) {
        this.payway = payway;
    }

    public String getSubpayway() {
        return subpayway;
    }

    public void setSubpayway(String subpayway) {
        this.subpayway = subpayway;
    }

    public String getOrder_fee() {
        return order_fee;
    }

    public void setOrder_fee(String order_fee) {
        this.order_fee = order_fee;
    }

    public String getExpire_time() {
        return expire_time;
    }

    public void setExpire_time(String expire_time) {
        this.expire_time = expire_time;
    }

    public String getPay_active_time() {
        return pay_active_time;
    }

    public void setPay_active_time(String pay_active_time) {
        this.pay_active_time = pay_active_time;
    }

    public String getCallback_url() {
        return callback_url;
    }

    public void setCallback_url(String callback_url) {
        this.callback_url = callback_url;
    }

    public String getNotify_url() {
        return notify_url;
    }

    public void setNotify_url(String notify_url) {
        this.notify_url = notify_url;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }
}
