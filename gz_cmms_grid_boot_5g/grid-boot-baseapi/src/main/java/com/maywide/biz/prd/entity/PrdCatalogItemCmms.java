package com.maywide.biz.prd.entity;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/6/2 9:29
 */
@Entity
@Table(name = "prd_catalog_item_cmms")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class PrdCatalogItemCmms implements Serializable {
    private Long itemid;
    private Long catalogid;
    private Long knowid;
    private Long pri;
    private Long createoper;
    private Date createtime;
    private Long updateoper;
    private Date updatetime;
    private String city;
    private Long integral;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ITEMID", unique = true)
    public Long getItemid() {
        return itemid;
    }

    public void setItemid(Long itemid) {
        this.itemid = itemid;
    }

    @Column(name = "CATALOGID")
    public Long getCatalogid() {
        return catalogid;
    }

    public void setCatalogid(Long catalogid) {
        this.catalogid = catalogid;
    }

    @Column(name = "KNOWID")
    public Long getKnowid() {
        return knowid;
    }

    public void setKnowid(Long knowid) {
        this.knowid = knowid;
    }

    @Column(name = "PRI")
    public Long getPri() {
        return pri;
    }

    public void setPri(Long pri) {
        this.pri = pri;
    }

    @Column(name = "CREATEOPER")
    public Long getCreateoper() {
        return createoper;
    }

    public void setCreateoper(Long createoper) {
        this.createoper = createoper;
    }

    @Column(name = "CREATETIME")
    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    @Column(name = "UPDATEOPER")
    public Long getUpdateoper() {
        return updateoper;
    }

    public void setUpdateoper(Long updateoper) {
        this.updateoper = updateoper;
    }

    @Column(name = "UPDATETIME")
    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    @Column(name = "CITY")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name = "INTEGRAL")
    public Long getIntegral() {
        return integral;
    }

    public void setIntegral(Long integral) {
        this.integral = integral;
    }
}


