package com.maywide.biz.pay.gzboss.pojo;

public class BizQdElecpayReq {

	private String payorderno;

	private String custid;

	private String payway;

	private String subpayway;

	private String bar_code;

	private String buyer_id;

	private String app_type;

	private String client_ip;

	private String pay_success_url;

	private String pay_fail_url;

	private String payreqid;

	private String notifyUrl;

	private String ext_params;

	public String getPayorderno() {
		return payorderno;
	}

	public void setPayorderno(String payorderno) {
		this.payorderno = payorderno;
	}

	public String getCustid() {
		return custid;
	}

	public void setCustid(String custid) {
		this.custid = custid;
	}

	public String getPayway() {
		return payway;
	}

	public void setPayway(String payway) {
		this.payway = payway;
	}

	public String getSubpayway() {
		return subpayway;
	}

	public void setSubpayway(String subpayway) {
		this.subpayway = subpayway;
	}

	public String getBar_code() {
		return bar_code;
	}

	public void setBar_code(String bar_code) {
		this.bar_code = bar_code;
	}

	public String getBuyer_id() {
		return buyer_id;
	}

	public void setBuyer_id(String buyer_id) {
		this.buyer_id = buyer_id;
	}

	public String getApp_type() {
		return app_type;
	}

	public void setApp_type(String app_type) {
		this.app_type = app_type;
	}

	public String getClient_ip() {
		return client_ip;
	}

	public void setClient_ip(String client_ip) {
		this.client_ip = client_ip;
	}

	public String getPay_success_url() {
		return pay_success_url;
	}

	public void setPay_success_url(String pay_success_url) {
		this.pay_success_url = pay_success_url;
	}

	public String getPay_fail_url() {
		return pay_fail_url;
	}

	public void setPay_fail_url(String pay_fail_url) {
		this.pay_fail_url = pay_fail_url;
	}

	public String getPayreqid() {
		return payreqid;
	}

	public void setPayreqid(String payreqid) {
		this.payreqid = payreqid;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getExt_params() {
		return ext_params;
	}

	public void setExt_params(String ext_params) {
		this.ext_params = ext_params;
	}
}
