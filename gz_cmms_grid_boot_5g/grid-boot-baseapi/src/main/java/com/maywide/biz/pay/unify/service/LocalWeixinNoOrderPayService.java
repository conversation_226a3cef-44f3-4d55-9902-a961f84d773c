package com.maywide.biz.pay.unify.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.servlet.SysConfig;
import com.maywide.biz.inter.entity.CustBizOrderPool;
import com.maywide.biz.inter.service.PubService;
import com.maywide.biz.pay.unify.pojo.QueUnifyPayInfoReq;
import com.maywide.biz.pay.unify.pojo.QueUnifyPayInfoResp;
import com.maywide.biz.pay.unify.pojo.localWeixinPay.LocalWeixinPayBean;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.util.BeanUtils;
import com.maywide.core.util.CheckUtils;
import com.maywide.core.util.DateUtils;
import com.maywide.payplat.security.SecureLink;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * 本地微信支付 -预受理--张耀其 -2020-12 跟统一支付差不多，部分参数有差别
 */
@Service
public class LocalWeixinNoOrderPayService extends CommonService {

	@Autowired
	private PubService pubService;

	public ReturnInfo queLocalWeixinNoOrderPayInfo(QueUnifyPayInfoReq req, QueUnifyPayInfoResp resp) throws Exception {
		ReturnInfo returnInfo = initReturnInfo();
		LoginInfo loginInfo = getLoginInfo();
		CheckUtils.checkNull(req, "请求对象不能为空");
		CheckUtils.checkNull(req.getCustorderid(), "订单号不能为空");
//		CheckUtils.checkNull(req.getCondition(), "订单条件不能为空");
		CustBizOrderPool custBizOrderPool = new CustBizOrderPool();
		custBizOrderPool.setServorderid(Long.parseLong(req.getCustorderid()));
		List<CustBizOrderPool> list = DAO.find(custBizOrderPool);
		if(null == list || list.isEmpty()) {
			CheckUtils.checkNull(null, "查询支付链接地址错误,根据订单号【"+req.getCustorderid()+"】无法查找到相关订单信息!");
		}
		custBizOrderPool = list.get(0);
		CheckUtils.checkNull(custBizOrderPool, "查询支付链接地址错误,根据订单号【"+req.getCustorderid()+"】无法查找到相关订单信息!");
		if(custBizOrderPool.getFeeStatus().equalsIgnoreCase("Y")) {
			CheckUtils.checkNull(null, "该订单已经为已支付状态,无需再次支付!");
		}
		if(custBizOrderPool.getCustOrder() == null){
			custBizOrderPool.setCustOrder(getSnowflakeOrderId());
			getDAO().update(custBizOrderPool);
		}
		// 20200628 增加payid
		Long payid = getSnowflakeOrderId();

		QueUnifyPayInfoResp payResp = generatePayReq(custBizOrderPool.getFees(),
				custBizOrderPool.getCustOrder().toString(), "业务工单处理", custBizOrderPool.getCustName(), custBizOrderPool.getCustid().toString(), BizConstant.BossInterfaceService.WFL_EQUIPINFO_SUBMIT, payid);
		BeanUtils.copyProperties(resp, payResp);
		return returnInfo;
	}

	public  QueUnifyPayInfoResp generatePayReq(String fees,String custOdrerid,String opcode,String custName
			,String custid,String bizType, Long payid) {
		String requestId = getRequestId();
		LocalWeixinPayBean payBean = getLocalWeixinPayBean(fees,custOdrerid,opcode,custName,custid,requestId,bizType,payid);

		HashMap<String, String> map = new HashMap<String, String>();
//		map.put("clienttype", BizConstant.PAY_CLIENT_TYPE.MERCHANT_MOBILE);
		map.put("clienttype", "02");
		map.put("servicecode", BizConstant.Platform_Service.PAY);
		map.put("clientcode", SysConfig.getPlatform_code());
		map.put("clientpwd", SysConfig.getPlatform_pwd());
		map.put("requestid", requestId);
		String requestContent = new Gson().toJson(payBean);
		map.put("requestContent", requestContent);

		String dataSign = getDataSign(map);
		map.put("dataSign", dataSign);

		String url = getUrlWithMap(SysConfig.getPlatform_outer_url(), map);
		QueUnifyPayInfoResp resp = new QueUnifyPayInfoResp(requestId, requestContent, url);
		return resp;
	}

	private static LocalWeixinPayBean getLocalWeixinPayBean(String fees, String custOdrerid, String opcode, String custName
			, String custid, String requestId, String bizType, Long payid){
		LocalWeixinPayBean payBean = new LocalWeixinPayBean();
		payBean.setIsOrder("N");

		payBean.setTotalFee(fees);
		String redirectUrl = String.format("%s?orderNo=%s&payDetail=%s", SysConfig.getPlatform_redirect_url(),
				payid, opcode);
		payBean.setRedirectUrl(redirectUrl);
		LoginInfo loginInfo = AuthContextHolder.getLoginInfo();

		HashMap<String, String> noticeParam = new HashMap<String, String>();
		noticeParam.put("clientId", loginInfo.getLoginname());
		noticeParam.put("requestid", requestId);
		noticeParam.put("orderid", custOdrerid);
		noticeParam.put("payid", payid.toString());
		noticeParam.put("loginInfo", getLoginInfoJson(loginInfo));
		noticeParam.put("biztype", bizType);
		noticeParam.put("city",loginInfo.getCity());
		noticeParam.put("areaid",loginInfo.getAreaid().toString());

		String noticeAction = getUrlWithMap(SysConfig.getPlatform_notice_url(), noticeParam);
		if(SysConfig.getSysyemIdentification().trim().equals("py-product")){
			noticeAction = noticeAction.replace("\"", "\\\""); //tomcta6
		}else {
			noticeAction = encodeUrl(noticeAction.replace("\"", "\\\""));
		}

		payBean.setNoticeAction(noticeAction);

		payBean.setOrderNum(payid.toString());

		LocalWeixinPayBean.CustInfo custInfo = new LocalWeixinPayBean.CustInfo();
		custInfo.setCustid(custid);
		custInfo.setCustname(custName);
		custInfo.setCity(loginInfo.getCity());
		custInfo.setArea(loginInfo.getAreaid().toString());
		payBean.setCustInfo(custInfo);

		LocalWeixinPayBean.OrderInfo orderInfo = new LocalWeixinPayBean.OrderInfo();
		orderInfo.setOrderNo(payid.toString());

		LocalWeixinPayBean.OrderInfo.Product product = new LocalWeixinPayBean.OrderInfo.Product();
		product.setProductName(opcode);
		product.setFee(fees);

		List<LocalWeixinPayBean.OrderInfo.Product> productList = new ArrayList<LocalWeixinPayBean.OrderInfo.Product>();
		productList.add(product);
		orderInfo.setProductList(productList);
		payBean.setOrderInfo(orderInfo);
		return payBean;
	}

	private static String getLoginInfoJson(LoginInfo loginInfo) {
		JsonObject jsonObj = new JsonObject();
		jsonObj.addProperty("operid", loginInfo.getOperid());
		jsonObj.addProperty("loginname", loginInfo.getLoginname());
		jsonObj.addProperty("deptid", loginInfo.getDeptid());
		jsonObj.addProperty("areaid", loginInfo.getAreaid());
		jsonObj.addProperty("city", loginInfo.getCity());
		jsonObj.addProperty("roleid", loginInfo.getRoleid());
		return jsonObj.toString();
	}

	private static String encodeUrl(String url) {
		try {
			return URLEncoder.encode(url, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			return url;
		}
	}

	private static String getUrlWithMap(String url, HashMap<String, String> map) {
		String params = getUrlParamWithMap(map);
		return String.format("%s?%s", url, params);
	}

	private static String getUrlParamWithMap(HashMap<String, String> map) {
		StringBuilder sb = new StringBuilder();
		boolean isFirst = true;
		for (Map.Entry<String, String> entry : map.entrySet()) {
			if (isFirst) {
				isFirst = false;
			} else {
				sb.append("&");
			}
			sb.append(entry.getKey()).append("=").append(entry.getValue());
		}
		return sb.toString();
	}

	private static String getDataSign(HashMap<String, String> map) {
		String dataSign = null;
		System.out.println(map);
		try {
			dataSign = SecureLink.sign(map, SysConfig.getPlatform_MD5());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return dataSign;
	}

	private static String getRequestId() {
		return SysConfig.getPlatform_code() + DateUtils.formatDate(new Date(), DateUtils.FORMAT_YYYYMMDD)
				+ UUID.randomUUID().toString().substring(0, 8);
	}


}
