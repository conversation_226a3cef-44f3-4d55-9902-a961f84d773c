package com.maywide.biz.prd.service;

import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.prd.entity.PrdSalespkgKnowMobile;
import com.maywide.biz.prd.pojo.PrdSalespkgKnowBo;
import com.maywide.core.dao.BaseDao;
import com.maywide.core.dao.support.Page;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.service.BaseService;
import com.maywide.core.service.PersistentService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class PrdConfigCmmsService extends BaseService<PrdSalespkgKnowBo, Long> {

	@Autowired
	private PersistentService persistentService;

	@Override
	protected BaseDao<PrdSalespkgKnowBo, Long> getEntityDao() {
		return null;
	}

	public PageImpl<PrdSalespkgKnowBo> findPrdRuleByPage(Pageable pageable,PrdSalespkgKnowMobile pro) throws Exception {
		LoginInfo loginInfo = AuthContextHolder.getLoginInfo();

		PageImpl<PrdSalespkgKnowBo> pageResult = null;
		Page<PrdSalespkgKnowBo> page = new Page<PrdSalespkgKnowBo>();
		page.setPageNo(pageable.getPageNumber() + 1);
		page.setPageSize(pageable.getPageSize());

		List params = new ArrayList();

		StringBuffer queSql = new StringBuffer();
//		queSql.append("SELECT" +
//				" t.*, " +
//				" ( SELECT city FROM PRV_AREA WHERE areaid = t.areas ) AS city " +
//				"FROM " +
//				" prd_salespkg_know_mobile t  " +
//				"WHERE " +
//				" 1 =1");
//		if(StringUtils.isNotEmpty(pro.getObjtype())){
//			queSql.append(" AND t.objtype = ?");
//			params.add(pro.getObjtype());
//		}
//		if(pro.getObjid()!=null){
//			queSql.append(" AND t.objid = ?");
//			params.add(pro.getObjid().toString());
//		}
//		if(StringUtils.isNotEmpty(pro.getKnowname())){
//			queSql.append(" AND t.knowname like '"+"%"+pro.getKnowname()+"%'");
//		}
//		queSql.append(" and (areas in (select areaid from PRV_AREA where city = ?) or areas = '*') ");
//		params.add(loginInfo.getCity());
//		queSql.append(" ORDER BY createtime DESC");

		queSql.append(" SELECT t0.*, ");
		queSql.append(" (SELECT ps.mname FROM prv_sysparam ps  WHERE ps.gcode = 'PRV_CITY' AND ps.mcode = t0.city ) AS cityname ");
		queSql.append(" FROM prd_salespkg_know_mobile t0 ");
		queSql.append(" LEFT JOIN prd_skm_purview_rel t1 ON t1.knowid = t0.knowid ");
		queSql.append(" WHERE 1 = 1 ");
		if (!loginInfo.isAdmin()) {
			queSql.append(" AND ( (t1.area = ?) ");
			queSql.append(" OR (t1.city = ? AND t1.area = '*') ");
			queSql.append(" OR (t1.city = '*' AND t1.area = '*') ");
			queSql.append(" OR (t1.jobnum = ?) )");
			params.add(loginInfo.getAreaid());
			params.add(loginInfo.getCity());
			params.add(loginInfo.getLoginname());
		}

		if(StringUtils.isNotEmpty(pro.getObjtype())){
			queSql.append(" AND t0.objtype = ? ");
			params.add(pro.getObjtype());
		}
		if(pro.getObjid()!=null){
			queSql.append(" AND t0.objid = ? ");
			params.add(pro.getObjid().toString());
		}
		if(StringUtils.isNotEmpty(pro.getKnowname())){
			queSql.append(" AND t0.knowname like '"+"%"+pro.getKnowname()+"%' ");
		}

		queSql.append(" ORDER BY t0.createtime DESC ");

		page = persistentService.findPage(page, queSql.toString(), entityClass, params.toArray());
		if (page != null && page.getResult() != null) {
			pageResult = new PageImpl<PrdSalespkgKnowBo>(page.getResult(), pageable, page.getTotalCount());
		} else {
			pageResult = new PageImpl<PrdSalespkgKnowBo>(new ArrayList<PrdSalespkgKnowBo>(), pageable, 0);
		}
		return pageResult;
	}


}
