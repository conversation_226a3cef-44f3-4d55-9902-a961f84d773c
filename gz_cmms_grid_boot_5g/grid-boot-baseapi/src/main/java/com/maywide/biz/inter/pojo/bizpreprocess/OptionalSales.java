package com.maywide.biz.inter.pojo.bizpreprocess;

/**
 * Created by lisongkang on 2019/9/16 0001.
 */
public class OptionalSales {
    private String msalesid;//主商品id
    private String salesname;//可选商品name
    private String osalesid;//可选商品id
    private String firstflag;
    private String ismust;
    private String issameobj;

    private Long knowid; //参数来源于商品详情接口 querySalespkgKnow
    private String objtype;
    private Long objid;
    private String price;//单价

    //加产品单位返回
    private String defauNumber;
    private String unitName;

    private String catalogid;


    public String getCatalogid() {
        return catalogid;
    }

    public void setCatalogid(String catalogid) {
        this.catalogid = catalogid;
    }

    public String getDefauNumber() {
        return defauNumber;
    }

    public void setDefauNumber(String defauNumber) {
        this.defauNumber = defauNumber;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getMsalesid() {
        return msalesid;
    }

    public void setMsalesid(String msalesid) {
        this.msalesid = msalesid;
    }

    public String getSalesname() {
        return salesname;
    }

    public void setSalesname(String salesname) {
        this.salesname = salesname;
    }

    public String getOsalesid() {
        return osalesid;
    }

    public void setOsalesid(String osalesid) {
        this.osalesid = osalesid;
    }

    public String getFirstflag() {
        return firstflag;
    }

    public void setFirstflag(String firstflag) {
        this.firstflag = firstflag;
    }

    public String getIsmust() {
        return ismust;
    }

    public void setIsmust(String ismust) {
        this.ismust = ismust;
    }

    public String getIssameobj() {
        return issameobj;
    }

    public void setIssameobj(String issameobj) {
        this.issameobj = issameobj;
    }

    public Long getKnowid() {
        return knowid;
    }

    public void setKnowid(Long knowid) {
        this.knowid = knowid;
    }

    public String getObjtype() {
        return objtype;
    }

    public void setObjtype(String objtype) {
        this.objtype = objtype;
    }

    public Long getObjid() {
        return objid;
    }

    public void setObjid(Long objid) {
        this.objid = objid;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }
}
