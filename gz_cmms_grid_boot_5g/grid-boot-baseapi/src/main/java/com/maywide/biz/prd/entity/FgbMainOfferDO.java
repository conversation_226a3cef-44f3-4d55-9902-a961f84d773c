package com.maywide.biz.prd.entity;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "fgb_mainoffer")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class FgbMainOfferDO implements Serializable {

    private Long recid;
    private String status;
    private String offercode;
    private String offername;
    private String offerdesc;
    private String openbrand;
    private String category;
    private String fixedcharge;
    private String nextchargetype;
    private Date validdate;
    private Date expiredate;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "recid")
    public Long getRecid() {
        return recid;
    }

    public void setRecid(Long recid) {
        this.recid = recid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOffercode() {
        return offercode;
    }

    public void setOffercode(String offercode) {
        this.offercode = offercode;
    }

    public String getOffername() {
        return offername;
    }

    public void setOffername(String offername) {
        this.offername = offername;
    }

    public String getOfferdesc() {
        return offerdesc;
    }

    public void setOfferdesc(String offerdesc) {
        this.offerdesc = offerdesc;
    }

    public String getOpenbrand() {
        return openbrand;
    }

    public void setOpenbrand(String openbrand) {
        this.openbrand = openbrand;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getFixedcharge() {
        return fixedcharge;
    }

    public void setFixedcharge(String fixedcharge) {
        this.fixedcharge = fixedcharge;
    }

    public String getNextchargetype() {
        return nextchargetype;
    }

    public void setNextchargetype(String nextchargetype) {
        this.nextchargetype = nextchargetype;
    }

    public Date getValiddate() {
        return validdate;
    }

    public void setValiddate(Date validdate) {
        this.validdate = validdate;
    }

    public Date getExpiredate() {
        return expiredate;
    }

    public void setExpiredate(Date expiredate) {
        this.expiredate = expiredate;
    }
}
