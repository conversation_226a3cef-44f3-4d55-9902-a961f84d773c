package com.maywide.biz.prd.entity;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;

/**
 * PrdSkmPurviewRel
 *
 * <AUTHOR>
 * @date 2022/12/15 10:12
 * @since 0.0.1
 */
@Entity
@Table(name = "prd_skm_purview_rel")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class PrdSkmPurviewRel implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String knowid;
    private String objid;
    private String purview;
    private String area;
    private String jobnum;

    private String city;

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKnowid() {
        return knowid;
    }

    public void setKnowid(String knowid) {
        this.knowid = knowid;
    }

    public String getObjid() {
        return objid;
    }

    public void setObjid(String objid) {
        this.objid = objid;
    }

    public String getPurview() {
        return purview;
    }

    public void setPurview(String purview) {
        this.purview = purview;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getJobnum() {
        return jobnum;
    }

    public void setJobnum(String jobnum) {
        this.jobnum = jobnum;
    }
}
