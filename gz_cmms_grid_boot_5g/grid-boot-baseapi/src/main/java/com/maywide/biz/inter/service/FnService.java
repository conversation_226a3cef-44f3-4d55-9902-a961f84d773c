package com.maywide.biz.inter.service;

import com.alibaba.fastjson.JSON;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.pojo.TokenReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.service.ParamService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.pojo.FnService.QueFnMenuConfigReq;
import com.maywide.biz.inter.pojo.FnService.entity.BizFnInstallConfig;
import com.maywide.biz.inter.pojo.FnService.entity.DataConfig;
import com.maywide.biz.inter.pojo.FnService.entity.InstallConfig;
import com.maywide.biz.inter.pojo.FnService.queFnPercomb.QueFnPercombReq;
import com.maywide.biz.inter.pojo.FnService.queFnPercomb.QueFnPercombResp;
import com.maywide.biz.inter.pojo.FnService.queGoodsBySeri.Goods;
import com.maywide.biz.inter.pojo.FnService.queGoodsBySeri.QueGoodsBySeriReq;
import com.maywide.biz.inter.pojo.FnService.queInstallConfig.QueInstallConfigReq;
import com.maywide.biz.inter.pojo.FnService.saveOrUpdateFnConfig.SaveOrUpdateFnConfigReq;
import com.maywide.biz.inter.pojo.queSyncPercomb.*;
import com.maywide.biz.inter.pojo.querySalespkgKnowZs.QuerySalespkgKnowInterReqZs;
import com.maywide.biz.inter.pojo.querysalespkgknow.ImSalepkgKnowsBO;
import com.maywide.biz.inter.pojo.querysalespkgknow.QuerySalespkgKnowInterResp;
import com.maywide.biz.prv.entity.Biz5nMenu;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 5N波轮配置
 *
 * <AUTHOR>
 * @date 2021/11/23 0023
 */
@Service
public class FnService extends CommonService {

    @Autowired
    private InterPrdService interPrdService;
    @Autowired
    private ParamService paramService;

    public static final String ROOT_NODE = "5N_SYS_INSTALL_ROOT";

    /**
     * 5N 波轮受理菜单配置
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queFnMenuConfig(QueFnMenuConfigReq req, ArrayList resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        String city = loginInfo.getCity();
        String queSql = "select t1.* from biz_5n_menu_config t0 " +
                "left join biz_5n_menu t1 on t0.menu_id = t1.recid " +
                "where t0.city = ? and t1.recid is not null order by t0.sort ";

        // 先根据本地市查
        List<Biz5nMenu> list = getDAO().find(queSql, Biz5nMenu.class, city);
        if (list != null && !list.isEmpty()) {
            resp.addAll(list);
        } else {
            List<Biz5nMenu> commonlist = getDAO().find(queSql, Biz5nMenu.class, "*");
            resp.addAll(commonlist);
        }

        return returnInfo;
    }

    /**
     * 5N 波轮开户一键配置
     */
    public void fnOpDefault(QueSyncPercombResp resp, String rNid, String cNid, String bNid, String opcode) throws Exception {
        // 查询5N配置
        String queSql = "select * from biz_5n_install_config t where t.nodeid = ? and t.cnodeid = ? and t.bnodeid = ? and t.opcode = ?";
        List<BizFnInstallConfig> list = getDAO().find(queSql, BizFnInstallConfig.class, Long.valueOf(rNid), cNid, bNid, opcode);
        if (list != null && !list.isEmpty()) {
            Iterator<BizFnInstallConfig> iterator = list.iterator();
            while (iterator.hasNext()) {
                BizFnInstallConfig next = iterator.next();
                chargeDefault(next.getMcode(), next.getMdata(), next.getPercomb(), resp);
            }
        }

    }

    /**
     * 查询5N波轮一键开户业务组合
     *
     * @return
     */
    public TokenReturnInfo queFnPercomb(QueFnPercombReq req) throws Exception {
        TokenReturnInfo rt = new TokenReturnInfo();
        rt.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        rt.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        CheckUtils.checkEmpty(req.getOpmode(), "开户模式没有选择");
        String sql = "select t0.percomb, t0.combname from biz_percomb_cfg t0, sys_percomb_param t1 where t0.percomb = t1.PERCOMB " +
                "and t1.CITY = ? and find_in_set(?, t0.opmodes) > 0 order by t1.SORT";
        List<QueFnPercombResp> list = getDAO().find(sql, QueFnPercombResp.class, getLoginInfo().getCity(), req.getOpmode());
        // 查询是否有默认值
        String defSql = "select * from biz_5n_install_config b where b.nodeid = ? and b.cnodeid = ? " +
                "and b.bnodeid = ? and b.opcode = ? and b.mcode = 'PERCOMB'";
        List<BizFnInstallConfig> defList = getDAO().find(defSql, BizFnInstallConfig.class, Long.valueOf(req.getRootNodeId()),
                req.getcNodeId(), req.getbNodeId(), req.getOpmode());
        if (defList != null && !defList.isEmpty()) {
            BizFnInstallConfig config = defList.get(0);
            String percomb = config.getMdata();
            if (StringUtils.isNotBlank(percomb)) {
                Iterator<QueFnPercombResp> iterator = list.iterator();
                while (iterator.hasNext()) {
                    QueFnPercombResp next = iterator.next();
                    String per = next.getPercomb();
                    if (StringUtils.equals(per, percomb)) {
                        iterator.remove();
                        next.setCombname(next.getCombname() + " --默认");
                        list.add(0, next);
                        break;
                    }
                }
            }
        }

        rt.setData(list);
        return rt;
    }

    /**
     * 保存或者更新配置
     *
     * @param req
     * @return
     */
    public TokenReturnInfo saveOrUpdateFnConfig(SaveOrUpdateFnConfigReq req) throws Exception {
        TokenReturnInfo rt = new TokenReturnInfo();
        rt.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        rt.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        BizFnInstallConfig config = new BizFnInstallConfig();
        config.setNodeid(Long.valueOf(req.getRootNodeId()));
        config.setCnodeid(req.getcNodeId());
        config.setBnodeid(req.getbNodeId());
        config.setOpcode(req.getOpmode());
        config.setMcode(req.getMcode());
        if (!StringUtils.equals(req.getMcode(), "PERCOMB")) {
            config.setPercomb(req.getPercomb());
        }

        List<BizFnInstallConfig> list = getDAO().find(config);
        if (list != null && !list.isEmpty()) {
            // 更新
            config = list.get(0);
        }
        config.setPercomb(req.getPercomb());
        config.setMdata(req.getMdata());
        getDAO().saveOrUpdate(config);
        return rt;
    }

    /**
     * 查询需要的配置
     *
     * @return
     */
    public TokenReturnInfo queInstallConfig(QueInstallConfigReq req) throws Exception {
        TokenReturnInfo rt = new TokenReturnInfo();
        rt.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        rt.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        DataConfig config = new DataConfig();
        config.setEnd("N");
        config.setType(DataConfig.TYPE_STA);
        config.setNext(ROOT_NODE);

        List<InstallConfig> list = new ArrayList<>();
        queNextNode(config, list, req, "");

        rt.setData(list);
        return rt;
    }

    /**
     * 查询需要的配置
     *
     * @return
     */
    public TokenReturnInfo queConfigDef(SaveOrUpdateFnConfigReq req) throws Exception {
        TokenReturnInfo rt = new TokenReturnInfo();
        rt.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        rt.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        // 查询是否有默认值
        String defSql = "select * from biz_5n_install_config b where b.nodeid = ? and b.cnodeid = ? " +
                "and b.bnodeid = ? and b.opcode = ? and percomb = ? and b.mcode = ?";
        List<BizFnInstallConfig> defList = getDAO().find(defSql, BizFnInstallConfig.class, Long.valueOf(req.getRootNodeId()),
                req.getcNodeId(), req.getbNodeId(), req.getOpmode(), req.getPercomb(), req.getMcode());
        if (defList != null && !defList.isEmpty()) {
            rt.setData(defList.get(0));
        }
        return rt;
    }

    /**
     * 查询商品目录下的商品
     * @return
     */
    public TokenReturnInfo queGoodsBySeri(QueGoodsBySeriReq req) throws Exception {
        TokenReturnInfo rt = new TokenReturnInfo();
        rt.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        rt.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        CheckUtils.checkNull(req.getSeriesId(), "系列id不能为空");

        String queSql = "select t2.KNOWID, t2.KNOWNAME, 0 preid, 2 whether from goods_series_item t1, prd_salespkg_know t2 " +
                "where t1.knowid = t2.KNOWID and t1.series_id = ?  " +
                "union all  " +
                "select t2.KNOWID, t2.KNOWNAME, t1.knowid preid, t.whether from salespkg_additional t, goods_series_item t1, prd_salespkg_know t2 " +
                "where t.knowid = t1.knowid and t.additionalid = t2.KNOWID and t1.series_id = ?";

        List<Goods> list = getDAO().find(queSql, Goods.class, req.getSeriesId(), req.getSeriesId());
        if (list != null && !list.isEmpty()) {
            long id = 0;
            Iterator<Goods> iterator = list.iterator();
            while (iterator.hasNext()) {
                Goods next = iterator.next();
                id++;
                next.setId(req.getSeriesId() + "-" + id);
                if (next.getPreid().longValue() == 0) {
                    next.setChildren(getGoodsChildren(list, next.getKnowid()));
                } else {
                    iterator.remove();
                }
            }
        }

        rt.setData(list);
        return rt;
    }

    private List<Goods> getGoodsChildren(List<Goods> list, Long knowid) {
        List<Goods> chilren = null;
        Iterator<Goods> iterator = list.iterator();
        while (iterator.hasNext()) {
            Goods next = iterator.next();
            if (next.getPreid().longValue() == knowid.longValue()) {
                if (chilren == null) {
                    chilren = new ArrayList<>();
                }
                chilren.add(next);
            }
        }

        return chilren;
    }

    /**
     * 查询默认商品
     * @param req
     * @return
     * @throws Exception
     */
    public TokenReturnInfo queGoodsDef(SaveOrUpdateFnConfigReq req) throws Exception {
        TokenReturnInfo rt = new TokenReturnInfo();
        rt.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        rt.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        // 查询是否有默认值
        String defSql = "select * from biz_5n_install_config b where b.nodeid = ? and b.cnodeid = ? " +
                "and b.bnodeid = ? and b.opcode = ? and percomb = ? and b.mcode = ?";
        List<BizFnInstallConfig> defList = getDAO().find(defSql, BizFnInstallConfig.class, Long.valueOf(req.getRootNodeId()),
                req.getcNodeId(), req.getbNodeId(), req.getOpmode(), req.getPercomb(), req.getMcode());

        List<Goods> goods = new ArrayList<>();
        if (defList != null && !defList.isEmpty()) {
            BizFnInstallConfig config = defList.get(0);
            String mdata = config.getMdata();
            if (StringUtils.isNotBlank(mdata)) {
                String queGoodsDefSql = "select KNOWID, KNOWNAME from prd_salespkg_know t " +
                        "where t.KNOWID in (" + mdata + ")";
                List<Goods> list = getDAO().find(queGoodsDefSql, Goods.class);
                goods.addAll(list);
            }
        }

        rt.setData(goods);
        return rt;
    }

    /**
     * 查询需要的配置
     *
     * @return
     */
    public TokenReturnInfo queYkConfig(SaveOrUpdateFnConfigReq req) throws Exception {
        TokenReturnInfo rt = new TokenReturnInfo();
        rt.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        rt.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        // 查询是否有默认值
        String defSql = "select * from biz_5n_install_config b where b.nodeid = ? and b.cnodeid = ? " +
                "and b.bnodeid = ? and b.opcode = ? and percomb = ? and b.mcode = ?";
        List<BizFnInstallConfig> defList = getDAO().find(defSql, BizFnInstallConfig.class, Long.valueOf(req.getRootNodeId()),
                req.getcNodeId(), req.getbNodeId(), req.getOpmode(), req.getPercomb(), req.getMcode());

        String ykType = null;
        String ykAttr = null;
        if (defList != null && !defList.isEmpty()) {
            BizFnInstallConfig defConfig = defList.get(0);
            String mdata = defConfig.getMdata();
            String[] split = StringUtils.split(mdata, ",");
            if (split != null && split.length == 2) {
                ykType = split[0];
                ykAttr = split[1];
            }
        }

        // 查询遥控器类型
        List<InstallConfig> ykTypelist = null;

        LoginInfo loginInfo = getLoginInfo();
        Long areaid = loginInfo.getAreaid();
        List<PrvSysparam> ykTypeList = paramService.getData("RES_FITTINGTYPE");

        if (ykTypeList != null && !ykTypeList.isEmpty()) {
            PrvSysparam ykTypeParam = ykTypeList.get(0);
            String ykTypeSql = ykTypeParam.getData();
            ykTypelist = getDAO().find(ykTypeSql, InstallConfig.class, areaid);
            if (ykTypelist != null && !ykTypelist.isEmpty()) {
                List<PrvSysparam> ykAttrList = paramService.getData("RES_PIDBYSUBKIND");
                if (ykAttrList != null && !ykAttrList.isEmpty()) {
                    PrvSysparam ykAttrParam = ykAttrList.get(0);
                    String ykAttrsql = ykAttrParam.getData();
                    Iterator<InstallConfig> iterator = ykTypelist.iterator();
                    while (iterator.hasNext()) {
                        InstallConfig next = iterator.next();
                        String mcode = next.getMcode();
                        List<InstallConfig> ykAttrlist = getDAO().find(ykAttrsql, InstallConfig.class, mcode, areaid);
                        next.setChildren(ykAttrlist);
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(ykType) && StringUtils.isNotBlank(ykAttr)
                && ykTypelist != null && !ykTypelist.isEmpty()) {
            Iterator<InstallConfig> iterator = ykTypelist.iterator();
            while (iterator.hasNext()) {
                InstallConfig next = iterator.next();
                String mcode = next.getMcode();
                if (StringUtils.equals(mcode, ykType)) {
                    iterator.remove();
                    ykTypelist.add(0, next);
                    next.setMname(next.getMname() + " -默认");
                    List<InstallConfig> children = next.getChildren();
                    if (children != null && !children.isEmpty()) {
                        Iterator<InstallConfig> citer = children.iterator();
                        while (citer.hasNext()) {
                            InstallConfig cnext = citer.next();
                            String cmcode = cnext.getMcode();
                            if (StringUtils.equals(cmcode, ykAttr)) {
                                citer.remove();
                                children.add(0, cnext);
                                cnext.setMname(cnext.getMname() + " -默认");
                                break;
                            }
                        }
                    }
                    break;
                }
            }
        }

        rt.setData(ykTypelist);

        return rt;
    }

    private void queNextNode(DataConfig config, List<InstallConfig> list, QueInstallConfigReq req, String path) throws Exception {
        if (StringUtils.equals(config.getEnd(), "N")) {
            if (StringUtils.equals(config.getType(), DataConfig.TYPE_STA)) {
                String sql = "select * from prv_sysparam t where t.gcode = ? order by sort";
                List<PrvSysparam> paramList = getDAO().find(sql, PrvSysparam.class, config.getNext());
                if (paramList != null && !paramList.isEmpty()) {
                    Iterator<PrvSysparam> iterator = paramList.iterator();
                    while (iterator.hasNext()) {
                        PrvSysparam next = iterator.next();
                        InstallConfig installConfig = new InstallConfig();
                        installConfig.setMcode(next.getMcode());
                        installConfig.setMname(next.getMname());
                        String data = next.getData();
                        DataConfig nextConfig = JSON.parseObject(data, DataConfig.class);
                        installConfig.setEnd(StringUtils.equals(nextConfig.getEnd(), "Y") ? true : false);
                        installConfig.setType(nextConfig.getType());
                        installConfig.setPath(path + (StringUtils.isNotBlank(path) ? "/" + installConfig.getMname() : installConfig.getMname()));

                        List<InstallConfig> children = new ArrayList<>();
                        if (installConfig.isEnd()) {
                            installConfig.setData(children);
                        } else {
                            installConfig.setChildren(children);
                        }
                        queNextNode(nextConfig, children, req, installConfig.getPath());
                        if (!installConfig.isEnd() || (installConfig.getData() != null && !installConfig.getData().isEmpty())) {
                            list.add(installConfig);
                        }

                        if (StringUtils.equals(next.getMcode(), "YK_TYPE") || StringUtils.equals(next.getMcode(), "PRD_INFO")) {
                            list.add(installConfig);
                        }
                    }

                }

            } else if (StringUtils.equals(config.getType(), DataConfig.TYPE_SQL)) {
                // TODO SQL的支持以后在写
            }
        } else if (StringUtils.equals(config.getEnd(), "Y")) {
            if (StringUtils.equals(config.getType(), DataConfig.TYPE_STA)) {
                String sql = "select * from prv_sysparam t where t.gcode = ? order by sort";
                List<PrvSysparam> paramList = getDAO().find(sql, PrvSysparam.class, config.getData());
                if (paramList != null && !paramList.isEmpty()) {
                    Iterator<PrvSysparam> iterator = paramList.iterator();
                    while (iterator.hasNext()) {
                        PrvSysparam next = iterator.next();
                        InstallConfig installConfig = new InstallConfig();
                        installConfig.setMcode(next.getMcode());
                        installConfig.setMname(next.getMname());
                        list.add(installConfig);
                    }

                }

            } else if (StringUtils.equals(config.getType(), DataConfig.TYPE_SQL)) {
                String sql = config.getData();
                List params = new ArrayList();
                sql = formatSql(sql, params, req);
                List<InstallConfig> datas = getDAO().find(sql, InstallConfig.class, params.toArray());
                if (datas != null && !datas.isEmpty()) {
                    list.addAll(datas);
                }
            }
        }
    }

    private String formatSql(String sql, List params, QueInstallConfigReq req) throws Exception {
        String reg = "\\#\\{(.*?)\\}";
        Pattern p = Pattern.compile(reg);
        Matcher matcher = p.matcher(sql);
        while (matcher.find()) {
            String group = matcher.group();
            String group1 = matcher.group(1);
            sql = StringUtils.replaceOnce(sql, group, "?");
            params.add(getParamData(group1, req));
        }

        return sql;
    }

    private Object getParamData(String param, QueInstallConfigReq req) throws Exception {

        switch (param) {
            case "city":
                return getLoginInfo().getCity();
            case "percomb":
                return req.getPercomb();
        }
        return null;
    }

    private void chargeDefault(String mcode, String mdata, String percomb, QueSyncPercombResp resp) throws Exception {
        switch (mcode) {
            case "PERCOMB":
                // 业务组合默认
                setPercombDefault(resp, mdata);
                break;
            case "IS_CYCLE":
                // 是否回收设备
                setIsCycleDefault(resp, mdata, percomb);
                break;
            case "PRD_PCODESMLIST":
            case "PRD_PCODESTBLIST":
            case "PRD_PCODEGJLIST":
            case "PRD_PCODECMLIST":
            case "PRD_PCODEOTTLIST":
            case "RES_FITTINGTYPE":
            case "PRD_PCODEEOCLIST":
                // 设备来源
                setDevSourceDefault(mcode, mdata, percomb, resp);
                break;
            case "YK_TYPE":
                // 设置遥控器默认值
                setYkDefault(mdata, percomb, resp);
                break;
            case "YK_ATTR":
                // 设置遥控器默认值
                setYkAttrDefault(mdata, percomb, resp);
                break;
            case "SERV_TYPE":
                // 数字业务用户类型
                setServTypeDef(mdata, percomb, resp);
                break;
            case "FACE_TO_WORK":
                setFaceToWordDef(mdata, percomb, resp);
                break;
            case "STAGE":
                setStageDef(mdata, percomb, resp);
                break;
            case "P2_INPUT_WAY":
                setP2InputWay(mdata, percomb, resp);
                break;
            case "CONFIRM_WAY":
                setConfirmWay(mdata, percomb, resp);
                break;
            case "IP_WAY":
                setIpWay(mdata, percomb, resp);
                break;
            case "P3_INPUT_WAY":
                setP3InputWay(mdata, percomb, resp);
                break;
            case "USE_MAC_IN":
                setMacDef(mdata, percomb, resp);
                break;
            case "PAY_TPYE":
                setPayTpyeDef(mdata, percomb, resp);
                break;
            case "PRD_INFO":
                setPrdDef(mdata, percomb, resp);
                break;
        }
    }

    private void setYkAttrDefault(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    if (StringUtils.isNotBlank(mdata)) {
                        next.setYkAttr(mdata);
                    }
                    break;
                }
            }
        }
    }

    private void setPrdDef(String mdata, String percomb, QueSyncPercombResp resp) throws Exception {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    String[] split = StringUtils.split(mdata, ",");
                    if (split != null && split.length > 0) {
                        for (String knowid : split) {
                            QuerySalespkgKnowInterReqZs prdReq = new QuerySalespkgKnowInterReqZs();
                            prdReq.setKnowid(knowid);
                            prdReq.setOpcode("BIZ_USER_NEW");
                            QuerySalespkgKnowInterResp prdResp = new QuerySalespkgKnowInterResp();
                            interPrdService.querySalespkgKnowZs(prdReq, prdResp);
                            List<ImSalepkgKnowsBO> knows = prdResp.getKnows();
                            if (knows != null && !knows.isEmpty()) {
                                List<ImSalepkgKnowsBO> nextKnows = next.getKnows();
                                if (nextKnows != null) {
                                    nextKnows.addAll(knows);
                                } else {
                                    next.setKnows(knows);
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }
    }

    private void setPayTpyeDef(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    next.setPayType(mdata);
                    break;
                }
            }
        }
    }

    private void setMacDef(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombVodParam> vodParams = next.getVodParams();
                    if (vodParams != null && !vodParams.isEmpty()) {
                        PercombVodParam vodParam = vodParams.get(0);
                        String isinmac = vodParam.getIsinmac();
                        String[] split = StringUtils.split(isinmac, ",");
                        List<String> asList = Arrays.asList(split);
                        List<String> list = new ArrayList<>(asList);
                        Iterator<String> cables = list.iterator();
                        String caTmp = null;
                        while (cables.hasNext()) {
                            String ca = cables.next();
                            if (StringUtils.equals(ca, mdata)) {
                                cables.remove();
                                caTmp = ca;
                                break;
                            }
                        }
                        if (caTmp != null) {
                            list.add(0, mdata);
                        }
                        vodParam.setIsinmac(strListToString(list));
                    }
                    break;
                }
            }
        }
    }

    private void setP3InputWay(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombVodParam> vodParams = next.getVodParams();
                    if (vodParams != null && !vodParams.isEmpty()) {
                        PercombVodParam vodParam = vodParams.get(0);
                        List<PrvSysparam> inputWayList = vodParam.getInputWayList();
                        Iterator<PrvSysparam> iter = inputWayList.iterator();
                        PrvSysparam temp = null;
                        while (iter.hasNext()) {
                            PrvSysparam obj = iter.next();
                            String mcode = obj.getMcode();
                            if (StringUtils.equals(mcode, mdata)) {
                                temp = obj;
                                iter.remove();
                                break;
                            }
                        }
                        if (temp != null) {
                            inputWayList.add(0, temp);
                        }
                    }
                    break;
                }
            }
        }
    }

    private void setIpWay(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombCmParam> cmParams = next.getCmParams();
                    if (cmParams != null && !cmParams.isEmpty()) {
                        PercombCmParam cmParam = cmParams.get(0);
                        List<PrvSysparam> ipmodeList = cmParam.getIpmodeList();
                        Iterator<PrvSysparam> iter = ipmodeList.iterator();
                        PrvSysparam temp = null;
                        while (iter.hasNext()) {
                            PrvSysparam obj = iter.next();
                            String mcode = obj.getMcode();
                            if (StringUtils.equals(mcode, mdata)) {
                                temp = obj;
                                iter.remove();
                                break;
                            }
                        }
                        if (temp != null) {
                            ipmodeList.add(0, temp);
                        }
                    }
                    break;
                }
            }
        }
    }

    private void setConfirmWay(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombCmParam> cmParams = next.getCmParams();
                    if (cmParams != null && !cmParams.isEmpty()) {
                        PercombCmParam cmParam = cmParams.get(0);
                        List<PrvSysparam> authmodeList = cmParam.getAuthmodeList();
                        Iterator<PrvSysparam> iter = authmodeList.iterator();
                        PrvSysparam temp = null;
                        while (iter.hasNext()) {
                            PrvSysparam obj = iter.next();
                            String mcode = obj.getMcode();
                            if (StringUtils.equals(mcode, mdata)) {
                                temp = obj;
                                iter.remove();
                                break;
                            }
                        }
                        if (temp != null) {
                            authmodeList.add(0, temp);
                        }
                    }
                    break;
                }
            }
        }
    }

    private void setP2InputWay(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombCmParam> cmParams = next.getCmParams();
                    if (cmParams != null && !cmParams.isEmpty()) {
                        PercombCmParam cmParam = cmParams.get(0);
                        List<PrvSysparam> inputWayList = cmParam.getInputWayList();
                        Iterator<PrvSysparam> inputways = inputWayList.iterator();
                        PrvSysparam temp = null;
                        while (inputways.hasNext()) {
                            PrvSysparam inputway = inputways.next();
                            String mcode = inputway.getMcode();
                            if (StringUtils.equals(mcode, mdata)) {
                                temp = inputway;
                                inputways.remove();
                                break;
                            }
                        }
                        if (temp != null) {
                            inputWayList.add(0, temp);
                        }
                    }
                    break;
                }
            }
        }
    }

    private void setStageDef(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombDigitParam> digitParams = next.getDigitParams();
                    if (digitParams != null && !digitParams.isEmpty()) {
                        PercombDigitParam digitParam = digitParams.get(0);
                        List<PrvSysparam> platformList = digitParam.getPlatformList();
                        Iterator<PrvSysparam> plats = platformList.iterator();
                        PrvSysparam platTmp = null;
                        while (plats.hasNext()) {
                            PrvSysparam plat = plats.next();
                            String mcode = plat.getMcode();
                            if (StringUtils.equals(mcode, mdata)) {
                                platTmp = plat;
                                plats.remove();
                                break;
                            }
                        }
                        if (platTmp != null) {
                            platformList.add(0, platTmp);
                        }
                    }
                    break;
                }
            }
        }
    }

    private void setFaceToWordDef(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombDigitParam> digitParams = next.getDigitParams();
                    if (digitParams != null && !digitParams.isEmpty()) {
                        PercombDigitParam digitParam = digitParams.get(0);
                        String iscable = digitParam.getIscable();
                        if (StringUtils.isNotBlank(iscable)) {
                            String[] split = StringUtils.split(iscable, ",");
                            List<String> asList = Arrays.asList(split);
                            List<String> list = new ArrayList<>(asList);
                            Iterator<String> cables = list.iterator();
                            String caTmp = null;
                            while (cables.hasNext()) {
                                String ca = cables.next();
                                if (StringUtils.equals(ca, mdata)) {
                                    cables.remove();
                                    caTmp = ca;
                                    break;
                                }
                            }
                            if (caTmp != null) {
                                list.add(0, mdata);
                            }
                            digitParam.setIscable(strListToString(list));
                        }
                    }
                    break;
                }
            }
        }
    }

    private String strListToString(List<String> list) {
        StringBuffer buffer = new StringBuffer();
        for (String s : list) {
            if (buffer.length() > 0) {
                buffer.append(",");
            }
            buffer.append(s);
        }
        return buffer.toString();
    }

    private void setServTypeDef(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombDigitParam> digitParams = next.getDigitParams();
                    if (digitParams != null && !digitParams.isEmpty()) {
                        PercombDigitParam digitParam = digitParams.get(0);
                        List<PrvSysparam> servtypeList = digitParam.getServtypeList();
                        Iterator<PrvSysparam> servts = servtypeList.iterator();
                        PrvSysparam servTmp = null;
                        while (servts.hasNext()) {
                            PrvSysparam serv = servts.next();
                            String mcode = serv.getMcode();
                            if (StringUtils.equals(mcode, mdata)) {
                                servTmp = serv;
                                servts.remove();
                                break;
                            }
                        }
                        if (servTmp != null) {
                            servtypeList.add(0, servTmp);
                        }
                    }
                    break;
                }
            }
        }
    }

    private void setYkDefault(String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    if (StringUtils.isNotBlank(mdata)) {
                        String[] split = mdata.split(",");
                        if (split.length == 2) {
                            next.setYkType(split[0]);
                            next.setYkPrd(split[1]);
                        }
                    }
                    break;
                }
            }
        }
    }

    private void setIsCycleDefault(QueSyncPercombResp resp, String mdata, String percomb) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    next.setIsCyc(mdata);
                    break;
                }
            }
        }
    }

    private void setPercombDefault(QueSyncPercombResp resp, String mdata) {
        PercombDevParam percombDevParam = null;
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb = next.getPercomb();
                if (StringUtils.equals(mdata, percomb)) {
                    percombDevParam = next;
                    iterator.remove();
                    break;
                }
            }
        }

        if (percombDevParam != null) {
            datas.add(0, percombDevParam);
        }

    }

    private void setDevSourceDefault(String subClass, String mdata, String percomb, QueSyncPercombResp resp) {
        List<PercombDevParam> datas = resp.getDatas();
        if (datas != null && !datas.isEmpty()) {
            Iterator<PercombDevParam> iterator = datas.iterator();
            while (iterator.hasNext()) {
                PercombDevParam next = iterator.next();
                String percomb1 = next.getPercomb();
                if (StringUtils.equals(percomb1, percomb)) {
                    List<PercombDevInfo> devinfoList = next.getDevinfoList();
                    Iterator<PercombDevInfo> devInfoIterator = devinfoList.iterator();
                    while (devInfoIterator.hasNext()) {
                        PercombDevInfo devInfo = devInfoIterator.next();
                        String psubClass = devInfo.getPsubClass();
                        if (StringUtils.equals(subClass, psubClass)) {
                            List<PrvSysparam> sourceList = devInfo.getSourceList();
                            Iterator<PrvSysparam> sourceIter = sourceList.iterator();
                            PrvSysparam dev = null;
                            while (sourceIter.hasNext()) {
                                PrvSysparam sysparam = sourceIter.next();
                                String mcode = sysparam.getMcode();
                                if (StringUtils.equals(mcode, mdata)) {
                                    dev = sysparam;
                                    sourceIter.remove();
                                }
                            }
                            if (dev != null) {
                                sourceList.add(0, dev);
                            }
                            break;
                        }
                    }
                    break;
                }
            }
        }
    }

}
