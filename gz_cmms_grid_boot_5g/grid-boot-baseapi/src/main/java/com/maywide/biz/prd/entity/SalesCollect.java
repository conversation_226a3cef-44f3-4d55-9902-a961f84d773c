package com.maywide.biz.prd.entity;

import com.maywide.core.entity.PersistableEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.domain.Persistable;

import javax.persistence.*;
import java.util.Date;

/**
 * <p>
 * 商品对象
 * <p>
 * Create at 2016年6月6日
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "prd_sales_collect")
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class SalesCollect extends PersistableEntity<Long> implements Persistable<Long> {
    private Long id;
    private Long knowid;
    private Long catalogid;
    private Long operid;
    private Long deptid;
    private Date createtime;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Transient
    @Override
    public String getDisplay() {
        return knowid == null ? null : knowid.toString();
    }

    public Long getKnowid() {
        return knowid;
    }

    public void setKnowid(Long knowid) {
        this.knowid = knowid;
    }

    public Long getCatalogid() {
        return catalogid;
    }

    public void setCatalogid(Long catalogid) {
        this.catalogid = catalogid;
    }

    public Long getOperid() {
        return operid;
    }

    public void setOperid(Long operid) {
        this.operid = operid;
    }

    public Long getDeptid() {
        return deptid;
    }

    public void setDeptid(Long deptid) {
        this.deptid = deptid;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    @Override
    public String toString() {
        return "SalesCollect{" +
                "id=" + id +
                ", knowid=" + knowid +
                ", operid=" + operid +
                ", deptid=" + deptid +
                ", createtime=" + createtime +
                '}';
    }
}