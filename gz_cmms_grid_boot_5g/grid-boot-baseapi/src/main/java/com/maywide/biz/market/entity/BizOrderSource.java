package com.maywide.biz.market.entity;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;

@Entity
@Table(name = "biz_order_source")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class BizOrderSource  {
    private Long recid;

    private Long orderid;
    private String code;
    private String content;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long getRecid() {
        return recid;
    }

    public void setRecid(Long recid) {
        this.recid = recid;
    }

    public Long getOrderid() {
        return orderid;
    }

    public void setOrderid(Long orderid) {
        this.orderid = orderid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

}
