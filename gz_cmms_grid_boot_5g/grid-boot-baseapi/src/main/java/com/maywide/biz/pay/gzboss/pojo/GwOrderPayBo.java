package com.maywide.biz.pay.gzboss.pojo;

import java.io.Serializable;
import java.util.List;

public class GwOrderPayBo implements Serializable{
	private String bossorderno;	//boss的电子支付订单号
	private String serialno;
	private Long cust_id;
	private Long serv_id;
	private String access_num;	//手机号
	private String city;
	private String corp_org_code;	//中国广电的分公司编号
	private String op_code;	//中国广电的分公司编号
	private String org_code;	//中国广电的分公司编号
	private String channel_id;	//中国广电的分公司编号
	private String subject;	//订单标题
	private String payment_mode_id;	//支付类型
	private String business_type_id;	//业务类型
	private String out_trade_no;	//外部订单号, 由调用方自定义，仅支持字母、数字、下划线且需保证在不重复
	private List<GwOrderTradeBo> trade_list;	//订单交易列表



	//退款接口请求字段
	private String out_request_no;	//部分退款订单号, 由调用方自定义，仅支持字母、数字、下划线且需保证在不重复

	//支付订单请求字段
	private String bar_code;	//设备读取用户微信、支付宝、银联等条码或者二维码信息 微信、支付宝、银联条码支付时必传

	//内部传参用
	private Long payid;	//fgb_payorder.payid
	private String isGwOrder;	//是否国网订单，请求支付平台使用
	private String pos_id;	//国网pos支付-pos机编号，请求支付平台使用
	private String pay_method;	//国网pos支付-付款方式，请求支付平台使用
	private String shop_id;	//国网pos支付-分店编码，请求支付平台使用
	private String custareaid;
	private String servid;

	/******响应报文内容 开始******/
	private String pay_page_url;	//电脑网站收银台地址,电脑网站支付时返回
	private String order_no;	 //中国广电5G BOSS支付平台返回的订单号
	private String pay_status;	//支付状态
	private String trade_no;	//交易流水号，支付平台返回的交易号
	private String pay_date;	//支付日期, 格式:yyyy-MM-dd HH:mm:ss
	private String order_amount;	//订单支付金额, （单位：分）
	private String expire_date;	//订单过期时间, 格式:yyyy-MM-dd HH:mm:ss

	//支付订单接口返回
	private String scan_url;	//二维码地址,微信、支付宝、银联、国网侧POS机等二维码支付场景下返回
	private String mweb_url;	//H5支付跳转URL,微信、支付宝、银联等平台的H5支付场景下返回
	private String pay_no;	//支付平台交易号,前端调用支付宝、微信等支付时使用的交易号,现金支付时不返回
	private String sign_info;	//签名信息,前端App、小程序、H5支付调用支付平台SDK时需要的签名数据 在需要签名的场景下返回此字段,现金支付时不返回


	//退款响应字段
	private String refund_amount;	//退款金额（单位：分）
	private String refund_order_no;	//部分退款订单号, 由调用方自定义，仅支持字母、数字、下划线且需保证在不重复
	private String refund_trade_no;	//退款国网订单号

	//退款查询响应字段
	private String total_amount;	//交易订单总金额（单位：分）
	private String refund_status;	//退款状态。枚举值： REFUND_SUCCESS 退款处理成功； 未返回该字段表示退款请求未收到或者退款失败；
	/******响应报文内容 结束******/

	//<AUTHOR> 2022-08-09 搬微信支付宝支付代码
	private String ext_params;
	private String result_flag;

	private String cmms_status;

	public String getExt_params() {
		return ext_params;
	}

	public void setExt_params(String ext_params) {
		this.ext_params = ext_params;
	}

	public String getResult_flag() {
		return result_flag;
	}

	public void setResult_flag(String result_flag) {
		this.result_flag = result_flag;
	}

	public String getSerialno(){
		return serialno;
	}

	public String getRefund_order_no(){
		return refund_order_no;
	}

	public String getRefund_trade_no(){
		return refund_trade_no;
	}

	public void setRefund_order_no(String refund_order_no){
		this.refund_order_no = refund_order_no;
	}

	public void setRefund_trade_no(String refund_trade_no){
		this.refund_trade_no = refund_trade_no;
	}

	public String getPos_id(){
		return pos_id;
	}

	public String getPay_method(){
		return pay_method;
	}

	public String getShop_id(){
		return shop_id;
	}

	public void setPos_id(String pos_id){
		this.pos_id = pos_id;
	}

	public void setPay_method(String pay_method){
		this.pay_method = pay_method;
	}

	public void setShop_id(String shop_id){
		this.shop_id = shop_id;
	}

	public String getOp_code(){
		return op_code;
	}

	public String getOrg_code(){
		return org_code;
	}

	public String getChannel_id(){
		return channel_id;
	}

	public void setOp_code(String op_code){
		this.op_code = op_code;
	}

	public void setOrg_code(String org_code){
		this.org_code = org_code;
	}

	public void setChannel_id(String channel_id){
		this.channel_id = channel_id;
	}

	public String getIsGwOrder(){
		return isGwOrder;
	}

	public String getCustareaid(){
		return custareaid;
	}

	public String getServid(){
		return servid;
	}

	public void setIsGwOrder(String isGwOrder){
		this.isGwOrder = isGwOrder;
	}

	public void setCustareaid(String custareaid){
		this.custareaid = custareaid;
	}

	public void setServid(String servid){
		this.servid = servid;
	}

	public Long getPayid(){
		return payid;
	}

	public void setPayid(Long payid){
		this.payid = payid;
	}

	public String getBar_code(){
		return bar_code;
	}

	public void setBar_code(String bar_code){
		this.bar_code = bar_code;
	}

	public String getBossorderno(){
		return bossorderno;
	}

	public void setBossorderno(String bossorderno){
		this.bossorderno = bossorderno;
	}

	public String getScan_url(){
		return scan_url;
	}
	public String getMweb_url(){
		return mweb_url;
	}
	public String getPay_no(){
		return pay_no;
	}
	public String getSign_info(){
		return sign_info;
	}
	public void setScan_url(String scan_url){
		this.scan_url = scan_url;
	}
	public void setMweb_url(String mweb_url){
		this.mweb_url = mweb_url;
	}
	public void setPay_no(String pay_no){
		this.pay_no = pay_no;
	}
	public void setSign_info(String sign_info){
		this.sign_info = sign_info;
	}
	public String getPay_page_url(){
		return pay_page_url;
	}
	public String getOrder_no(){
		return order_no;
	}
	public void setPay_page_url(String pay_page_url){
		this.pay_page_url = pay_page_url;
	}
	public void setOrder_no(String order_no){
		this.order_no = order_no;
	}
	public String getTotal_amount(){
		return total_amount;
	}
	public String getRefund_status(){
		return refund_status;
	}
	public void setTotal_amount(String total_amount){
		this.total_amount = total_amount;
	}
	public void setRefund_status(String refund_status){
		this.refund_status = refund_status;
	}
	public String getOut_request_no(){
		return out_request_no;
	}
	public String getRefund_amount(){
		return refund_amount;
	}
	public void setOut_request_no(String out_request_no){
		this.out_request_no = out_request_no;
	}
	public void setRefund_amount(String refund_amount){
		this.refund_amount = refund_amount;
	}
	public String getCity(){
		return city;
	}
	public void setCity(String city){
		this.city = city;
	}

	public String getPay_status(){
		return pay_status;
	}
	public String getTrade_no(){
		return trade_no;
	}
	public String getPay_date(){
		return pay_date;
	}
	public String getOrder_amount(){
		return order_amount;
	}
	public String getExpire_date(){
		return expire_date;
	}

	public void setPay_status(String pay_status){
		this.pay_status = pay_status;
	}
	public void setTrade_no(String trade_no){
		this.trade_no = trade_no;
	}
	public void setPay_date(String pay_date){
		this.pay_date = pay_date;
	}
	public void setOrder_amount(String order_amount){
		this.order_amount = order_amount;
	}
	public void setExpire_date(String expire_date){
		this.expire_date = expire_date;
	}
	public Long getCust_id(){
		return cust_id;
	}
	public Long getServ_id(){
		return serv_id;
	}
	public String getAccess_num(){
		return access_num;
	}

	public String getCorp_org_code(){
		return corp_org_code;
	}
	public String getSubject(){
		return subject;
	}
	public String getPayment_mode_id(){
		return payment_mode_id;
	}
	public String getOut_trade_no(){
		return out_trade_no;
	}
	public List<GwOrderTradeBo> getTrade_list(){
		return trade_list;
	}
	public void setSerialno(String serialno){
		this.serialno = serialno;
	}
	public void setCust_id(Long cust_id){
		this.cust_id = cust_id;
	}
	public void setServ_id(Long serv_id){
		this.serv_id = serv_id;
	}
	public void setAccess_num(String access_num){
		this.access_num = access_num;
	}

	public void setCorp_org_code(String corp_org_code){
		this.corp_org_code = corp_org_code;
	}
	public void setSubject(String subject){
		this.subject = subject;
	}
	public void setPayment_mode_id(String payment_mode_id){
		this.payment_mode_id = payment_mode_id;
	}
	public void setOut_trade_no(String out_trade_no){
		this.out_trade_no = out_trade_no;
	}
	public void setTrade_list(List<GwOrderTradeBo> trade_list){
		this.trade_list = trade_list;
	}

	public String getBusiness_type_id(){
		return business_type_id;
	}

	public void setBusiness_type_id(String business_type_id){
		this.business_type_id = business_type_id;
	}

	public String getCmms_status() {
		return cmms_status;
	}

	public void setCmms_status(String cmms_status) {
		this.cmms_status = cmms_status;
	}
}
