package com.maywide.biz.inter.pojo.install;

public class InstallParams implements java.io.Serializable{
	private String houseid          ; 
	private String addr             ; 
	private String patchid          ; 
	private String name             ; 
	private String cardtype         ; 
	private String cardno           ; 
	private String linkman          ; 
	private String linkphone        ; 
	private String mobile           ; 
	private String permark          ; 
	private String omode            ; 
	private String feekind          ; 
	private String servtype         ; 
	private String pservid          ; 
	private String servid           ; 
	private String oservid          ;
	private String bankid           ; 
	private String ologicdevno      ; 
	private String devback          ; 
	private String stbback          ; 
	private String predate          ; 
	private String logicdevno       ; 
	private String stbno            ; 
	private String percomb          ;
	private String memo             ;
	private String smnouseprop      ;
	private String stbuseprop       ;
	private String cmno				;
	private String maindevno		;
	
	public String getSmnouseprop() {
		return smnouseprop;
	}
	public void setSmnouseprop(String smnouseprop) {
		this.smnouseprop = smnouseprop;
	}
	public String getStbuseprop() {
		return stbuseprop;
	}
	public void setStbuseprop(String stbuseprop) {
		this.stbuseprop = stbuseprop;
	}
	public String getHouseid() {
		return houseid;
	}
	public void setHouseid(String houseid) {
		this.houseid = houseid;
	}
	public String getAddr() {
		return addr;
	}
	public void setAddr(String addr) {
		this.addr = addr;
	}
	public String getPatchid() {
		return patchid;
	}
	public void setPatchid(String patchid) {
		this.patchid = patchid;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCardtype() {
		return cardtype;
	}
	public void setCardtype(String cardtype) {
		this.cardtype = cardtype;
	}
	public String getCardno() {
		return cardno;
	}
	public void setCardno(String cardno) {
		this.cardno = cardno;
	}
	public String getLinkman() {
		return linkman;
	}
	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}
	public String getLinkphone() {
		return linkphone;
	}
	public void setLinkphone(String linkphone) {
		this.linkphone = linkphone;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getPermark() {
		return permark;
	}
	public void setPermark(String permark) {
		this.permark = permark;
	}
	public String getOmode() {
		return omode;
	}
	public void setOmode(String omode) {
		this.omode = omode;
	}
	public String getFeekind() {
		return feekind;
	}
	public void setFeekind(String feekind) {
		this.feekind = feekind;
	}
	public String getServtype() {
		return servtype;
	}
	public void setServtype(String servtype) {
		this.servtype = servtype;
	}
	public String getPservid() {
		return pservid;
	}
	public void setPservid(String pservid) {
		this.pservid = pservid;
	}
	public String getServid() {
		return servid;
	}
	public void setServid(String servid) {
		this.servid = servid;
	}
	
	public String getOservid() {
		return oservid;
	}
	public void setOservid(String oservid) {
		this.oservid = oservid;
	}
	public String getBankid() {
		return bankid;
	}
	public void setBankid(String bankid) {
		this.bankid = bankid;
	}
	public String getOlogicdevno() {
		return ologicdevno;
	}
	public void setOlogicdevno(String ologicdevno) {
		this.ologicdevno = ologicdevno;
	}
	public String getDevback() {
		return devback;
	}
	public void setDevback(String devback) {
		this.devback = devback;
	}
	public String getStbback() {
		return stbback;
	}
	public void setStbback(String stbback) {
		this.stbback = stbback;
	}
	public String getPredate() {
		return predate;
	}
	public void setPredate(String predate) {
		this.predate = predate;
	}
	public String getLogicdevno() {
		return logicdevno;
	}
	public void setLogicdevno(String logicdevno) {
		this.logicdevno = logicdevno;
	}
	public String getStbno() {
		return stbno;
	}
	public void setStbno(String stbno) {
		this.stbno = stbno;
	}
	
	public String getPercomb() {
		return percomb;
	}
	public void setPercomb(String percomb) {
		this.percomb = percomb;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getCmno() {
		return cmno;
	}
	public void setCmno(String cmno) {
		this.cmno = cmno;
	}
	public String getMaindevno() {
		return maindevno;
	}
	public void setMaindevno(String maindevno) {
		this.maindevno = maindevno;
	} 

	
	
	
}
