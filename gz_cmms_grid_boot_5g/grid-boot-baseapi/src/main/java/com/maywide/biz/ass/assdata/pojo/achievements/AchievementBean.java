package com.maywide.biz.ass.assdata.pojo.achievements;

import java.util.Date;

public class AchievementBean {

	private Long recid;
	
	private String city;
	
	private Date cyclenum;
	
	private String fielid;
	
	private String viSql;
	
	private String gridcode;
	
	private Integer assDate;

	//网格类型  20210524 minchan
	private Long gtype;
	
	public Long getRecid() {
		return recid;
	}

	public void setRecid(Long recid) {
		this.recid = recid;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}


	public Date getCyclenum() {
		return cyclenum;
	}

	public void setCyclenum(Date cyclenum) {
		this.cyclenum = cyclenum;
	}

	public String getFielid() {
		return fielid;
	}

	public void setFielid(String fielid) {
		this.fielid = fielid;
	}

	public String getViSql() {
		return viSql;
	}

	public void setViSql(String viSql) {
		this.viSql = viSql;
	}

	public String getGridcode() {
		return gridcode;
	}

	public void setGridcode(String gridcode) {
		this.gridcode = gridcode;
	}

	public Integer getAssDate() {
		return assDate;
	}

	public void setAssDate(Integer assDate) {
		this.assDate = assDate;
	}

	public Long getGtype() {
		return gtype;
	}

	public void setGtype(Long gtype) {
		this.gtype = gtype;
	}
}
