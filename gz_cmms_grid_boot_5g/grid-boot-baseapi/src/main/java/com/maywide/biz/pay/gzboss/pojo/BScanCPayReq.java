package com.maywide.biz.pay.gzboss.pojo;

import com.maywide.biz.core.pojo.api.BaseApiRequest;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class BScanCPayReq extends BaseApiRequest {

    /*支付*/
    public static final String PAYCODE_PAY = "1";
    /*退款*/
    public static final String PAYCODE_REFUND = "0";

    /**
     * 支付场景：1支付、0退款
     */
    private String payCode;
    private PayInfo payInfo;

    private Map<String, Object> custInfo;

    private Object bizCustomInfo;

    public String getPayCode() {
        return payCode;
    }

    public void setPayCode(String payCode) {
        this.payCode = payCode;
    }

    public PayInfo getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(PayInfo payInfo) {
        this.payInfo = payInfo;
    }

    public Map<String, Object> getCustInfo() {
        return custInfo;
    }

    public void setCustInfo(Map<String, Object> custInfo) {
        this.custInfo = custInfo;
    }

    public Object getBizCustomInfo() {
        return bizCustomInfo;
    }

    public void setBizCustomInfo(Object bizCustomInfo) {
        this.bizCustomInfo = bizCustomInfo;
    }
}
