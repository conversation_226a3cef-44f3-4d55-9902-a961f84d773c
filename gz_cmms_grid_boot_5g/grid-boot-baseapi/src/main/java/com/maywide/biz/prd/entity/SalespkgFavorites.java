package com.maywide.biz.prd.entity;

import com.maywide.core.annotation.MetaData;
import com.maywide.core.entity.PersistableEntity;
import com.maywide.core.entity.annotation.EntityAutoCode;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.domain.Persistable;

import javax.persistence.*;

@Entity
@Table(name = "PRD_SALESPKG_FAVORITES")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class SalespkgFavorites extends PersistableEntity<Long> implements Persistable<Long> {

    @MetaData(value = "id")
    @EntityAutoCode
    private Long id;

    @MetaData(value = "knowid")
    @EntityAutoCode
    private Long knowid;

    @MetaData(value = "操作员")
    @EntityAutoCode
    private Long operid;


    @Override
    @Transient
    public String getDisplay() {
        return null;
    }


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false, unique = false, insertable = true, updatable = true)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "KNOWID", nullable = true, unique = false, insertable = true, updatable = true)
    public Long getKnowid() {
        return knowid;
    }

    public void setKnowid(Long knowid) {
        this.knowid = knowid;
    }

    @Column(name = "OPERID", nullable = true, unique = false, insertable = true, updatable = true)
    public Long getOperid() {
        return operid;
    }

    public void setOperid(Long operid) {
        this.operid = operid;
    }
}
