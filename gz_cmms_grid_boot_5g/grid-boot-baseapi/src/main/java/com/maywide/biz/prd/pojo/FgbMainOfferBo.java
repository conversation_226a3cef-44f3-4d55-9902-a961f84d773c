package com.maywide.biz.prd.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.maywide.biz.prd.entity.FgbMainOfferDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Persistable;

import javax.persistence.Transient;
import java.io.Serializable;

public class FgbMainOfferBo extends FgbMainOfferDO implements Persistable<Long> {
    private Long salesid;
    private String offertype;
    private String unit;
    private String price;


    public Long getSalesid() {
        return salesid;
    }

    public void setSalesid(Long salesid) {
        this.salesid = salesid;
    }

    public String getOffertype() {
        return offertype;
    }

    public void setOffertype(String offertype) {
        this.offertype = offertype;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    @Override
    public Long getId() {
        return salesid;
    }

    @Override
    @Transient
    @JsonIgnore
    public boolean isNew() {
        Serializable id = getId();
        return id == null || StringUtils.isBlank(String.valueOf(id));
    }
}
