package com.maywide.biz.pay.gzboss.pojo;


import java.util.List;

/**
 * @ClassName GwPayOrderInfoBo
 * @description:
 * @author: ZHEMING.Mai
 * @create: 2022-09-14 17:02
 **/

public class GwPayOrderInfoBo {
    private String access_num;
    private String order_amount	;//订单支付金额
    private String bossorderno	;//Boss支付临时订单号
    private String order_no	;//中国广电5G BOSS支付平台返回的订单号
    private String out_trade_no	;//外部订单号
    private String pay_date	;//支付日期
    private String pay_no	;//支付平台交易号
    private String pay_status	;//支付状态
    private String payment_mode_id	;//支付类型
    private String trade_no	;//中国广电5G BOSS支付平台返回的交易流水号

    private List<TradeOrderInfoBo> trade_list	;//订单交易列表

    public String getAccess_num() {
        return access_num;
    }

    public void setAccess_num(String access_num) {
        this.access_num = access_num;
    }

    public String getOrder_amount() {
        return order_amount;
    }

    public void setOrder_amount(String order_amount) {
        this.order_amount = order_amount;
    }

    public String getBossorderno() {
        return bossorderno;
    }

    public void setBossorderno(String bossorderno) {
        this.bossorderno = bossorderno;
    }

    public String getOrder_no() {
        return order_no;
    }

    public void setOrder_no(String order_no) {
        this.order_no = order_no;
    }

    public String getOut_trade_no() {
        return out_trade_no;
    }

    public void setOut_trade_no(String out_trade_no) {
        this.out_trade_no = out_trade_no;
    }

    public String getPay_date() {
        return pay_date;
    }

    public void setPay_date(String pay_date) {
        this.pay_date = pay_date;
    }

    public String getPay_no() {
        return pay_no;
    }

    public void setPay_no(String pay_no) {
        this.pay_no = pay_no;
    }

    public String getPay_status() {
        return pay_status;
    }

    public void setPay_status(String pay_status) {
        this.pay_status = pay_status;
    }

    public String getPayment_mode_id() {
        return payment_mode_id;
    }

    public void setPayment_mode_id(String payment_mode_id) {
        this.payment_mode_id = payment_mode_id;
    }

    public String getTrade_no() {
        return trade_no;
    }

    public void setTrade_no(String trade_no) {
        this.trade_no = trade_no;
    }

    public List<TradeOrderInfoBo> getTrade_list() {
        return trade_list;
    }

    public void setTrade_list(List<TradeOrderInfoBo> trade_list) {
        this.trade_list = trade_list;
    }


    public static class TradeOrderInfoBo{
        private String  detail_id;//交易明细ID
        private String  peer_trade_id;//子订单号
        private String trade_desc;//	子订单描述
        private String trade_amount; //交易明细金额 单位：分

        private List<ItemOrderInfoBo> item_list;//	订单费用明细列表

        public String getDetail_id() {
            return detail_id;
        }

        public void setDetail_id(String detail_id) {
            this.detail_id = detail_id;
        }

        public String getPeer_trade_id() {
            return peer_trade_id;
        }

        public void setPeer_trade_id(String peer_trade_id) {
            this.peer_trade_id = peer_trade_id;
        }

        public String getTrade_desc() {
            return trade_desc;
        }

        public void setTrade_desc(String trade_desc) {
            this.trade_desc = trade_desc;
        }

        public List<ItemOrderInfoBo> getItem_list() {
            return item_list;
        }

        public void setItem_list(List<ItemOrderInfoBo> item_list) {
            this.item_list = item_list;
        }

        public String getTrade_amount() {
            return trade_amount;
        }

        public void setTrade_amount(String trade_amount) {
            this.trade_amount = trade_amount;
        }
    }

    public static class ItemOrderInfoBo{
        private String  detail_id;//交易明细ID
        private String  peer_item_id;//费用项id
        private String  item_fee;//	支付金额
        private String  item_id;//	费用明细ID
        private String  item_num;//费用明细序号
        private String  item_code;//费用编码
        private String  item_type;//费用类型
        private String  item_name;//费用名称
        public String getDetail_id() {
            return detail_id;
        }

        public void setDetail_id(String detail_id) {
            this.detail_id = detail_id;
        }

        public String getPeer_item_id() {
            return peer_item_id;
        }

        public void setPeer_item_id(String peer_item_id) {
            this.peer_item_id = peer_item_id;
        }

        public String getItem_fee() {
            return item_fee;
        }

        public void setItem_fee(String item_fee) {
            this.item_fee = item_fee;
        }

        public String getItem_id() {
            return item_id;
        }

        public void setItem_id(String item_id) {
            this.item_id = item_id;
        }

        public String getItem_num() {
            return item_num;
        }

        public void setItem_num(String item_num) {
            this.item_num = item_num;
        }

        public String getItem_code() {
            return item_code;
        }

        public void setItem_code(String item_code) {
            this.item_code = item_code;
        }

        public String getItem_type() {
            return item_type;
        }

        public void setItem_type(String item_type) {
            this.item_type = item_type;
        }

        public String getItem_name() {
            return item_name;
        }

        public void setItem_name(String item_name) {
            this.item_name = item_name;
        }
    }

}
