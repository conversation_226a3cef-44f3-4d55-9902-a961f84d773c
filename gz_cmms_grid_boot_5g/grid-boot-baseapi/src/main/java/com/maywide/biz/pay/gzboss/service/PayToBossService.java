package com.maywide.biz.pay.gzboss.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.inter.pojo.chargeFeeBook.QuePayOrderResp;
import com.maywide.biz.market.entity.BizPortalOrder;
import com.maywide.biz.market.entity.CustOrder;
import com.maywide.biz.pay.gzboss.pojo.*;
import com.maywide.biz.pay.unify.entity.PayRecordLog;
import com.maywide.biz.pay.unify.pojo.UnifyPayCondition;
import com.maywide.biz.pay.unify.service.PayNoticeService;
import com.maywide.biz.pay.unify.service.UnifyPayService;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 贵州BOSS支付
 *
 * <AUTHOR>
 */
@Service
public class PayToBossService extends CommonService {

    @Autowired
    private PayNoticeService noticeService;
    @Autowired
    private UnifyPayService unifyPayService;

    /**
     * 贵州BOSS支付接口，扫客户支付码
     *
     * @param req
     * @param resp
     * @return
     */
    public synchronized ReturnInfo bScanCToPayByBoss(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getPayType(), "CMMS:支付方式为空");
        CheckUtils.checkEmpty(req.getAuthCode(), "CMMS:支付条码为空");
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        CheckUtils.checkNull(req.getCondition(), "CMMS:支付条件为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }

        // 2. 获取流水号
        Long snowflakeOrderId = getSnowflakeOrderId();
        custOrder.getPortalOrder().setPayid(snowflakeOrderId);
        log.info("=>[{}]支付流失号: {}", req.getCustorderid(), snowflakeOrderId);
        // 3. 初始化支付记录
        PayRecordLog recordLog = initPayRecordLog(snowflakeOrderId, custOrder, PayRecordLog.ACTION_TYPE_PAY);
        // 4. 封装BOSS请求
        BScanCPayReq bossreq = new BScanCPayReq();
        bossreq.setBizorderid(req.getBizorderid());
        bossreq.setPayCode(BScanCPayReq.PAYCODE_PAY);
        bossreq.setPayInfo(new PayInfo());
        bossreq.setCustInfo(new HashMap<>());
        bossreq.getPayInfo().setPayType(req.getPayType());
        bossreq.getPayInfo().setMchntOrderNo(snowflakeOrderId.toString());
        bossreq.getPayInfo().setAuthCode(req.getAuthCode());
        bossreq.getPayInfo().setAmount(getAmount(custOrder.getPortalOrder()));
        bossreq.getCustInfo().put("custName", custOrder.getName());
        bossreq.getCustInfo().put("cust_id", custOrder.getCustid());
        bossreq.getCustInfo().put("custareaid", custOrder.getAreaid());
        bossreq.getCustInfo().put("city", custOrder.getCity());

        String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(req.getCustorderid());
        if (StringUtils.isNotBlank(gwPayOrderInfo)) {
            JSONObject customInfo = JSONObject.parseObject(gwPayOrderInfo);
            bossreq.setBizCustomInfo(customInfo);
            bossreq.getCustInfo().put("servid", customInfo.get("servid"));
        }

        // 5. 调用BOSS接口支付
        try {
            log.info("[{}]调用BOSS接口支付.", req.getCustorderid());
            BScanCPayResp payResp = bScanCPay(bossreq, loginInfo);
            if (payResp != null) {
                String tranStatus = payResp.getStatus();
                if (StringUtils.equals(tranStatus, BScanCPayResp.STATUS_FAIL)) {
                    throw new BusinessException("CMMS:支付失败.");
                }
            }

            // 将bossorderno补充到支付完成的数据里面
            JSONObject customInfo = payResp.getBizCustomInfo();
            if (customInfo != null && bossreq.getBizCustomInfo() != null) {
                JSONObject bizCustomInfo = (JSONObject) bossreq.getBizCustomInfo();
                customInfo.put("bossorderno", bizCustomInfo.getString("bossorderno"));
            }

            // 6. 支付成功，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYED);
            recordLog.setPaydate(new Date());
            recordLog.setRedicetStr(JSON.toJSONString(payResp));
            updatePayRecordLog(recordLog);

            // 更新biz_portal_order表的payid字段
            getDAO().executeSql("update biz_portal_order set payid = ? where orderid = ?",
                    custOrder.getPortalOrder().getPayid(), custOrder.getId());

            BeanUtils.copyProperties(resp, payResp);
        } catch (Exception e) {
            log.error(String.format("=>[%s]-[%s]支付失败", req.getCustorderid(), snowflakeOrderId), e);
            // 6. 支付失败，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYFAIL);
            recordLog.setPaydate(new Date());
            recordLog.setBizexmsg(e.getMessage());
            updatePayRecordLog(recordLog);
            throw new BusinessException(e.getMessage());
        }

        log.info("=>[{}]订单确认.", req.getCustorderid());
        // 7. 订单确认
        bossOrderCommit(custOrder, resp, req.getCondition(), loginInfo);

        return returnInfo;
    }

    /**
     * 订单确认
     *
     * @param custOrder
     * @param resp
     * @param condition
     * @param loginInfo
     */
    private void bossOrderCommit(CustOrder custOrder, BScanCPayResp resp, UnifyPayCondition condition, LoginInfo loginInfo) throws Exception {
        String biztype = condition.getBizType();
        String extra = condition.getExtra();
        log.info("=========start biz business======");

        try {
            QuePayOrderResp order = new QuePayOrderResp();
            order.setOrderNo(custOrder.getId().toString());
            order.setPaySerialNo(resp.getOrderNo());
            order.setPayway(resp.getPayway());
            order.setBossPaycode(resp.getPaycode());
            order.setTotalFee(Double.parseDouble(custOrder.getPortalOrder().getFees()));

            Object bizCustomInfo = resp.getBizCustomInfo();
            JSONObject bizCustomInfoJson = JSONObject.parseObject(JSON.toJSONString(bizCustomInfo));

            if (BizConstant.BizOpcode.BIZ_UP_NOTPAIDFEES.equals(custOrder.getOpcode())) {
                noticeService.bizConfirmaPay(order, extra);
            } else if (BizConstant.BizOpcode.BIZ_USER_NEW.equals(custOrder.getOpcode())) {
                noticeService.bizInstallCommit(order, extra, null, null, bizCustomInfoJson);
            } else if (BizConstant.BizOpcode.BIZ_PARTS_SALES.equals(custOrder.getOpcode())) {
                noticeService.bizSaleCommit(custOrder.getId().toString(), order, extra, null, null);
            } else if (BizConstant.BizOpcode.BIZ_CUSTADDR_CHG.equals(custOrder.getOpcode())) {
                noticeService.bizCustAddrCommit(custOrder.getId().toString(), order, null, null);
            } else if (BizConstant.PAY_BIZ_TYPE.ORDER.equals(biztype)
                    || BizConstant.BossInterfaceService.BIZ_FEEIN.equals(biztype)
                    || BizConstant.BizOpcode.BIZ_SALES_TERMINAL.equals(custOrder.getOpcode())
                    || BizConstant.BizOpcode.BIZ_CHL_RESTART.equals(custOrder.getOpcode())) {
                noticeService.orderCommit(order, extra, null, null, bizCustomInfoJson);
            } else if (BizConstant.PAY_BIZ_TYPE.CHG_DEV.equals(biztype)) {
                noticeService.changeDevice(order, extra, null, null);
            } else {
                throw new BusinessException("传入的业务类型不正确！");
            }
        } catch (Exception e) {
            log.error(String.format("=>[%s]订单确认异常:", custOrder.getId()), e);
            // 订单确认异常, 发起退款
            PayRecordLog recordLog = initPayRecordLog(custOrder.getPortalOrder().getPayid(), custOrder, PayRecordLog.ACTION_TYPE_REFUND);

            try {
                // 请求BOSS接口回退
                ReverseResp reverse = reverse(resp.getOrderNo(), loginInfo, getBizorderid());
                if (reverse == null || StringUtils.equals(reverse.getStatus(), ReverseResp.STATUS_FAIL)) {
                    throw new BusinessException(String.format("退款失败: %s", reverse.getMessage()));
                }
                // 回退成功
                recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUND);
                recordLog.setPaydate(new Date());
                recordLog.setRedicetStr(JSON.toJSONString(reverse));
                recordLog.setBizexmsg(e.getMessage());
                updatePayRecordLog(recordLog);
            } catch (Exception ex) {
                recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUNDFAIL);
                recordLog.setPaydate(new Date());
                recordLog.setBizexmsg(ex.getMessage());
                updatePayRecordLog(recordLog);
                throw new BusinessException(String.format("CMMS:订单确认发生异常: %s, 支付退款异常: %s", e.getMessage(), ex.getMessage()));
            }
            throw new BusinessException(String.format("CMMS:订单确认发生异常, 钱已退款: %s", e.getMessage()));
        }
    }

    /**
     * @param recordLog
     * @throws Exception
     */
    private void updatePayRecordLog(PayRecordLog recordLog) throws Exception {
        getDAO().saveOrUpdate(recordLog);
    }

    private String getAmount(BizPortalOrder portalOrder) {
        String fees = portalOrder.getFees();
        BigDecimal feesYuan = new BigDecimal(fees);
        BigDecimal feesFen = feesYuan.multiply(new BigDecimal("100"));

        return feesFen.intValue() + "";
    }

    private PayRecordLog initPayRecordLog(Long orderid, CustOrder custOrder, String actionType) throws Exception {
        CheckUtils.checkEmpty(custOrder.getPortalOrder().getFees(), "CMMS:支付金额为空.");

        PayRecordLog recordLog = new PayRecordLog();
        recordLog.setActionType(actionType);
        recordLog.setOrderid(orderid);
        recordLog.setCustorderid(custOrder.getId());
        recordLog.setPaydate(new Date());
        recordLog.setPayresult(PayRecordLog.PAY_RESULT_INIT);
        recordLog.setFees(custOrder.getPortalOrder().getFees());
        getDAO().save(recordLog);
        return recordLog;
    }

    private CustOrder getCustOrder(String custorderid) throws Exception {
        CustOrder custOrder = new CustOrder();
        custOrder.setId(Long.valueOf(custorderid));
        List<CustOrder> list = getDAO().find(custOrder);
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    public BScanCPayResp bScanCPay(BScanCPayReq req, LoginInfo loginInfo) throws Exception {
        CheckUtils.checkEmpty(req.getPayCode(), "CMMS: 支付场景为空.");
        CheckUtils.checkNull(req.getPayInfo(), "CMMS: 支付信息为空");
        String payCode = req.getPayCode();
        if (StringUtils.equals(payCode, BScanCPayReq.PAYCODE_REFUND)) {
            // 退款
            CheckUtils.checkEmpty(req.getPayInfo().getOrderNo(), "CMMS:退款时支付订单号为空.");
        } else if (StringUtils.equals(payCode, BScanCPayReq.PAYCODE_PAY)) {
            // 支付
            CheckUtils.checkEmpty(req.getPayInfo().getMchntOrderNo(), "CMMS:支付流失号为空.");
            CheckUtils.checkEmpty(req.getPayInfo().getAmount(), "CMMS:支付金额为空.");
            CheckUtils.checkEmpty(req.getPayInfo().getPayType(), "CMMS:支付方式为空.");
            switch (req.getPayInfo().getPayType()) {
                case PayInfo.PAY_TYPE_WECHAT:
                    CheckUtils.checkEmpty(req.getPayInfo().getAuthCode(), "CMMS:微信支付条码为空.");
                    break;
                case PayInfo.PAY_TYPE_ALIPAY:
                    CheckUtils.checkEmpty(req.getPayInfo().getAuthCode(), "CMMS:支付宝支付条码为空.");
                    break;
                default:
                    throw new BusinessException(String.format("CMMS:[%s]支付方式不存在.", req.getPayInfo().getPayType()));
            }
        } else {
            throw new BusinessException(String.format("CMMS:[%s]支付场景不存在.", payCode));
        }

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("out_trade_no", req.getPayInfo().getMchntOrderNo());
        bossreq.put("authCode", req.getPayInfo().getAuthCode());
        bossreq.put("payType", req.getPayInfo().getPayType());
        bossreq.put("amount", req.getPayInfo().getAmount());
        bossreq.put("custInfo", req.getCustInfo());
        bossreq.put("bizCustomInfo", req.getBizCustomInfo());
        String output = getBossHttpInfOutput(req.getBizorderid(), BizConstant.BossInterfaceService.B_SCAN_C_PAY, bossreq, loginInfo);
        BScanCPayResp bScanCPayResp = JSON.parseObject(output, BScanCPayResp.class);

        return bScanCPayResp;
    }

    public ReverseResp reverse(String orderNo, LoginInfo loginInfo, String bizorderid) throws Exception {
        CheckUtils.checkEmpty(orderNo, "CMMS:BOSS支付订单号为空");

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("orderNo", orderNo);
        String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.REVERSE, bossreq, loginInfo);
        ReverseResp resp = JSON.parseObject(output, ReverseResp.class);

        return resp;
    }

}
