package com.maywide.biz.pay.task;

import com.alibaba.fastjson.JSONObject;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.market.entity.BizOrderSource;
import com.maywide.biz.market.entity.BizPortalOrder;
import com.maywide.biz.market.entity.CustOrder;
import com.maywide.biz.pay.gzboss.service.GcPayToBossService;
import com.maywide.biz.pay.task.bean.OrderInfoBean;
import com.maywide.biz.pay.task.entity.BizExceptionRecord;
import com.maywide.biz.pay.unify.service.UnifyPayService;
import com.maywide.core.dao.support.Page;
import com.maywide.core.service.PersistentService;
import com.maywide.core.util.UUIDGenerator;
import com.maywide.util.SqlTplUtils;
import com.maywide.util.StringObjectMapBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * OrderExceptionMonitorTask
 *
 * <AUTHOR>
 * @date 2022/11/24 16:11
 * @since 0.0.1
 */
@Component
public class OrderExceptionMonitorTask {

    private static final Logger logger = LoggerFactory.getLogger(OrderExceptionMonitorTask.class);

    private static final Map<String, OrderInfoBean> ORDER_CACHE = new HashMap<>();

    public static void add(OrderInfoBean orderInfoBean) {
        ORDER_CACHE.put(orderInfoBean.getOrderId(), orderInfoBean);
    }

    public static void remove(String orderId) {
        ORDER_CACHE.remove(orderId);
    }

    // 监听开始时间默认5分钟后开始 300000ms
    @Value("${order.exception.monitor.monitorStart:300000}")
    private long monitorStart;

    // 监听间隔结束时间默认15分钟后结束 900000ms
    @Value("${order.exception.monitor.monitorEnd:900000}")
    private long monitorEnd;

    @Autowired
    GcPayToBossService gcPayToBossService;

    @Autowired
    UnifyPayService unifyPayService;

    @Autowired
    PersistentService DAO;

    @Scheduled(cron = "0 */2 * * * ?")
    public void execute() {
        logger.info("订单异常监听任务开始执行!");
        Set<String> deleteOrderIds = new HashSet<>();
        ORDER_CACHE.forEach((orderId, orderInfoBean) -> {
            long createTime = orderInfoBean.getCreateTime();
            // 判断是否开始监听
            if (System.currentTimeMillis() - createTime > monitorStart) {

                // 判断是否结束监听
                if (System.currentTimeMillis() - createTime > monitorEnd) {
                    logger.info("订单[{}]支付超时,支付方式[{}],支付状态[{}],提交状态[{}]", orderId, orderInfoBean.getPayment(), orderInfoBean.getPayStatus(), orderInfoBean.getSubmitStatus());
                    deleteOrderIds.add(orderId);
                } else {
                    // 查询订单支付状态
                    try {
                        // 获取订单信息
                        String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(orderId);
                        String orderNo = JSONObject.parseObject(gwPayOrderInfo).get("bossorderno").toString();

                        if(StringUtils.isNotBlank(orderNo)){
                            // 查询订单状态
                            JSONObject queryResult = gcPayToBossService.queryOrderResultBoss(getCode(), orderNo, orderInfoBean.getCustId(), orderInfoBean.getLoginInfo());
                            if (queryResult != null) {
                                String queryStatus = queryResult.getString("pay_status");
                                // 如果支付成功，则判断订单是否已提交
                                if ("2".equals(queryStatus)) {
                                    // 设置支付状态
                                    orderInfoBean.setPayStatus(queryStatus);
                                    Long payTime = orderInfoBean.getPayTime();
                                    if (payTime == null) {
                                        payTime = System.currentTimeMillis();
                                        orderInfoBean.setPayTime(payTime);
                                    }

                                    // 如果支付成功，则判断订单是否已提交
                                    BizPortalOrder bizPortalOrderQuery = new BizPortalOrder();
                                    bizPortalOrderQuery.setId(Long.valueOf(orderInfoBean.getOrderId()));
                                    Page<BizPortalOrder> bizPortalOrderPage = DAO.find(getOne(), bizPortalOrderQuery);
                                    List<BizPortalOrder> list = bizPortalOrderPage.getResult();

                                    if (!CollectionUtils.isEmpty(list)) {
                                        BizPortalOrder order = list.get(0);
                                        // 判断订单是否已提交
                                        if ("1".equals(order.getSubmitStatus())) {
                                            // 如果已提交，则删除订单
                                            deleteOrderIds.add(orderId);
                                        } else {
                                            // 如果没提交，则判断支付时间，如果支付时间大于5分钟仍未提交则生成记录
                                            if (System.currentTimeMillis() - payTime > 300000) {
                                                logger.info("订单[{}]支付成功,但未提交,支付方式[{}],支付状态[{}],提交状态[{}]", orderId, orderInfoBean.getPayment(), orderInfoBean.getPayStatus(), orderInfoBean.getSubmitStatus());
                                                // 生成记录
                                                createExceptionRecord(orderInfoBean);
                                                // 删除订单
                                                deleteOrderIds.add(orderId);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.error("查询订单[{}]支付状态异常", orderId, e);
                    }
                }
            }
        });

        // 删除订单
        deleteOrderIds.forEach(ORDER_CACHE::remove);
        logger.info("订单异常监听任务执行结束!");
    }

    // 获取请求id
    private String getCode() {
        String idStr = "";
        try {
            Thread thread = Thread.currentThread();
            long id = thread.getId();
            idStr = String.valueOf(id);
            if (idStr.length() < 3) {
                StringBuffer idSb = new StringBuffer(idStr);
                int length = idSb.toString().length();
                for (int i = 0; i < 3 - length; i++) {
                    idSb.append(new Random().nextInt(10));
                }
                idStr = idSb.toString();
            } else {
                idStr = idStr.substring(idStr.length() - 3, idStr.length());
            }
        } catch (Exception e) {
            idStr = "000";
        }

        return System.currentTimeMillis() + idStr;
    }
    // 未读
    private static final String UNREAD = "unread";

    // 已读
    private static final String READ = "read";

    /**
     * 创建异常记录
     * @param orderInfoBean
     */
    public void createExceptionRecord(OrderInfoBean orderInfoBean) {
        try {
            BizExceptionRecord bizExceptionRecord = new BizExceptionRecord();
            // 查询订单
            BizPortalOrder bizPortalOrderQuery = new BizPortalOrder();
            bizPortalOrderQuery.setId(Long.valueOf(orderInfoBean.getOrderId()));
            Page<BizPortalOrder> bizPortalOrdePage = DAO.find(getOne(), bizPortalOrderQuery);
            List<BizPortalOrder> bizPortalOrderList = bizPortalOrdePage.getResult();

            String submitStatus = null;

            if (!CollectionUtils.isEmpty(bizPortalOrderList)) {
                BizPortalOrder bizPortalOrder = bizPortalOrderList.get(0);
                bizExceptionRecord.setCreateTime(new Date());
                LoginInfo loginInfo = orderInfoBean.getLoginInfo();
                bizExceptionRecord.setOperid(loginInfo.getOperid() + "");
                bizExceptionRecord.setOpername(loginInfo.getName());
                bizExceptionRecord.setOperdeptid(loginInfo.getDeptid() + "");
                bizExceptionRecord.setOperdeptname(loginInfo.getDeptname());
                bizExceptionRecord.setErrorDesc("订单未提交");
                bizExceptionRecord.setOrderId(orderInfoBean.getOrderId());
                bizExceptionRecord.setFee(bizPortalOrder.getFees());
                bizExceptionRecord.setPayStatus(orderInfoBean.getPayStatus());
                bizExceptionRecord.setSubmitStatus(bizPortalOrder.getSubmitStatus());
                bizExceptionRecord.setPayMethod(orderInfoBean.getPayment());
                bizExceptionRecord.setPayTime(new Date(orderInfoBean.getPayTime()));
                // 设置提交状态
                submitStatus = bizPortalOrder.getSubmitStatus();
            }

            // 查询订单详情
            CustOrder custOrderQuery = new CustOrder();
            custOrderQuery.setId(Long.valueOf(orderInfoBean.getOrderId()));
            Page<CustOrder> custOrderPage = DAO.find(getOne(), custOrderQuery);
            List<CustOrder> bizCustOrderList = custOrderPage.getResult();

            if (!CollectionUtils.isEmpty(bizCustOrderList)) {
                CustOrder custOrder = bizCustOrderList.get(0);
                bizExceptionRecord.setCustId(custOrder.getCustid().toString());
                bizExceptionRecord.setCustName(custOrder.getName());
            }

            // 查询元数据
            BizOrderSource bizOrderSourceQuery = new BizOrderSource();
            bizOrderSourceQuery.setOrderid(Long.valueOf(orderInfoBean.getOrderId()));
            Page<BizOrderSource> bizOrderSourcePage = DAO.find(getOne(), bizOrderSourceQuery);
            List<BizOrderSource> bizOrderSourceList = bizOrderSourcePage.getResult();

            if (!CollectionUtils.isEmpty(bizOrderSourceList)) {
                BizOrderSource bizOrderSource = bizOrderSourceList.get(0);
                bizExceptionRecord.setBizParam(bizOrderSource.getContent());
                if (StringUtils.isNotBlank(bizOrderSource.getContent())) {
                    try {
                        JSONObject json = JSONObject.parseObject(bizOrderSource.getContent());
                        String serialno = json.getString("serialno");
                        bizExceptionRecord.setSerialNo(serialno);
                        String subject = json.getString("subject");
                        bizExceptionRecord.setBizName(subject);
                        String businessTypeId = json.getString("business_type_id");
                        bizExceptionRecord.setBizCode(businessTypeId);
                        // 默认未读
                        bizExceptionRecord.setReadStatus(UNREAD);
                        // 如果没有提交才生成记录
                        if (StringUtils.isBlank(submitStatus) || "0".equals(submitStatus)) {
                            DAO.saveOrUpdate(bizExceptionRecord);
                        }
                        logger.info("新增异常订单提交记录：{}", JSONObject.toJSONString(bizExceptionRecord));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("查询订单[{}]信息异常", orderInfoBean.getOrderId(), e);
        }
    }

    private Page getOne() {
        Page page = new Page();
        page.setPageSize(1);
        page.setPageNo(1);
        return page;
    }

}
