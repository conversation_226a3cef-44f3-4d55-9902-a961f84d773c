package com.maywide.biz.prd.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.maywide.biz.prd.entity.PrdSalespkgKnowMobile;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Persistable;

import javax.persistence.Transient;
import java.io.Serializable;

public class PrdSalespkgKnowBo extends PrdSalespkgKnowMobile implements Persistable<Long>  {
    private Long salesid;

    private String city;

    public Long getSalesid() {
        return salesid;
    }

    public void setSalesid(Long salesid) {
        this.salesid = salesid;
    }

    @Override
    public Long getId() {
        return salesid;
    }

    @Override
    @Transient
    @JsonIgnore
    public boolean isNew() {
        Serializable id = getId();
        return id == null || StringUtils.isBlank(String.valueOf(id));
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
}
