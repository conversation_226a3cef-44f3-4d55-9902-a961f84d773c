package com.maywide.biz.prd.entity;

import com.maywide.core.annotation.MetaData;
import com.maywide.core.entity.PersistableEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.domain.Persistable;

import javax.persistence.*;

@Entity
@Table(name = "salespkg_additional")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class SalespkgAdditional extends PersistableEntity<Long> implements Persistable<Long> {
    @MetaData(value = "编号")
    private Long id;

    @MetaData(value = "商品优惠编号")
    private Long knowid;

    @MetaData(value = "附加商品编号")
    private String additionalid;

    @MetaData(value = "附加商品类型")
    private String additionaltype;

    @MetaData(value = "是否必选")
    private String whether;

    @Override
    @Transient
    public String getDisplay() {
        return null;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, length = 16)
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }


    public Long getKnowid() {
        return knowid;
    }

    public void setKnowid(Long knowid) {
        this.knowid = knowid;
    }

    public String getAdditionalid() {
        return additionalid;
    }

    public void setAdditionalid(String additionalid) {
        this.additionalid = additionalid;
    }

    @Column(nullable = false)
    public String getAdditionaltype() {
        return additionaltype;
    }

    public void setAdditionaltype(String additionaltype) {
        this.additionaltype = additionaltype;
    }

    @Column(nullable = false)
    public String getWhether() {
        return whether;
    }

    public void setWhether(String whether) {
        this.whether = whether;
    }
}
