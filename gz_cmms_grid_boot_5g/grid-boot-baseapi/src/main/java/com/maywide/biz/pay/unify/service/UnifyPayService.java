package com.maywide.biz.pay.unify.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.cons.BizConstant.BossInterfaceService;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.constant.QueConstant.CommonNotice;
import com.maywide.biz.inter.entity.CustBizOrderPool;
import com.maywide.biz.inter.pojo.bizpreprocess.SplitInfo;
import com.maywide.biz.inter.pojo.quecustorder.CustordersBO;
import com.maywide.biz.inter.pojo.quecustorder.QueCustorderInterReq;
import com.maywide.biz.inter.pojo.quecustorder.QueCustorderInterResp;
import com.maywide.biz.inter.service.PubService;
import com.maywide.biz.market.entity.BizOrderSource;
import com.maywide.biz.market.entity.BizPortalOrder;
import com.maywide.biz.pay.gzboss.pojo.BizQdElecpayReq;
import com.maywide.biz.pay.gzboss.pojo.GwOrderPayBo;
import com.maywide.biz.pay.gzboss.pojo.QueQdUnifyPayInfoReq;
import com.maywide.biz.pay.gzboss.pojo.QueQdUnifyPayResultResp;
import com.maywide.biz.pay.unify.entity.PayRecordLog;
import com.maywide.biz.pay.unify.manager.UnifyPayManager;
import com.maywide.biz.pay.unify.pojo.QueUnifyPayInfoReq;
import com.maywide.biz.pay.unify.pojo.QueUnifyPayInfoResp;
import com.maywide.biz.pay.unify.pojo.quePayResult.QuePayResultReq;
import com.maywide.biz.pay.unify.pojo.quePayResult.QuePayResultResp;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.util.BeanUtils;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class UnifyPayService extends CommonService {

    private Logger log = LoggerFactory.getLogger(UnifyPayService.class);

    @Autowired
    private PubService pubService;

    public ReturnInfo queUnifyPayInfo(QueUnifyPayInfoReq req, QueUnifyPayInfoResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, CommonNotice.LOGIN_OUT_NOTICE);

        CheckUtils.checkNull(req, "请求对象不能为空");
        CheckUtils.checkNull(req.getCustorderid(), "订单号不能为空");
        CheckUtils.checkNull(req.getCondition(), "订单条件不能为空");

        QueCustorderInterResp queOrderResp = new QueCustorderInterResp();
        QueCustorderInterReq queOrderReq = new QueCustorderInterReq();
        queOrderReq.setDetail(false);
        queOrderReq.setCustorderid(req.getCustorderid());
        ReturnInfo queOrderInfo = pubService.queCustorder(queOrderReq, queOrderResp);
        if (queOrderInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
            throw new BusinessException(queOrderInfo.getMessage(), queOrderInfo.getCode());
        }
        if (queOrderResp.getCustorders() == null || queOrderResp.getCustorders().size() == 0) {
            throw new BusinessException("查询订单失败");
        }
        CustordersBO order = queOrderResp.getCustorders().get(0);
        order.setSplitInfoList(getSplitInfoList(order.getCustorderid()));

        // 获得国网订单信息
        order.setGwPayOrderinfo(getGwPayOrderInfo(order.getCustorderid()));

        BizPortalOrder bizPortalOrder = (BizPortalOrder) getDAO().find(BizPortalOrder.class, Long.parseLong(order.getCustorderid()));
        CheckUtils.checkNull(bizPortalOrder, "缴费失败,查询不到对应的订单信息!");
        // 更改为不执行sql，改到回调执行
        Long payid = getSnowflakeOrderId();
        QueUnifyPayInfoResp quResp = UnifyPayManager.generatePayReq(order, req.getCondition(), req.getUnifyPayWechatBingBean(), req.getMultipayBean(), payid);

        BeanUtils.copyProperties(resp, quResp);
        return returnInfo;
    }


    public String getGwCodePayOrderInfo(String custorderid) throws Exception {
        BizOrderSource source = new BizOrderSource();
        source.setCode(BizConstant.SourceCode.GW_CODE_PAY_ORDERINFO);
        source.setOrderid(Long.valueOf(custorderid));
        List<BizOrderSource> list = getDAO().find(source);
        if (list != null && !list.isEmpty()) {
            source = list.get(0);
            String content = source.getContent();
            if (StringUtils.isNotBlank(content)) {
                return content;
            }
        }
        return null;
    }

    public String getGwPayOrderInfo(String custorderid) throws Exception {
        BizOrderSource source = new BizOrderSource();
        source.setCode(BizConstant.SourceCode.GW_PAY_ORDERINFO);
        source.setOrderid(Long.valueOf(custorderid));
        List<BizOrderSource> list = getDAO().find(source);
        if (list != null && !list.isEmpty()) {
            source = list.get(0);
            String content = source.getContent();
            if (StringUtils.isNotBlank(content)) {
                return content;
            }
        }
        return null;
    }

    public List<SplitInfo> getSplitInfoList(String custorderid) throws Exception {
        BizOrderSource source = new BizOrderSource();
        source.setCode(BizConstant.SourceCode.SPLIT_INFO);
        source.setOrderid(Long.valueOf(custorderid));
        List<BizOrderSource> list = getDAO().find(source);
        if (list != null && !list.isEmpty()) {
            source = list.get(0);
            String content = source.getContent();
            return JSON.parseObject(content, new TypeReference<List<SplitInfo>>(){});
        }
        return null;
    }

    public QueUnifyPayInfoResp generatePayReq(String fees, String custOdrerid, String opcode, String custName
            , String custid) {
        return UnifyPayManager.generatePayReq(fees, custOdrerid, opcode, custName, custid, "CSJF");
    }

    /**
     * 为当前网格营销的业务工单做额外的支付链接查询
     * 使用原来的出入参是为了让前端改动最小
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ReturnInfo queNoOrderPayInfo(QueUnifyPayInfoReq req, QueUnifyPayInfoResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req, "请求对象不能为空");
        CheckUtils.checkNull(req.getCustorderid(), "订单号不能为空");
//		CheckUtils.checkNull(req.getCondition(), "订单条件不能为空");
        CustBizOrderPool custBizOrderPool = new CustBizOrderPool();
        custBizOrderPool.setServorderid(Long.parseLong(req.getCustorderid()));
        List<CustBizOrderPool> list = DAO.find(custBizOrderPool);
        if (null == list || list.isEmpty()) {
            CheckUtils.checkNull(null, "查询支付链接地址错误,根据订单号【" + req.getCustorderid() + "】无法查找到相关订单信息!");
        }
        custBizOrderPool = list.get(0);
        CheckUtils.checkNull(custBizOrderPool, "查询支付链接地址错误,根据订单号【" + req.getCustorderid() + "】无法查找到相关订单信息!");
        if (custBizOrderPool.getFeeStatus().equalsIgnoreCase("Y")) {
            CheckUtils.checkNull(null, "该订单已经为已支付状态,无需再次支付!");
        }
        if (custBizOrderPool.getCustOrder() == null) {
            custBizOrderPool.setCustOrder(getSnowflakeOrderId());
            getDAO().update(custBizOrderPool);
        }

        // 20200628 增加payid
        Long payid = getSnowflakeOrderId();

        QueUnifyPayInfoResp payResp = UnifyPayManager.generatePayReq(custBizOrderPool.getFees(),
                custBizOrderPool.getCustOrder().toString(), "业务工单处理", custBizOrderPool.getCustName(), custBizOrderPool.getCustid().toString(), BossInterfaceService.WFL_EQUIPINFO_SUBMIT, payid);
        BeanUtils.copyProperties(resp, payResp);
        return returnInfo;
    }

    /**
     * 查询支付结果
     *
     * @return
     */
    public ReturnInfo quePayResult(QuePayResultReq req, QuePayResultResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();

        Long custorderid = req.getCustorderid();
        CheckUtils.checkNull(custorderid, "订单号为空!");

        // 查询支付日志
        String sql = "select * from pay_record_log where custorderid = ? and action_type = ? order by paydate desc";

        resp.setStatus(-1);
        resp.setMsg("[该订单未收到支付回调]");

        List<PayRecordLog> list = getDAO().find(sql, PayRecordLog.class, custorderid, "01");
        if (list != null && !list.isEmpty()) {
            // 取最新的数据
            PayRecordLog payLog = list.get(0);
            String payresult = payLog.getPayresult();
            switch (payresult) {
                case "0":
                case "1":
                    // 收到回调，但是未做订单确认
                    resp.setStatus(1);
                    resp.setMsg("[收到支付回调，还未做订单确认]");
                    break;
                case "2":
                    // 收到回调且订单确认成功
                    resp.setStatus(2);
                    resp.setMsg("[收到支付回调，业务办理成功]");
                    break;
                case "3":
                    // 收到支付回调，业务办理失败，收款已回退
                    resp.setStatus(3);
                    resp.setMsg("[收到支付回调，业务办理失败，收款已回退]" + payLog.getBizexmsg());
                    break;
                case "4":
                    // 收到支付回调，业务办理失败，收款回退失败
                    resp.setStatus(4);
                    resp.setMsg("[收到支付回调，业务办理失败，收款回退失败]" + payLog.getBizexmsg());
                    break;
                case "5":
                    // 支付失败
                    resp.setStatus(5);
                    resp.setMsg("[支付失败]");
                    break;
                default:
                    break;
            }
        }

        return returnInfo;
    }

    /**
     * 获取支付结果信息
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queQdUnifyPayResultInfo(QueQdUnifyPayInfoReq req, QueQdUnifyPayResultResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, CommonNotice.LOGIN_OUT_NOTICE);

        CheckUtils.checkNull(req, "请求对象不能为空");
        CheckUtils.checkNull(req.getCustorderid(), "订单号不能为空");

        QueCustorderInterResp queOrderResp = new QueCustorderInterResp();
        QueCustorderInterReq queOrderReq = new QueCustorderInterReq();
        queOrderReq.setDetail(false);
        queOrderReq.setCustorderid(req.getCustorderid());
        ReturnInfo queOrderInfo = pubService.queCustorder(queOrderReq, queOrderResp);
        if (queOrderInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
            throw new BusinessException(queOrderInfo.getMessage(), queOrderInfo.getCode());
        }
        if (queOrderResp.getCustorders() == null || queOrderResp.getCustorders().size() == 0) {
            throw new BusinessException("查询订单失败");
        }
        CustordersBO order = queOrderResp.getCustorders().get(0);
        order.setSplitInfoList(getSplitInfoList(order.getCustorderid()));
        //查询cmms订单状态
        BizPortalOrder bizPortalOrder = (BizPortalOrder) getDAO().find(BizPortalOrder.class, Long.parseLong(order.getCustorderid()));
        CheckUtils.checkNull(bizPortalOrder, "查询不到对应的订单信息!");
        String cmms_status = bizPortalOrder.getStatus();
        // 获得国网订单信息,payorderno
        String gwPayOrderInfo = getGwPayOrderInfo(order.getCustorderid());
        if(StringUtils.equals(cmms_status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            GwOrderPayBo bo = new GwOrderPayBo();
            bo.setCmms_status("200");
            resp.setData(bo);
            return returnInfo;
        }
        if(StringUtils.isBlank(gwPayOrderInfo)){
            GwOrderPayBo bo = new GwOrderPayBo();
            bo.setCmms_status("0");
            resp.setData(bo);
            return returnInfo;
        }
        String bossorderno = JSONObject.parseObject(gwPayOrderInfo).get("bossorderno").toString();
        BizQdElecpayReq reqBoss = new BizQdElecpayReq();
        reqBoss.setPayorderno(bossorderno);
        reqBoss.setCustid(order.getCustid());
        String bossRespOutput = "null";
        try {
             bossRespOutput = getBossHttpInfOutput(req.getBizorderid(), BizConstant.BossInterfaceService.QUE_QD_PAY, reqBoss, loginInfo);
        }catch (Exception e){
            e.printStackTrace();
            GwOrderPayBo bo = new GwOrderPayBo();
            bo.setCmms_status("0");
            resp.setData(bo);
            return returnInfo;
        }
        if(null!=bossRespOutput&&!"null".equals(bossRespOutput)) {
            GwOrderPayBo bo = JSON.parseObject(bossRespOutput,GwOrderPayBo.class);
            //如果cmms中订单未支付，国网已支付，则cmms_status=7
            bo.setCmms_status("0");
            if(cmms_status.equals("0") && bo.getPay_status().equals("2")){
                bo.setCmms_status("7");
            }
            if("2".equals(cmms_status)) {
                bo.setCmms_status("200");
            }
            resp.setData(bo);
        }else{
            if("2".equals(cmms_status)) {
                GwOrderPayBo bo = new GwOrderPayBo();
                bo.setCmms_status("200");
                resp.setData(bo);
            }
        }

        return returnInfo;
    }
}
