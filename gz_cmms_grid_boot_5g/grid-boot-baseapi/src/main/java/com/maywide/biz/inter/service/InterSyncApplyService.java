package com.maywide.biz.inter.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.cons.BizConstant.*;
import com.maywide.biz.core.entity.Rule;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.pojo.bizpreprocess.SalespkgKnowObjInfoBO;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.service.RuleService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.constant.QueConstant;
import com.maywide.biz.inter.constant.QueConstant.CommonNotice;
import com.maywide.biz.inter.pojo.applyinstall.BizProcessOrderDetailBO;
import com.maywide.biz.inter.pojo.applyinstall.ExdevList;
import com.maywide.biz.inter.pojo.bizGroupActivate.BizGroupActiveBossReq;
import com.maywide.biz.inter.pojo.bizGroupActivate.BizGroupActiveReq;
import com.maywide.biz.inter.pojo.bizGroupActivate.BizGroupActiveResp;
import com.maywide.biz.inter.pojo.bizInstallCommit.BizInstallCommit2BossReq;
import com.maywide.biz.inter.pojo.bizInstallCommit.BizInstallCommit2BossResp;
import com.maywide.biz.inter.pojo.bizInstallCommit.BizInstallCommitReq;
import com.maywide.biz.inter.pojo.bizInstallCommit.BizInstallCommitResp;
import com.maywide.biz.inter.pojo.bizLockCmccAcctno.BizLockCmccAcctnoReq;
import com.maywide.biz.inter.pojo.interSyncApplyService.queFixorderList.Fixorder;
import com.maywide.biz.inter.pojo.interSyncApplyService.queFixorderList.QueFixorderListReq;
import com.maywide.biz.inter.pojo.interSyncApplyService.queFixorderList.QueFixorderListResp;
import com.maywide.biz.inter.pojo.interSyncApplyService.queOperExpand.OperExpandBo;
import com.maywide.biz.inter.pojo.interSyncApplyService.queOperExpand.QueOperExpandResp;
import com.maywide.biz.inter.pojo.queAgentDeveloperType.QueAgentDeveloperTypeBO;
import com.maywide.biz.inter.pojo.queAgentDeveloperType.QueAgentDeveloperTypeResp;
import com.maywide.biz.inter.pojo.queCmccAcctno.QueAcctnoBossResp;
import com.maywide.biz.inter.pojo.queCmccAcctno.QueCmccAcctnoReq;
import com.maywide.biz.inter.pojo.queCmccAcctno.QueCmccAcctnoResp;
import com.maywide.biz.inter.pojo.queDevPrdinfo.QueDevPrdinfoBossResp;
import com.maywide.biz.inter.pojo.queDevPrdinfo.QueDevPrdinfoReq;
import com.maywide.biz.inter.pojo.queDevPrdinfo.QueDevPrdinfoResp;
import com.maywide.biz.inter.pojo.queDevstore.QueDevstoreReq;
import com.maywide.biz.inter.pojo.queDevstore.ResDevice;
import com.maywide.biz.inter.pojo.queSameDeptOperList.QueSameDeptOperBO;
import com.maywide.biz.inter.pojo.queSameDeptOperList.QueSameDeptOperListResp;
import com.maywide.biz.inter.pojo.queSyncPercomb.*;
import com.maywide.biz.inter.pojo.quereshouse.QueResHouseBossReq;
import com.maywide.biz.inter.pojo.quereshouse.QueResHouseBossResp;
import com.maywide.biz.inter.pojo.quereshouse.QueResHouseInterReq;
import com.maywide.biz.inter.pojo.quereshouse.QueResHouseInterResp;
import com.maywide.biz.inter.pojo.querysalespkgknow.SubclassBo;
import com.maywide.biz.inter.pojo.sycnChlInstall.*;
import com.maywide.biz.inter.pojo.syncApplyInstall.*;
import com.maywide.biz.market.entity.*;
import com.maywide.biz.prd.entity.Sales;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.exception.CustomException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.util.BeanUtil;
import com.maywide.core.util.BeanUtils;
import com.maywide.core.util.CheckUtils;
import com.maywide.core.util.DateUtils;
import com.maywide.grid.boot.baseapi.sysparam.SysparamCache;
import com.maywide.grid.boot.baseapi.sysparam.SysparamGcodeCons;
import com.maywide.util.SqlTplUtils;
import com.maywide.util.StringObjectMapBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class InterSyncApplyService extends CommonService {
    protected final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private PubService pubService;

    @Autowired
    private RuleService ruleService;

//	@Autowired
//	private UsingProductService usingProductService;

    @Autowired
    private InterApplyService interApplyService;
    @Autowired
    private FnService fnService;

    @Autowired
    private DeviceAllocationService deviceAllocationService;

    @Autowired
    private InterOrderService interOrderService;

    private final String sourceGcode = "SYS_DEV_UPROP";

    private final String servtypeGcode = "SYS_SERV_TYPE";

    private final String vodInputWayGcode = "SYS_VOD_INPUTWAY";

    private final String cmInputWayGcode = "SYS_CM_INPUTWAY";

    private final String authmodeGcode = "BIZ_AUTHMODE";

    private final String ipModeGcode = "BIZ_IPMODE";

    private final String cmTypeGcode = "SERV_CM_TYPE";

    private final String feekindGcode = "SYS_CHARGETYPE";

    private final String feeWayGcode = "SYS_FEEWAY";

    private final String ottRtoTwoSwitch = "OTTR_TWO_SWITCH";

    private final String installTypeGcode = "BIZ_INSTALLTYPE";

    private final String scopetypeGcode = "BIZ_SCOPETYPE";

    private final String sysUecodeGcode = "SYS_UECODE";

    protected final String suffixGcode = "CMACCTNO_LAST";

    private final String digitPermark = "1";

    private final String cmPermark = "2";

    private final String vodPermark = "3";

    private final String ottPermark = "4";

    private final String stPermark = "S";

    private final String sthPermark = "W";
    private final String m5gPermark = "B";

    private String fixed = "GDM";

    private final String CITY_ORDER_GOODS_RULE = "CITY_ORDER_GOODS";

    private final String CITY_ISASH_PLACING_RULE = "CITY_ISASH_PLACING";

    private final String SMARTHOME_DEV_INPUTWAY = "SMARTHOME_DEV_INPUTWAY";
    private final String ZNJU_JSQ_PARAM = "ZNJU_JSQ_PARAM";

    private final String SYS_FGB_SERV_TYPE = "SYS_FGB_SERV_TYPE";
    private final String FGB_USER_INVOICEMODE = "FGB_USER_INVOICEMODE";

    /**
     * 新设备开户
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo quePercombInfo(QueSyncPercombReq req, QueSyncPercombResp resp) throws Exception {

        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, CommonNotice.LOGIN_OUT_NOTICE);
        List<PercombDevParam> datas = new ArrayList<>();
        if (StringUtils.isBlank(req.getFixed()) || !fixed.equals(req.getFixed())){
            datas = getPercombDevInfo(loginInfo, req.getOpmode(), loginInfo.getCity()
                    , req.getCustid(), req.getBizorderid(), req.getPermark());
        }else{
            datas = getPercombDevInfoFixed(loginInfo, req.getOpmode(),req.getOpcodes(), loginInfo.getCity()
                    , req.getCustid(), req.getBizorderid(), req.getPermark());
        }

        resp.setOpmode(req.getOpmode());
        resp.setDatas(datas);
        setCommonInfo2Resp(loginInfo.getCity(), resp);
        setFeekindParam2Resp(loginInfo.getCity(), resp);
        addCityOpmodeRule(loginInfo.getCity(), req.getOpmode(), resp);
        controllerAcctno(loginInfo.getCity(), resp);
        //录入宽带类型是移动，电信，联调是否需要输入账号密码规则
        //inputIsAshPlacing(loginInfo.getCity(),resp);

        // 增加5G 业务的过滤
        filterByM5g(resp, req.getPcode());

        // 5N波轮一键开户配置
        if (StringUtils.isNotBlank(req.getRootNodeId()) && StringUtils.isNotBlank(req.getcNodeId())
                && StringUtils.isNotBlank(req.getbNodeId())) {
            fnService.fnOpDefault(resp, req.getRootNodeId(), req.getcNodeId(), req.getbNodeId(), req.getOpmode());
        }

        return returnInfo;
    }

    private void filterByM5g(QueSyncPercombResp resp, String pcode) {
        if (StringUtils.isNotBlank(pcode)) {
            // 移动业务 和 以固促移 只有移动业务
            if (StringUtils.equals(pcode, "MO") || StringUtils.equals(pcode, "GDM")) {
                List<PercombDevParam> datas = resp.getDatas();
                if (datas != null && !datas.isEmpty()) {
                    Iterator<PercombDevParam> iterator = datas.iterator();
                    while (iterator.hasNext()) {
                        PercombDevParam next = iterator.next();
                        if (!StringUtils.equals(next.getPermarks(), PermarkType.M5G)) {
                            iterator.remove();
                        }
                    }
                }
            }

            // 目前移动业务只有前台缴费
            List<PrvSysparam> paymentList = resp.getPaymentList();
            if (paymentList != null && !paymentList.isEmpty()) {
                Iterator<PrvSysparam> iterator = paymentList.iterator();
                while (iterator.hasNext()) {
                    PrvSysparam next = iterator.next();
                    if (!StringUtils.equals(next.getMcode(), "0")) {
                        iterator.remove();
                    }
                }
            }


        }
    }

	/*private void inputIsAshPlacing(String city,QueSyncPercombResp resp) throws Exception{
		Rule rule = ruleService.getRule(city,CITY_ISASH_PLACING_RULE);
		if(rule == null){
			rule = ruleService.getRule("*", CITY_ISASH_PLACING_RULE);
		}
		if(rule != null && StringUtils.isNotBlank(rule.getValue())){
			resp.setIsAshPlacing(rule.getValue().trim());
		}

	}*/

    private void controllerAcctno(String city, QueSyncPercombResp resp) throws Exception {
        Rule rule = ruleService.getRule(city, "ACCTNO_VIEW");
        if (rule == null) {
            rule = ruleService.getRule("*", "ACCTNO_VIEW");
        }
        if (rule != null) {
            resp.setAcctnoShow("Y");
        } else {
            resp.setAcctnoShow("N");
        }
    }


    /**
     * 修改规则为没有配置则默认要全省订购,有的话则不需要订购(有就不需要，没有就需要的原则)
     *
     * @param city
     * @param opmode
     * @param resp
     * @throws Exception
     */
    protected void addCityOpmodeRule(String city, String opmode, QueSyncPercombResp resp) throws Exception {
        Rule rule = ruleService.getRule(city, CITY_ORDER_GOODS_RULE);
        if (rule == null) {
            rule = ruleService.getRule("*", CITY_ORDER_GOODS_RULE);
        }

        if (rule != null && StringUtils.isNotBlank(rule.getValue())) {
            String[] opmodes = rule.getValue().split(",");
            for (String value : opmodes) {
                if (value.equals(opmode)) {
                    resp.setOrdergoods("N");
                }
            }
        }
    }

    /**
     * 获取业务组合下的设备信息(包含了Lable)
     *
     * @param
     * @param permarks
     * @throws Exception
     */
    protected List<PercombDevParam> getPercombDevInfo(LoginInfo loginInfo, String opmodes,
                                                      String city, String custid, String bizOrderid, String permarks) throws Exception {
        List params = new ArrayList();
        StringBuffer sb = new StringBuffer();
        sb.append("		SELECT a.RECID recid,a.PERCOMB percomb,b.combname combname,b.permarks permarks");
        sb.append("		FROM  SYS_PERCOMB_PARAM a ,biz_percomb_cfg b");
        sb.append("		WHERE a.PERCOMB = b.percomb");
        sb.append("		and b.OPMODES LIKE ? ");
        params.add("%" + opmodes + "%");
        sb.append("		AND a.CITY = ? ");
        params.add(city);
        if (StringUtils.isNotBlank(permarks)) {
            String[] split = StringUtils.split(permarks, ",");
            sb.append(" and ( ");
            for (int i = 0; i < split.length; i++) {
                sb.append(" find_in_set(?, b.permarks) > 0 ");
                params.add(split[i]);
                if (i + 1 < split.length) {
                    sb.append("or ");
                }
            }
            sb.append(" ) ");
        }
        sb.append("		ORDER BY a.SORT");
        List<PercombDevParam> resultList = getDAO().find(sb.toString(), PercombDevParam.class, params.toArray());
        if (resultList != null && !resultList.isEmpty()) {
            resultList.parallelStream().forEach(devParam -> {
                try {
                    List<PercombDevInfo> devInfos = getDevInfo(loginInfo, devParam.getRecid(), devParam.getPercomb());
                    devParam.setDevinfoList(devInfos);
                    if (devParam.getPermarks().contains(digitPermark)) {
                        devParam.setDigitParams(getDigitParams(devParam.getRecid()));
                    }
                    if (devParam.getPermarks().contains(vodPermark)) {
                        devParam.setVodParams(getVodParams(devParam.getRecid()));
                    }
                    if (devParam.getPermarks().contains(cmPermark)) {
                        devParam.setCmParams(getCmParams(devParam.getRecid()));
                        fillCMParam(devParam.getCmParams(), custid, bizOrderid, loginInfo);
                    }
                    if (devParam.getPermarks().contains(sthPermark)) {
                        devParam.setSthParams(getSthParams(devParam.getRecid()));
                        List<SubclassBo> subclassList = devParam.getSubclassList();
                        if (subclassList == null) {
                            subclassList = new ArrayList<>();
                            devParam.setSubclassList(subclassList);
                        }

                        subclassList.addAll(getSthSubclassList(devParam.getDevinfoList()));
                    }

                    if (devParam.getPermarks().contains(m5gPermark)) {
                        devParam.setMobileParams(getMobileParams(devParam.getRecid()));
                    }

                    List<PercombOnceParam> onceParams = checkExtraInfo(devParam.getRecid());
                    devParam.setOnceParams(onceParams);
                } catch (Exception e) {
                    log.error("=>获取业务组合出错", e);
                }
            });
        }
        return resultList;
    }


    /**
     * 获取业务组合下的设备信息(包含了Lable)
     *
     * @param
     * @param permarks
     * @throws Exception
     */
    protected List<PercombDevParam> getPercombDevInfoFixed(LoginInfo loginInfo, String opmodes,String opcodes,
                                                      String city, String custid, String bizOrderid, String permarks) throws Exception {
        List params = new ArrayList();
        StringBuffer sb = new StringBuffer();
        sb.append("		SELECT a.RECID recid,a.PERCOMB percomb,b.combname combname,b.permarks permarks");
        sb.append("		FROM  SYS_PERCOMB_PARAM a ,biz_percomb_cfg b");
        sb.append("		WHERE a.PERCOMB = b.percomb");
        sb.append("		and b.permarks !='B' ");
        sb.append("		and b.OPMODES LIKE ? ");
        params.add("%" + opmodes + "%");
        sb.append("		AND (a.CITY = ? or a.CITY = '*') ");
        params.add(city);
        if(StringUtils.isNotBlank(opcodes)){
            sb.append(" AND b.opcodes like ? ");
            params.add("%" + opcodes + "%");
        }else {
            sb.append(" AND b.opcodes != 'BIZ_DEV_INVERSION' ");
        }
        sb.append("		ORDER BY a.SORT");
        List<PercombDevParam> resultList = getDAO().find(sb.toString(), PercombDevParam.class, params.toArray());
        if (resultList != null && !resultList.isEmpty()) {
            for (int i = 0; i < resultList.size(); i++) {
                PercombDevParam devParam = resultList.get(i);
                List<PercombDevInfo> devInfos = getDevInfo(loginInfo, devParam.getRecid(), devParam.getPercomb());
                devParam.setDevinfoList(devInfos);
                if (devParam.getPermarks().contains(digitPermark)) {
                    devParam.setDigitParams(getDigitParams(devParam.getRecid()));
                }
                if (devParam.getPermarks().contains(vodPermark)) {
                    devParam.setVodParams(getVodParams(devParam.getRecid()));
                }
                //广西没有OTT （暂时先不搬）
//                if (devParam.getPermarks().contains(ottPermark)) {
//                    devParam.setOttParams(getOttParams(devParam.getRecid()));
//                }
                if (devParam.getPermarks().contains(cmPermark)) {
                    devParam.setCmParams(getCmParams(devParam.getRecid()));
//					fillCMParam(devParam.getCmParams(),custid,bizOrderid,loginInfo);
                }
                //广西没有软终端（暂时先不搬）
//                if (devParam.getPermarks().contains(stPermark)) {
//                    devParam.setStParams(getStParams(devParam.getRecid()));
//                }
                List<PercombOnceParam> onceParams = checkExtraInfo(devParam.getRecid());
                devParam.setOnceParams(onceParams);
            }
        }
        return resultList;
    }

    private List<SubclassBo> getSthSubclassList(List<PercombDevInfo> devinfoList) throws Exception {

        List<SubclassBo> list = new ArrayList<>();

        if (devinfoList != null && !devinfoList.isEmpty()) {
            for (PercombDevInfo devInfo : devinfoList) {
                List<DevProductInfo> productList = devInfo.getProductList();
                if (productList != null && !productList.isEmpty()) {
                    for (DevProductInfo devProductInfo : productList) {
                        SubclassBo subclassBo = new SubclassBo();
                        subclassBo.setSubkind(devProductInfo.getPid());
                        subclassBo.setSubname(devProductInfo.getPname());

                        // 查询型号
                        String sql = "select t0.subkind, t0.name subname from res_class t0 " +
                                "left join prd_pcode t1 on find_in_set(t0.subkind, t1.EXTERNALID) > 0 " +
                                "where t1.pid = ? ";
                        List<SubclassBo> sub = getDAO().find(sql, SubclassBo.class, devProductInfo.getPid());
                        if (sub == null) {
                            sub = new ArrayList<>();
                        }
                        subclassBo.setSubclass(sub);
                        list.add(subclassBo);
                    }
                }
            }
        }

        return list;
    }

    /**
     * 获得智能家居
     *
     * @param recid
     * @return
     */
    private List<PercombSthParam> getSthParams(Long recid) throws Exception {
        String sql = "select t.INPUTWAY, t.subcomperk from sys_sth_param t where t.PP_RECID = ?";
        List<PercombSthParam> list = getDAO().find(sql, PercombSthParam.class, recid);
        if (list != null && !list.isEmpty()) {
            list.parallelStream().forEach(sthParam -> {
//				PercombSthParam sthParam = list.get(i);
                try {
                    sthParam.setInputWayList(getParams(SMARTHOME_DEV_INPUTWAY, sthParam.getInputWay()));
                    sthParam.setSubcomperkList(getParams(ZNJU_JSQ_PARAM, sthParam.getSubcomperk()));
                } catch (Exception e) {
                    log.error("获得智能家居错误", e);
                }
            });
            return list;
        }
        return null;
    }

    /**
     * 获得手机业务的设置
     *
     * @param recid
     * @return
     */
    private List<PercombMobileParam> getMobileParams(Long recid) throws Exception {
        String sql = "select t.fgbServType, t.fgbUserInvoicemode from sys_mobile_param t where t.PP_RECID = ?";
        List<PercombMobileParam> list = getDAO().find(sql, PercombMobileParam.class, recid);
        if (list != null && !list.isEmpty()) {
            list.parallelStream().forEach(param -> {
                try {
                    param.setFgbServTypeList(getParams(SYS_FGB_SERV_TYPE, param.getFgbServType()));
                    param.setFgbUserInvoicemodeList(getParams(FGB_USER_INVOICEMODE, param.getFgbUserInvoicemode()));
                } catch (Exception e) {
                    log.error("获得手机业务错误", e);
                }
            });
            return list;
        }
        return null;
    }

    protected void fillCMParam(List<PercombCmParam> params, String custid,
                               String bizOrderid, LoginInfo loginInfo) throws Exception {
    }


    /**
     * 获取设备信息
     *
     * @param percomb
     * @throws Exception
     */
    protected List<PercombDevInfo> getDevInfo(LoginInfo loginInfo, Long recid, String percomb) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("		SELECT a.LABLE devLable ,a.PSUBCLASS psubClass,a.USEPROPS userprops,a.ISMUST isMust ");
        sb.append("		FROM sys_dev_param a ");
        sb.append("		WHERE a.PP_RECID = ?");
        sb.append("		 ORDER BY a.SORT");
        List<PercombDevInfo> devList = getDAO().find(sb.toString(), PercombDevInfo.class, recid);
        if (devList != null || !devList.isEmpty()) {
            devList.parallelStream().forEach(info -> {
//				PercombDevInfo info = devList.get(i);
                try {
                    List<PrvSysparam> sources = getParams(sourceGcode, info.getUserprops());// 获取设备来源
                    List<DevProductInfo> products = getDevInfoProduct(loginInfo.getAreaid(), info.getPsubClass(), percomb);
                    info.setSourceList(sources);
                    info.setProductList(products);
                } catch (Exception e) {
                    log.error("获取设备信息错误", e);
                }
            });
        }
        return devList;
    }

    /**
     * 获取设备对应的产品
     *
     * @throws Exception
     */
    private List<DevProductInfo> getDevInfoProduct(Long areaid, String psubclass, String percomb) throws Exception {
        if (StringUtils.isBlank(psubclass)) return null;
        PrvSysparam sqlParam = new PrvSysparam();
        sqlParam.setMcode("@DYNAMIC_LEVEL");
        sqlParam.setGcode(psubclass);
        List<PrvSysparam> prvparams = getDAO().find(sqlParam);
        if (prvparams == null || prvparams.isEmpty()) {
            return null;
        }
        sqlParam = prvparams.get(0);
        String sql = sqlParam.getData();
        CheckUtils.checkNull(sql, "产品设备参数配置为空,请联系管理员");
        List<DevProductInfo> productInfos = getDAO().find(sql, DevProductInfo.class, areaid);
        handlerDevRule(percomb, psubclass, productInfos);
        return productInfos;
    }

    /**
     * 处理设备产品规则
     *
     * @param percomb
     * @param psubclass
     * @param productInfos
     * @throws Exception
     */
    protected void handlerDevRule(String percomb, String psubclass, List<DevProductInfo> productInfos) throws Exception {
        if (psubclass.equals("PRD_PCODESTBLIST") || psubclass.equals("PRD_PCODESMLIST")) {
            String kind = psubclass.equals("PRD_PCODESTBLIST") ? "2" : "1";
            String sql = "SELECT a.DEVPRDS FROM biz_percomb_dev_cfg a WHERE a.KIND = ? AND a.PERCOMB = ?";
            List params = new ArrayList();
            params.add(kind);
            params.add(percomb);
            List<DevProductInfo> devprods = getDAO().find(sql, DevProductInfo.class, params.toArray());
            if (devprods != null && !devprods.isEmpty()) {
                DevProductInfo result = devprods.get(0);
                if (StringUtils.isBlank(result.getDevprds()) || result.getDevprds().equals("*")) return;
                List<DevProductInfo> tmp = new ArrayList<DevProductInfo>();
                String[] prods = result.getDevprds().split(",");
                for (String str : prods) {
                    for (DevProductInfo info : productInfos) {
                        if (info.getPid().equals(str)) {
                            tmp.add(info);
                            break;
                        }
                    }
                }
                if (!tmp.isEmpty()) {
                    productInfos.clear();
                    productInfos.addAll(tmp);
                }
            }
        }
    }


    /**
     * 获取数字业务参数
     *
     * @throws Exception
     */
    private List<PercombDigitParam> getDigitParams(Long ppRecid) throws Exception {
        String sql = "SELECT a.SERVTYPE,a.ISCABLE,a.PLATFORM FROM sys_digit_param a WHERE a.PP_RECID = ?";
        List<PercombDigitParam> digitParams = getDAO().find(sql, PercombDigitParam.class, ppRecid);
        if (digitParams != null || !digitParams.isEmpty()) {
            digitParams.parallelStream().forEach(digit -> {
                try {
                    List<PrvSysparam> servtypeList = getParams(servtypeGcode, digit.getServtype());
                    digit.setServtypeList(servtypeList);
                    List<PrvSysparam> platformList = getParams(sysUecodeGcode, digit.getPlatform());
                    digit.setPlatformList(platformList);
                } catch (Exception e) {
                    log.error("获取数字业务参数错误", e);
                }
            });
        }
        return digitParams;
    }

    /**
     * 获取互动业务参数
     *
     * @throws Exception
     */
    private List<PercombVodParam> getVodParams(Long ppRecid) throws Exception {
        String sql = "SELECT a.VODINPUTWAY vodinputway,a.ISINMAC isinmac,a.CREDITLIMIT creditlimit FROM sys_vod_param a WHERE a.PP_RECID = ?";
        List<PercombVodParam> vodParams = getDAO().find(sql, PercombVodParam.class, ppRecid);
        if (vodParams != null && !vodParams.isEmpty()) {
            vodParams.parallelStream().forEach(vodParam -> {
//				PercombVodParam vodParam = vodParams.get(i);
                try {
                    List<PrvSysparam> inputWayList = getParams(vodInputWayGcode, vodParam.getVodinputway());
                    vodParam.setInputWayList(inputWayList);
                } catch (Exception e) {
                    log.error("获取互动业务参数错误", e);
                }
            });
        }
        return vodParams;
    }

    /**
     * 获取宽带业务参数
     *
     * @throws Exception
     */
    private List<PercombCmParam> getCmParams(Long recid) throws Exception {
        String sql = "SELECT a.AUTHMODE authmode,a.CMINPUTWAY cminputway,a.CMTYPE cmtype,a.IPMODE ipmode,a.CMLAST cmLast FROM SYS_CM_PARAM a WHERE a.PP_RECID = ?";
        List<PercombCmParam> cmParams = getDAO().find(sql, PercombCmParam.class, recid);
        if (cmParams != null && !cmParams.isEmpty()) {
            cmParams.parallelStream().forEach(param -> {
//				PercombCmParam param = cmParams.get(i);
                try {
                    List<PrvSysparam> inputWayList = getParams(cmInputWayGcode, param.getCminputway());
                    param.setInputWayList(inputWayList);
                    List<PrvSysparam> authmodeList = getParams(authmodeGcode, param.getAuthmode());
                    param.setAuthmodeList(authmodeList);
                    List<PrvSysparam> ipModes = getParams(ipModeGcode, param.getIpmode());
                    param.setIpmodeList(ipModes);
                    List<PrvSysparam> cmTypeList = getParams(cmTypeGcode, param.getCmtype());
                    param.setTypeList(cmTypeList);
                    if (StringUtils.isNotBlank(param.getCmLast())) {
                        param.setSuffixList(getDLCmsuffix(param.getCmLast()));
                    }
                } catch (Exception e) {
                    log.error("获取宽带业务参数错误", e);
                }
            });
        }
        return cmParams;
    }

    /**
     * 获取大连网格开户宽带后缀
     *
     * @throws Exception
     */
    private List<String> getDLCmsuffix(String suffix) throws Exception {
        String[] suffixStrs = suffix.split(",");
        return Arrays.asList(suffixStrs);
    }


    protected void setCommonInfo2Resp(String city, QueSyncPercombResp resp) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("		SELECT a.FEEKIND feekind,a.PAYWAY payway,a.INSTALLTYPE installType,a.SCOPETYPE SCOPETYPE");
        sb.append("		FROM sys_common_param a");
        sb.append("		WHERE  (a.CITY = ? OR a.CITY = ? )");
        sb.append("		ORDER BY a.SORT ");
        sb.append("		LIMIT 1");
        List<PercombCommonInfo> datas = getDAO().find(sb.toString(), PercombCommonInfo.class, city, "*");
        datas.parallelStream().forEach(info -> {
//			PercombCommonInfo info = datas.get(0);
            try {
                List<PrvSysparam> charges = getParams(feekindGcode, info.getFeekind());
                List<PrvSysparam> paymentList = getParams(feeWayGcode, info.getPayway());
                List<PrvSysparam> installTypeList = getParams(installTypeGcode, info.getInstallType());
                List<PrvSysparam> scopetypeList = getParams(scopetypeGcode, info.getScopetype());
                resp.setCharges(charges);
                resp.setInstallationList(installTypeList);
                resp.setPaymentList(paymentList);
                resp.setScopetypeList(scopetypeList);
            } catch (Exception e) {
                log.error("获取通用数据错误", e);
            }
        });
    }

    /**
     * 检查是否含有额外信息
     *
     * @throws Exception
     */
    protected List<PercombOnceParam> checkExtraInfo(Long recid) throws Exception {
        String sql = "SELECT a.PCODE pcode,a.PNAME pName,a.SUM sum FROM sys_oncefee_param a WHERE a.PP_RECID =  ?";
        List<PercombOnceParam> onceParams = getDAO().find(sql, PercombOnceParam.class, recid);
        if (onceParams != null && !onceParams.isEmpty()) {
            for (int i = 0; i < onceParams.size(); i++) {
                PercombOnceParam onceParam = onceParams.get(i);
                String[] sums = onceParam.getSum().split(",");
                onceParam.setSums(Arrays.asList(sums));
            }
        }
        return onceParams;
    }

    /**
     * 实时开户接口 （移网接口）
     *
     * @return
     * @throws Exception
     */
    public ReturnInfo syncApplyInstall(SyncApplyInstallReq req, SyncApplyInstallResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        String sql = "select bl.biz_code, bl.lock_value from biz_lock bl where bl.biz_code = ? and lock_value = ?";
        List<BizLock> lockList = getDAO().find(sql, BizLock.class, BIZ_LOCK_CODE.INTER_SYNC_APPLY, req.getCustid());
        if (CollectionUtil.isNotEmpty(lockList)) {
            throw new BusinessException(String.format("请勿重复提交，该客户的开户请求已在处理中，请关闭本提醒，耐心等待页面跳转%s", req.getCustid()));
        }
        // 暂不考虑高并发，只做简单的防重调用
        BizLock bizLock = new BizLock();
        bizLock.setBizCode(BIZ_LOCK_CODE.INTER_SYNC_APPLY);
        bizLock.setLockValue(String.valueOf(req.getCustid()));
        getDAO().save(bizLock);
        try {
            // 校验代理商状态
            this.judgeAgentStatus(req.getBizorderid());
            LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
            CheckUtils.checkNull(loginInfo, CommonNotice.LOGIN_OUT_NOTICE);
            CheckUtils.checkNull(req, "请求对象不能为空");
            CheckUtils.checkEmpty(req.getBizorderid(), "业务订单号不能为空");
            CheckUtils.checkEmpty(req.getPoid() + "", "业务流程编号不能为空");
            CheckUtils.checkNull(req.getInstallparams(), "报装信息参数不能为空");
            if (req.getUserparams() == null || req.getUserparams().isEmpty()) {
                CheckUtils.checkNull(null, "用户参数信息不能为空");
            }

            // 判断之前是否有调用过
            List<CustOrder> existCustOrderList = DAO.find(SqlTplUtils.getSql("biz_custorder.find", StringObjectMapBuilder.builder()
                    .put("poid", req.getPoid())
                    .build()), CustOrder.class);
            if (CollectionUtil.isNotEmpty(existCustOrderList)) {
                resp.setSerialno(existCustOrderList.get(0).getBossserialno());
                resp.setCustOrderid(String.valueOf(existCustOrderList.get(0).getId()));
                return returnInfo;
            }
//            List<BizProcessOrderDetailBO> orderDetailList = DAO.find(SqlTplUtils.getSql("biz_process_order_detail.find", StringObjectMapBuilder.builder()
//                    .put("poid", req.getPoid())
//                    .put("cpcode", "GDKH")
//                    .build()), BizProcessOrderDetailBO.class);
//            if (!CollectionUtils.isEmpty(orderDetailList)) {
//                BizProcessOrderDetailBO detailBO = orderDetailList.get(0);
//                Integer status = detailBO.getStatus();
//                if (BizProcessOrderDetailBO.POD_STATUS_DONE == status) {
//                    JSONObject obj = JSON.parseObject(detailBO.getData());
//                    resp.setSerialno(obj.getString("serialno"));
//                    resp.setCustOrderid(obj.getString("custOrderid"));
//                    return returnInfo;
//                }
//            }

            InstallUserParam userParams = req.getUserparams().get(0);
            BizPortalOrder bizPortalOrder = register4PortalOrder(Long.parseLong(req.getBizorderid()), req.getPoid());
            CustOrder custOrder = register4Custoer(req, loginInfo, bizPortalOrder);
            ApplyInstall applyInstall = register4ApplyInstall(custOrder, req, loginInfo);

            List<ApplyProduct> applyProducts = register4ApplyPrds(req, loginInfo, custOrder);

            if (req.getBankparams() != null && req.getBankparams().size() > 0) {
                ApplyBank applyBank = register4ApplyBank(req.getBankparams().get(0), loginInfo, custOrder.getId(),
                        userParams.getPayway());
            }
            register4SelectPrd(req.getSelectProducts(), loginInfo, custOrder.getId());

            SyncChlInstallReq req2Boss = getSyncChinstallReq(req, loginInfo, applyProducts, custOrder.getId());

            // 由于 5G开户只有一个商品以固促移的订购对象可以直接添加，若以后有多商品订购，再改造
            List<SyncPrdParam> prdparams = req2Boss.getPrdparams();
            if (prdparams != null && !prdparams.isEmpty()) {
                if (prdparams.size() > 1) {
                    prdparams.get(prdparams.size() - 1).setDigituser(req.getDigituser());
                    prdparams.get(prdparams.size() - 1).setMixservids(req.getMixservids());
                } else {
                    prdparams.get(0).setDigituser(req.getDigituser());
                    prdparams.get(0).setMixservids("");
                }
            }

            String resultPutInfo = getBossHttpInfOutput(req.getBizorderid(), BossInterfaceService.BIZ_CHL_INSTALL, req2Boss,
                    loginInfo);
            // 保存 订单
            try {
                getDAO().saveOrUpdate(bizPortalOrder);
            } catch (Exception e) {
                boolean state = true;
                if (e.getMessage().contains("Duplicate entry") || e.getMessage().contains("ConstraintViolationException")) {
                    //如果流程单号重复，则可能是重读调用，则save改成update
                    List<BizPortalOrder> portalOrderList = DAO.find(SqlTplUtils.getSql("biz_portal_order.find", StringObjectMapBuilder.builder()
                            .put("poid", bizPortalOrder.getPoid())
                            .build()), BizPortalOrder.class);
                    if (!CollectionUtils.isEmpty(portalOrderList)) {
                        bizPortalOrder.setId(portalOrderList.get(0).getId());
                        getDAO().update(bizPortalOrder);
                        state = false;
                    }
                }
                if (state) {
                    throw e;
                }
            }

            try {
                getDAO().saveOrUpdate(custOrder);
            } catch (Exception e) {
                boolean state = true;
                if (e.getMessage().contains("Duplicate entry") || e.getMessage().contains("ConstraintViolationException")) {
                    //如果流程单号重复，则可能是重读调用，则save改成update
                    List<CustOrder> custOrderList = DAO.find(SqlTplUtils.getSql("biz_custorder.find", StringObjectMapBuilder.builder()
                            .put("poid", custOrder.getPoid())
                            .build()), CustOrder.class);
                    if (!CollectionUtils.isEmpty(custOrderList)) {
                        custOrder.setId(custOrderList.get(0).getId());
                        getDAO().update(custOrder);
                        state = false;
                    }
                }
                if (state) {
                    throw e;
                }
            }

            resp.setCustOrderid(String.valueOf(custOrder.getId()));
            hanlderResp(resultPutInfo, resp, bizPortalOrder, custOrder);

            //ywp 开户成功后用新地址去调用boss接口查询是否是非标准地址，isstd=N即非标准地址
		/*StringBuffer newAddrStr = new StringBuffer();
		SyncInstallAddr newAddr = req2Boss.getAddrparam();
		//开始拼接入参
		QueResHouseInterReq req1 = new QueResHouseInterReq();
		req1.setCity(req2Boss.getCity());
		req1.setAreaid(req2Boss.getAreaid()+"");
		String whladdr = req.getWhladdr();
		req1.setAddr(whladdr);
		req1.setHouseid(newAddr.getHouseid());
		req1.setIsstd("N");
		req1.setBizorderid(getNewBizOrderid());
		QueResHouseInterResp resp1 = new QueResHouseInterResp();
		callBossInf4queResHouse(req1,resp1,loginInfo);
		if(null!=resp1){
			String tableName = "biz_grid_house_"+req2Boss.getCity().toLowerCase();
			StringBuffer sql = new StringBuffer();
			sql.append("INSERT INTO biz_grid_house_cz (houseid, addrid, status, patchid, areaid, netstruct, whladdr, whgridcode, ywgridcode, gridid) ");
			sql.append(" VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);");
			List param = new ArrayList();
			String totalRecords = resp1.getTotalRecords();
			if(null!=totalRecords&&Integer.parseInt(totalRecords)>0){
				List<ResHousesBO> houses = resp1.getHouses();
				for(ResHousesBO item:houses){
					param.clear();
					param.add(item.getHouseid());
					param.add(item.getHouseno());
					param.add(item.getStatus());
					param.add(item.getPatchid());
					param.add(item.getAreaid());
					param.add("0");//netstruct先用0测试
					param.add(item.getWhladdr());
					param.add("A2");//whgridcode先用A2测试
					param.add("CZ");//ywgridcode先用CZ测试
					param.add("-1");
					getDAO().executeSql(sql.toString(), param.toArray());
				}
			}
		}*/
        } catch (Exception e) {
            log.error("开户出现异常", e);
            throw e;
        } finally {
            try {
                getDAO().delete(bizLock);
            }catch (Exception e) {
                log.error("清理业务锁异常", e);
            }

        }
        return returnInfo;
    }


    /**
     * （移网项目搬固网）实时开户接口  （接口名称新增）
     *
     * @return
     * @throws Exception
     */
    public ReturnInfo syncApplyFixedInstall(SyncApplyInstallReq req, SyncApplyInstallResp resp) throws Exception {
        this.judgeAgentStatus(req.getBizorderid());
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req, "请求对象不能为空");
        CheckUtils.checkEmpty(req.getBizorderid(), "业务订单号不能为空");
        CheckUtils.checkNull(req.getInstallparams(), "报装信息参数不能为空");

        if(!BizConstant.BizOpcode.BIZ_USER_INVERSION.equals(req.getOpcode())){
            if (req.getUserparams() == null || req.getUserparams().isEmpty()) {
                CheckUtils.checkNull(null, "用户参数信息不能为空");
            }
        }/*else{
            //倒装机开户模式做转换  0 表示新开户
            if(req.getInstallparams() != null && req.getInstallparams().size() > 0){
                req.getInstallparams().get(0).setOmode("0");
            }
        }*/

        //如果没传addrs 传了addr11 通过外部的houseid和addr11 拼出addrs
        if (StringUtils.isNotBlank(req.getAddr11()) && org.apache.commons.collections.CollectionUtils.isEmpty(req.getAddrs())) {
            req.setAddrs(new ArrayList<>());
            SyncInstallAddr syncInstallAddr = new SyncInstallAddr();
            syncInstallAddr.setHouseid(req.getHouseid());
            syncInstallAddr.setAddr11(req.getAddr11());
            req.getAddrs().add(syncInstallAddr);
        }

        InstallUserParam userParams = req.getUserparams().get(0);
//        ChlInstallParam chlInstallParam = req.getInstallparams().get(0);
        BizPortalOrder bizPortalOrder = register4PortalOrder(Long.parseLong(req.getBizorderid()));
        CustOrder custOrder = register4FixedCustoer(req, loginInfo, bizPortalOrder);

        //更新证件数据
        interOrderService.updateCustOrderCardPhoto(custOrder.getId(), req.getCustorderCardId());
        ApplyInstall applyInstall = register4ApplyInstall(custOrder, req, loginInfo);
        List<ApplyProduct> applyProducts = register4ApplyPrds(req, loginInfo, custOrder);
        if (req.getBankparams() != null && req.getBankparams().size() > 0) {
            ApplyBank applyBank = register4ApplyBank(req.getBankparams().get(0), loginInfo, custOrder.getId(),
                    userParams.getPayway());
        }
        register4SelectPrd(req.getSelectProducts(), loginInfo, custOrder.getId());

        SyncChlInstallReq req2Boss = getSyncChinstallReq(req, loginInfo, applyProducts, custOrder.getId());

        getEquityPoolReqBoss(req2Boss, req);

        String resultPutInfo = getBossHttpInfOutput(req.getBizorderid(), BossInterfaceService.BIZ_CHL_INSTALL, req2Boss,
                loginInfo);
        resp.setCustOrderid(req.getBizorderid());
        hanlderResp(resultPutInfo, resp, bizPortalOrder, custOrder);

        return returnInfo;
    }


    /**
     * 5G乐享参数
     * @param req2Boss
     * @param req
     */
    public void getEquityPoolReqBoss(SyncChlInstallReq req2Boss, SyncApplyInstallReq req) {
        List<SyncPrdParam> prdparams = req2Boss.getPrdparams();
        if (prdparams != null && !prdparams.isEmpty()) {
            for (SyncPrdParam prdparam : req2Boss.getPrdparams()) {
                prdparam.setPsiprddets(req.getPsiprddets()); // 进销存商品信息
                prdparam.setPoolSelList(req.getPoolSelList()); // 营销方案自选池商品信息列表
                prdparam.setPrdseltdet(req.getPrdseltdet()); // M选N产品参数
                prdparam.setDelivermoder(req.getDelivermoder()); //硬件商品配送方式
                prdparam.setReceiptname(req.getReceiptname()); //收货人
                prdparam.setReceipttel(req.getReceipttel()); //电话
                prdparam.setReceiptaddr(req.getReceiptaddr()); //收货地址
                prdparam.setSalespkgcode(req.getSalespkgcode());
            }
        }

    }


    /**
     * 3.2.22.	以固促移固网对象查询接口
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queFixorderList(QueFixorderListReq req, QueFixorderListResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        List<Fixorder> fixorders = queFixorderList(req.getCustid(), req.getSalesid(), req.getBizcode(), loginInfo, req.getBizorderid());
        resp.setData(fixorders);

        return returnInfo;
    }

    public List<Fixorder> queFixorderList(String custid, String salesid, String bizcode, LoginInfo loginInfo, String bizorderid) throws Exception {
        CheckUtils.checkEmpty(custid, "Custid不能为空");
//        CheckUtils.checkEmpty(salesid, "salesid不能为空");
        CheckUtils.checkEmpty(bizcode, "bizcode不能为空");

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("custid", custid);
        bossreq.put("salesid", salesid);
        bossreq.put("bizcode", bizcode);

        String output = getBossHttpInfOutput(bizorderid, M5gInterface.QUE_FIXORDER_LIST, bossreq, loginInfo);

        List<Fixorder> fixorders = JSON.parseObject(output, new TypeReference<List<Fixorder>>() {
        });

        return fixorders == null ? new ArrayList<>() : fixorders;
    }


    /**
     * 查询发展人类型为0-代理商发展时，可以选的发展渠道
     * 选择“0-代理商发展”，发展渠道可下拉选择的代理商，查prv_department，kind=4 and city=操作员所属地市
     *
     * @param resp
     * @return
     * @throws Exception
     */
    @Transactional(readOnly = true)
    public ReturnInfo queAgentDeveloperType(QueAgentDeveloperTypeResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);


        StringBuffer sql = new StringBuffer();
        List paramList = new ArrayList();

        sql.append(" select a.deptid mcode,a.`name` mname from prv_department a where a.kind = 4 and a.city = ? ");
        paramList.add(loginInfo.getCity());

        List<QueAgentDeveloperTypeBO> queAgentDeveloperTypeBOList = getDAO().find(sql.toString(), QueAgentDeveloperTypeBO.class,
                paramList.toArray());

        resp.setQueAgentDeveloperTypeList(queAgentDeveloperTypeBOList);

        return returnInfo;
    }

    /**
     * 查询相同部门的操作人员
     *
     * @param resp
     * @return
     * @throws Exception
     */
    @Transactional(readOnly = true)
    public ReturnInfo queSameDeptOperList(QueSameDeptOperListResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);


        StringBuffer sql = new StringBuffer();
        List paramList = new ArrayList();


        sql.append(" select a.operid mcode,a.`name` mname from prv_operator a ,prv_department b ,prv_operdept c ");
        sql.append(" where a.operid = c.operid and b.deptid = c.depid and c.depid = ? ");
        paramList.add(loginInfo.getDeptid());

        List<QueSameDeptOperBO> queAgentDeveloperTypeBOList = getDAO().find(sql.toString(), QueSameDeptOperBO.class,
                paramList.toArray());

        resp.setSameDeptOperList(queAgentDeveloperTypeBOList);

        return returnInfo;
    }

    /**
     * 查询相同部门的操作人员
     *
     * @param resp
     * @return
     * @throws Exception
     */
    @Transactional(readOnly = true)
    public ReturnInfo queOperExpand(QueOperExpandResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        List<OperExpandBo> list = queOperExpandList(loginInfo);
        String rootPid = "1";

        // 整理成 发展渠道 - 发展人 层级关系
        formatOperExpandList(list, rootPid);

        // 删掉没有员工的部门
        removeNoChild(list);

        resp.setOperExpandBoList(list);

        return returnInfo;
    }

    /**
     * 删除没有发展人的渠道
     *
     * @param list
     */
    private void removeNoChild(List<OperExpandBo> list) {
        if (list != null && !list.isEmpty()) {
            Iterator<OperExpandBo> iterator = list.iterator();
            while (iterator.hasNext()) {
                OperExpandBo next = iterator.next();
                Set<OperExpandBo> child = next.getChild();
                if (child == null || child.isEmpty()) {
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 整理成 发展渠道 - 发展人 层级关系
     *
     * @param list
     * @param rootPid
     */
    private void formatOperExpandList(List<OperExpandBo> list, String rootPid) {
        if (list != null && !list.isEmpty()) {
            // 渠道Map
            Map<String, OperExpandBo> deptMap = new HashMap<>();
            Iterator<OperExpandBo> iterator = list.iterator();
            while (iterator.hasNext()) {
                // 这里查询的时候已经做了排序，发展渠道在发展人前面，
                // 这里直接循环就好，不考虑发展人在发展渠道前面的情况
                OperExpandBo next = iterator.next();
                if (StringUtils.equalsIgnoreCase(rootPid, next.getPid())) {
                    // rootPid 匹配的为发展渠道
                    String mcode = next.getMcode();
                    if (StringUtils.isNotBlank(mcode)) {
                        OperExpandBo deptExpand = deptMap.get(mcode);
                        if (deptExpand != null) {
                            iterator.remove();
                        } else {
                            Set<OperExpandBo> child = next.getChild();
                            if (child == null) {
                                next.setChild(new HashSet<>());
                            }
                            deptMap.put(mcode, next);
                        }
                    }
                } else {
                    // 发展人
                    String pid = next.getPid();
                    OperExpandBo deptExpand = deptMap.get(pid);
                    if (deptExpand != null) {
                        Set<OperExpandBo> child = deptExpand.getChild();
                        child.add(next);
                    }
                    iterator.remove();
                }
            }
        } else {
            list = new ArrayList<>();
        }
    }

    /**
     * 查询员工发展
     *
     * @param loginInfo
     * @return
     */
    private List<OperExpandBo> queOperExpandList(LoginInfo loginInfo) throws Exception {
        String kindListStr = "1,6";
        Rule expandDeptKind = ruleService.getCityRuleOrDefault("Expand_Dept_Kind", loginInfo.getCity());
        if (expandDeptKind != null && StringUtils.isNotBlank(expandDeptKind.getValue())) {
            kindListStr = expandDeptKind.getValue();
        }

        List<String> kindList = getKindList(kindListStr);


        StringBuffer sqlSb = new StringBuffer();
        List params = new ArrayList();
        sqlSb.append(" select * from ( ");
        sqlSb.append(" select pd.deptid mcode, pd.name mname, 1 pid from prv_department pd where pd.areaid = ? ");
        params.add(loginInfo.getAreaid());
        if (kindList != null && !kindList.isEmpty()) {
            sqlSb.append(" and pd.kind in ").append(getInParam(kindList));
            params.addAll(kindList);
        }

        sqlSb.append(" union all ");
        sqlSb.append(" select po.operid mcode, po.name mname, pd.deptid pid from prv_operator po, prv_department pd, prv_operdept po2 ");
        sqlSb.append(" where po.operid = po2.operid and pd.deptid = po2.depid and po.status = 0 and pd.areaid = ? ");
        params.add(loginInfo.getAreaid());
        if (kindList != null && !kindList.isEmpty()) {
            sqlSb.append(" and pd.kind in ").append(getInParam(kindList));
            params.addAll(kindList);
        }
        sqlSb.append(" ) t order by t.pid, t.mcode ");

        List list = getDAO().find(sqlSb.toString(), OperExpandBo.class, params.toArray());

        return list;
    }

    private String getInParam(List<String> kindList) {
        String ret = "(";
        for (int i = 0; i < kindList.size(); i++) {
            ret += "?";
            if (i < kindList.size() - 1) {
                ret += ",";
            }
        }
        ret += ")";
        return ret;
    }

    /**
     * 获得部门类型列表
     *
     * @param kindListStr
     * @return
     */
    private List<String> getKindList(String kindListStr) {
        String[] kinds = StringUtils.split(kindListStr, ",");
        if (kinds != null && kinds.length > 0) {
            List<String> kindList = new ArrayList<>();
            for (String kind : kinds) {
                if (StringUtils.isNotBlank(kind)) {
                    kindList.add(StringUtils.trim(kind));
                }
            }
            return kindList;
        }
        return null;
    }

    private String getNewBizOrderid() throws Exception {
//		return getDAO().getSequence("SEQ_BIZ_CUSTORDER_ID").toString();
        return getBizorderid();
    }

    private void callBossInf4queResHouse(QueResHouseInterReq req, QueResHouseInterResp resp, LoginInfo loginInfo)
            throws Exception {

        // 将请求做一下转换，并赋默认值
        QueResHouseBossReq req2Boss = getQueResHouseInterReq2Boss(req);

        String bossRespOutput = getBossHttpInfOutput(req.getBizorderid(), BossInterfaceService.QUE_RESHOUSE,
                req2Boss, loginInfo);

        copyBossResp2InterResp4queResHouse(resp, bossRespOutput);

    }

    private void copyBossResp2InterResp4queResHouse(QueResHouseInterResp resp, String jsonStr) throws Exception {
        QueResHouseBossResp bossResp = (QueResHouseBossResp) BeanUtil.jsonToObject(jsonStr, QueResHouseBossResp.class);

        // 因为字段是一样的，这里可以偷懒一下，
        // 后面如果接口对外字段和boss返回字段不一致，则自行写使用set方法设置值
        BeanUtils.copyProperties(resp, bossResp);
        // resp.setAcctkind(bossResp.getAcctkind());

    }

    private QueResHouseBossReq getQueResHouseInterReq2Boss(QueResHouseInterReq req) {

        QueResHouseBossReq queResHouseBossReq = new QueResHouseBossReq();
        queResHouseBossReq.setAddr(req.getAddr());
        queResHouseBossReq.setAreaid(req.getAreaid());
        queResHouseBossReq.setCity(req.getCity());
        queResHouseBossReq.setCurrentPage(req.getCurrentPage());
        queResHouseBossReq.setPagesize(req.getPagesize());
        queResHouseBossReq.setPatch(req.getPatch());
        queResHouseBossReq.setRightControl(req.getRightControl());
        queResHouseBossReq.setHouseid(req.getHouseid());
        queResHouseBossReq.setHouseid(req.getHouseid());
        queResHouseBossReq.setIsstd(req.getIsstd());
        return queResHouseBossReq;
    }
//	private void setInstallparams(SyncApplyInstallReq req){
//		ChlInstallParam installParam = req.getInstallparams().get(0);
//		installParam.setFitkind(req.getFitkind());
//		installParam.setFitattr(req.getFitattr());
//		installParam.setFitpid(req.getFitpid());
//		installParam.setFituseprop(req.getFituseprop());
//
//		installParam.setExdevList(req.getExdevList());
//		installParam.setRecycleFitList(req.getRecycleFitList());
//	}

    protected void hanlderResp(String jsonStr, SyncApplyInstallResp resp, BizPortalOrder bizPortalOrder, CustOrder custOrder)
            throws Exception {
        SyncChlInstallResp resp2Boss = JSON.parseObject(jsonStr, SyncChlInstallResp.class);
//		SyncChlInstallResp resp2Boss = (SyncChlInstallResp) BeanUtil.jsonToObject(jsonStr, SyncChlInstallResp.class);
        if (resp2Boss.getSplitInfoList() != null && !resp2Boss.getSplitInfoList().isEmpty()
                && custOrder.getId() != null) {
            saveOrderSource(custOrder.getId(), BizConstant.SourceCode.SPLIT_INFO, JSON.toJSONString(resp2Boss.getSplitInfoList()));
        }
        bizPortalOrder.setResporderid(Long.parseLong(resp2Boss.getOrderid()));
        bizPortalOrder.setFees(resp2Boss.getSums());
        custOrder.setBossserialno(resp2Boss.getSerialno());
        custOrder.setOrderstatus(BizCustorderOrderstatus.SYNC);
        custOrder.setBusinessdescipt(String.format("%s,%s", custOrder.getBusinessdescipt() == null ? "" : custOrder.getBusinessdescipt(),
                resp2Boss.getNewhouseid() == null ? "" : resp2Boss.getNewhouseid()));
        resp.setSums(resp2Boss.getSums());
        resp.setFeesInfos(resp2Boss.getFeesInfos());
        resp.setSerialno(resp2Boss.getSerialno());

        getDAO().update(bizPortalOrder);
        getDAO().update(custOrder);

        setGwPayOrderInfo(custOrder.getId(), resp2Boss.getGwPayOrderInfo());

    }

    private void setGwPayOrderInfo(Long id, JSONObject gwPayOrderInfo) throws Exception {
        if (gwPayOrderInfo == null) {
            return;
        }
        BizOrderSource orderSource = new BizOrderSource();
        orderSource.setCode(SourceCode.GW_PAY_ORDERINFO);
        orderSource.setOrderid(id);
        orderSource.setContent(gwPayOrderInfo.toJSONString());
        getDAO().save(orderSource);
    }


    /**
     * 写入业务轨迹表
     *
     * @param req
     * @param loginInfo
     * @return
     * @throws Exception
     */
    protected CustOrder register4Custoer(SyncApplyInstallReq req, LoginInfo loginInfo, BizPortalOrder bizPortalOrder) throws Exception {
        CustOrder custOrder = new CustOrder();
        custOrder.setId(Long.parseLong(req.getBizorderid()));
        custOrder.setAddr(req.getWhladdr());
        custOrder.setAreaid(loginInfo.getAreaid());
        custOrder.setCity(loginInfo.getCity());
        custOrder.setPoid(req.getPoid());
        if (StringUtils.isNotBlank(req.getDescribe())) {
            custOrder.setDescrip(req.getDescribe());
        } else if (StringUtils.isNotBlank(req.getMemo())) {
            custOrder.setDescrip(req.getMemo());
        }
        custOrder.setName(req.getCustname());

        String permark = req.getUserparams().get(0).getPermark();
        if (permark.equals("B")) {
            custOrder.setOpcode(BizOpcode.BIZ_FGUSER_NEW);
        } else {
            custOrder.setOpcode(BizOpcode.BIZ_USER_NEW);
        }

        custOrder.setOperator(loginInfo.getOperid());
        custOrder.setOprdep(loginInfo.getDeptid());
        custOrder.setOptime(new Date());
        custOrder.setOrdercode(pubService.getOrderCode());
        custOrder.setOrderstatus(BizCustorderOrderstatus.INIT);
        custOrder.setSyncmode(BizCustorderSyncmode.SYNC);
        custOrder.setVerifyphone(req.getVerifyphone());
        custOrder.setCustid(req.getCustid());
        custOrder.setBusinessdescipt(req.getUserparams().get(0).getInstalltype());
        custOrder.setPortalOrder(null);
        custOrder.setPreacceptinfo(req.getPreacceptinfo());

        // 保存发展人信息
        if (req.getDeveloper() != null) {
            custOrder.setDevname(req.getDeveloper().getDevname());
            custOrder.setDeveloper(JSON.toJSONString(req.getDeveloper()));
        }

        //getDAO().save(custOrder);
        return custOrder;
    }

    /**
     * 写入业务轨迹表(搬固网代码，移网有改造，不再公用)
     *
     * @param req
     * @param loginInfo
     * @return
     * @throws Exception
     */
    protected CustOrder register4FixedCustoer(SyncApplyInstallReq req, LoginInfo loginInfo, BizPortalOrder bizPortalOrder) throws Exception {
        CustOrder custOrder = new CustOrder();
        custOrder.setId(Long.parseLong(req.getBizorderid()));
        custOrder.setAddr(req.getWhladdr());
        custOrder.setAreaid(loginInfo.getAreaid());
        custOrder.setCity(loginInfo.getCity());
        custOrder.setPoid(req.getPoid());
        if (StringUtils.isNotBlank(req.getDescribe())) {
            custOrder.setDescrip(req.getDescribe());
        } else if (StringUtils.isNotBlank(req.getMemo())) {
            custOrder.setDescrip(req.getMemo());
        }
        custOrder.setName(req.getCustname());

        String permark = req.getUserparams().get(0).getPermark();
        if (permark.equals("B")) {
            custOrder.setOpcode(BizOpcode.BIZ_FGUSER_NEW);
        } else {
            custOrder.setOpcode(BizOpcode.BIZ_USER_NEW);
        }

        custOrder.setOperator(loginInfo.getOperid());
        custOrder.setOprdep(loginInfo.getDeptid());
        custOrder.setOptime(new Date());
        custOrder.setOrdercode(pubService.getOrderCode());
        custOrder.setOrderstatus(BizCustorderOrderstatus.INIT);
        custOrder.setSyncmode(BizCustorderSyncmode.SYNC);
        custOrder.setVerifyphone(req.getVerifyphone());
        custOrder.setCustid(req.getCustid());
        custOrder.setBusinessdescipt(req.getUserparams().get(0).getInstalltype());
        custOrder.setPortalOrder(null);
        custOrder.setPreacceptinfo(req.getPreacceptinfo());

        // 保存发展人信息
        if (req.getDeveloper() != null) {
            custOrder.setDevname(req.getDeveloper().getDevname());
            custOrder.setDeveloper(JSON.toJSONString(req.getDeveloper()));
        }

        getDAO().save(custOrder);
        return custOrder;
    }

    /**
     * 写入订单账单表
     *
     * @param
     * @param
     * @param orderid
     * @return
     * @throws Exception
     */
    protected BizPortalOrder register4PortalOrder(Long orderid, Long poid) throws Exception {
        BizPortalOrder portalOrder = new BizPortalOrder();
        portalOrder.setCreatetime(new Date());
        portalOrder.setOrdertype(PORTAL_ORDER_ORDERTYPE.PORTAL_ORDER_TYPE_PRD);
        portalOrder.setId(orderid);
        portalOrder.setPayid(orderid);
        portalOrder.setPoid(poid);
        portalOrder.setStatus(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_NOPAY);
//		getDAO().save(portalOrder);
        return portalOrder;
    }

    protected BizPortalOrder register4PortalOrder(Long orderid) throws Exception {
        BizPortalOrder portalOrder = new BizPortalOrder();
        portalOrder.setCreatetime(new Date());
        portalOrder.setOrdertype(PORTAL_ORDER_ORDERTYPE.PORTAL_ORDER_TYPE_PRD);
        portalOrder.setId(orderid);
        portalOrder.setPayid(orderid);
        portalOrder.setStatus(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_NOPAY);
        getDAO().save(portalOrder);
        return portalOrder;
    }

    /**
     * 写入报装参数表
     *
     * @param custOrder
     * @param req
     * @param loginInfo
     * @return
     * @throws Exception
     */
    protected ApplyInstall register4ApplyInstall(CustOrder custOrder, SyncApplyInstallReq req, LoginInfo loginInfo)
            throws Exception {
        ApplyInstall applyInstall = new ApplyInstall();
        applyInstall.setAreaid(loginInfo.getAreaid());
        applyInstall.setCity(loginInfo.getCity());
        applyInstall.setCustid(custOrder.getCustid());
        ChlInstallParam installParam = req.getInstallparams().get(0);
        applyInstall.setHouseid(installParam.getHouseid());
        applyInstall.setLinkphone(installParam.getLinkphone());
        applyInstall.setLogicdevno(installParam.getLogicdevno());
        applyInstall.setName(req.getCustname());
        applyInstall.setOlogicdevno(installParam.getOlogicdevno());
        applyInstall.setOrderid(custOrder.getId());
        if (StringUtils.isNotBlank(installParam.getOservid())) {
            applyInstall.setOservid(Long.parseLong(installParam.getOservid()));
        }

        if (StringUtils.isNotBlank(req.getPatchid())) {
            applyInstall.setPatchid(Long.parseLong(req.getPatchid()));
        }
        applyInstall.setPercomb(installParam.getPercomb());
        applyInstall.setPermark(installParam.getPermark());

        Date preDate = null;
        try {
            String predate = installParam.getPredate();
            if (StringUtils.isNotBlank(predate)) {
                preDate = DateUtils.parseDate(predate);
            }
            applyInstall.setPredate(preDate);
        } catch (Exception e) {
            log.error("InterSyncApplyService[1175]:" + e.getMessage());
        }
        applyInstall.setPservid(installParam.getPservid());
        applyInstall.setSmuseprop(installParam.getSmnouseprop());
        applyInstall.setStbback(installParam.getStbback());
        applyInstall.setStbno(installParam.getStbno());
        applyInstall.setWhladdr(custOrder.getAddr());
        getDAO().save(applyInstall);
        return applyInstall;
    }

    protected List<ApplyProduct> register4ApplyPrds(SyncApplyInstallReq req, LoginInfo loginInfo, CustOrder custOrder)
            throws Exception {

        List<ApplyProduct> retList = new ArrayList();
        String[] knowidArray = req.getKnowids().split(",");
        String[] countArray = req.getCounts().split(",");
        String[] unitArray = req.getUnits().split(",");
        String[] mindateArray = req.getMindates().split(",");
        String[] ispostponeArray = req.getIspostpones().split(",");

        String[] servidsArray = req.getServids().split(",");


        if (null == knowidArray || knowidArray.length <= 0) {
            CheckUtils.checkNull(null, "登记报装申请产品信息:产品信息不能为空");
        }
        if (countArray == null || countArray.length <= 0) {
            CheckUtils.checkNull(null, "登记报装申请产品信息:产品订购周期不能为空");
        }
        if (unitArray == null || unitArray.length <= 0) {
            CheckUtils.checkNull(null, "登记报装申请产品信息:产品周期单位不能为空");
        }
        if (ispostponeArray == null || ispostponeArray.length <= 0) {
            CheckUtils.checkNull(null, "登记报装申请产品信息:顺延标识不能为空");
        }
        if (knowidArray.length != countArray.length) {
            CheckUtils.checkNull(null, "登记报装申请产品信息:产品订购周期与产品数不符");
        }
        if (unitArray.length != countArray.length) {
            CheckUtils.checkNull(null, "登记报装申请产品信息:产品订购周期数与产品单位数不符");
        }
        if (ispostponeArray.length != countArray.length) {
            CheckUtils.checkNull(null, "登记报装申请产品信息:产品订购周期数与顺延标识不符");
        }
        if (servidsArray.length != countArray.length) {
            CheckUtils.checkNull(null, "登记报装申请产品信息:用户id与顺延标识不符");
        }

        String[] stimeArray;

        if (StringUtils.isBlank(req.getStimes())) {
            stimeArray = new String[knowidArray.length];
        } else {
            stimeArray = req.getStimes().split(",", -1);
            if (stimeArray.length != countArray.length) {
                CheckUtils.checkNull(null, "登记报装申请产品信息:产品订购周期数与预约开始时间不符");
            }
        }
        ChlInstallParam installParam = req.getInstallparams().get(0);
        for (int i = 0; i < knowidArray.length; i++) {
            String knowid = knowidArray[i];
            String count = countArray[i];
            String unit = unitArray[i];
            String ispostpone = ispostponeArray[i];
            String stime = StringUtils.isBlank(stimeArray[i]) ? null : stimeArray[i];
            String mindate = mindateArray[i];
            String servid = servidsArray[i];
            if (StringUtils.isBlank(knowid)) {
                continue;
            }

            SalespkgKnowObjInfoBO knowObjInfo = null;
            String permark = req.getInstallparams().get(0).getPermark();
            if (StringUtils.isNotBlank(permark) && StringUtils.contains(permark, PermarkType.M5G)) {
                knowObjInfo = interApplyService.getM5gSalespkgKnowObjInfo(knowid);
            } else {
                knowObjInfo = interApplyService.getSalespkgKnowObjInfo(knowid);
            }


            CheckUtils.checkNull(knowObjInfo, "登记报装申请产品信息:根据营销标识[" + knowid + "]查询不营销配置信息");
            ApplyProduct product = register4Prd(knowObjInfo, loginInfo, Long.parseLong(count), Long.parseLong(knowid),
                    unit, installParam.getLogicdevno(), custOrder.getId(), ispostpone, stime, mindate, servid);
            if (req.getHarddevnobos() != null && !req.getHarddevnobos().isEmpty()) {
                product.setHarddevnobos(req.getHarddevnobos().get(i));
            }

            // 5G 商品促销优惠
            List<RelSales> relSales = req.getRelSales();
            if (relSales != null && !relSales.isEmpty()) {
                Iterator<RelSales> iterator = relSales.iterator();
                while (iterator.hasNext()) {
                    RelSales next = iterator.next();
                    Long nextKnowid = next.getKnowid();
                    if (nextKnowid != null && StringUtils.equals(knowid, nextKnowid.longValue() + "")) {
                        if (next.getRelsalesid() != null) {
                            // 这里要把营销活动转成商品
                            Sales sales = (Sales) getDAO().find(Sales.class, next.getRelsalesid());
                            if (null == sales) {
                                throw new BusinessException(next.getRelsalesid() + "营销活动商品不存在");
                            }
                            ApplyProduct applyProduct = new ApplyProduct();
                            applyProduct.setPid(sales.getPid());
                            applyProduct.setSalesid(next.getRelsalesid());
                            applyProduct.setCount(0L);
                            applyProduct.setUnit(sales.getUnit());
                            applyProduct.setRelsalesid(product.getSalesid().toString());

                            retList.add(applyProduct);
                        }

                    }
                }
            }

            retList.add(product);
        }
        return retList;
    }

    /**
     * 写入报装产品表
     *
     * @param knowObjInfo
     * @param loginInfo
     * @param count
     * @param knowid
     * @param unit
     * @param logicdevno
     * @param orderid
     * @param servid
     * @return
     * @throws Exception
     */
    private ApplyProduct register4Prd(SalespkgKnowObjInfoBO knowObjInfo, LoginInfo loginInfo, Long count, Long knowid,
                                      String unit, String logicdevno, Long orderid, String ispostpone, String stime, String mindate, String servid) throws Exception {
        ApplyProduct bizApplyProduct = new ApplyProduct();
        bizApplyProduct.setCity(loginInfo.getCity());
        bizApplyProduct.setCount(count);
        bizApplyProduct.setKnowid(knowid);
        bizApplyProduct.setLogicdevno(logicdevno);
        bizApplyProduct.setOrderid(orderid);
        bizApplyProduct.setOstatus(BizApplyProductOstatus.ORDER);
        bizApplyProduct.setIspostpone(ispostpone);
        bizApplyProduct.setStime(stime);
        bizApplyProduct.setMindate(mindate);
        if (servid != null && !("null".equals(servid)) &&!("".equals(servid))) {
            bizApplyProduct.setServid(Long.valueOf(servid));
        }

        String permark = knowObjInfo.getPermark();
        if (StringUtils.equals(permark, PermarkType.M5G)) {
            // 设置商品id
            bizApplyProduct.setSalesid(Long.valueOf(knowObjInfo.getId()));
            bizApplyProduct.setPid(Long.valueOf(knowObjInfo.getPid()));

            List<PrvSysparam> list = SysparamCache.getSysparamList(SysparamGcodeCons.GW_TERM_UNIT);
            if (list != null && !list.isEmpty()) {
                Iterator<PrvSysparam> iterator = list.iterator();
                while (iterator.hasNext()) {
                    PrvSysparam next = iterator.next();
                    if (StringUtils.equals(next.getMname(), unit)) {
                        bizApplyProduct.setUnit(next.getData());
                        break;
                    }
                }
            }
        } else {
            if (knowObjInfo.getType().equals(PrdSalespkgKnowObjtype.PRD)) {
                bizApplyProduct.setPid(Long.parseLong(knowObjInfo.getId()));
            }

            bizApplyProduct.setSalespkgid(knowObjInfo.getType().equals(PrdSalespkgKnowObjtype.SALESPKG)
                    ? Long.valueOf(knowObjInfo.getId()) : null);
            if (knowObjInfo.getType().equals(PrdSalespkgKnowObjtype.GOODS)
                    || knowObjInfo.getType().equals(PrdSalespkgKnowObjtype.GOODS_TYPE)) {
                bizApplyProduct.setSalesid(Long.valueOf(knowObjInfo.getId()));
                if (StringUtils.isNotBlank(knowObjInfo.getPid())) {
                    bizApplyProduct.setPid(Long.parseLong(knowObjInfo.getPid()));
                }
            }

            if (unit.equals("日")) {
                bizApplyProduct.setUnit("0");
            } else if (unit.equals("月")) {
                bizApplyProduct.setUnit("1");
            } else if (unit.equals("年") || unit.equals("周期")) {
                bizApplyProduct.setUnit("2");
            } else if (unit.equals("次")) {
                bizApplyProduct.setUnit(null);
            } else {
                bizApplyProduct.setUnit(unit);
            }
        }


        getDAO().save(bizApplyProduct);
        return bizApplyProduct;
    }

    /**
     * 写入报装的银行信息
     *
     * @param param
     * @param loginInfo
     * @param orderid
     * @param payway
     * @return
     * @throws Exception
     */
    protected ApplyBank register4ApplyBank(SyncInstallBankParam param, LoginInfo loginInfo, Long orderid, String payway)
            throws Exception {
        if (param == null) return null;
        ApplyBank applyBank = new ApplyBank();
        applyBank.setAcctkind(param.getAcctkind());
        applyBank.setAcctname(param.getAcctname());
        applyBank.setAcctno(param.getAcctno());
        applyBank.setAccttype(param.getAccttype());
        applyBank.setBankcode(param.getBankcode());
        applyBank.setCity(loginInfo.getCity());
        applyBank.setOrderid(orderid);
        applyBank.setPayway(payway);
        if (StringUtils.isNotBlank(param.getServid())) {
            applyBank.setServid(Long.parseLong(param.getServid()));
        }
        getDAO().save(applyBank);
        return applyBank;
    }

    /**
     * 写入选择性产品
     *
     * @param selectProducts
     * @throws Exception
     */
    protected void register4SelectPrd(List<KnowProducts> selectProducts, LoginInfo loginInfo, Long bizOrderid)
            throws Exception {
        if (selectProducts == null || selectProducts.isEmpty())
            return;
        for (KnowProducts kProducts : selectProducts) {
            ApplyProduct applyProduct = new ApplyProduct();
            applyProduct.setKnowid(Long.parseLong(kProducts.getKnowId()));
            List<ApplyProduct> applyProductList = getDAO().find(applyProduct);
            if (applyProductList != null && applyProductList.size() > 0 && applyProductList.get(0) != null) {
                applyProduct = applyProductList.get(0);
                for (SelectProduct product : kProducts.getProducts()) {
                    ApplyProductSelect applyProductSelect = new ApplyProductSelect();
                    applyProductSelect.setCity(loginInfo.getCity());
                    applyProductSelect.setKnowid(Long.parseLong(kProducts.getKnowId()));
                    applyProductSelect.setOrderid(bizOrderid);
                    applyProductSelect.setPrecid(applyProduct.getRecid());
                    applyProductSelect.setPid(product.getProductId());
                    applyProductSelect.setSelectid(Long.parseLong(kProducts.getSelectId()));
                    getDAO().save(applyProductSelect);
                }
            }
        }
    }

    private SyncChlInstallReq getSyncChinstallReq(SyncApplyInstallReq req, LoginInfo loginInfo,
                                                  List<ApplyProduct> products, Long orderid) throws Exception {
        SyncChlInstallReq req2Boss = new SyncChlInstallReq();
        if (req.getAddrs() != null && !req.getAddrs().isEmpty()) {
            req2Boss.setAddrparam(req.getAddrs().get(0));
        }

        req2Boss.setAreaid(loginInfo.getAreaid());
        req2Boss.setBankparams(req.getBankparams());
        req2Boss.setCity(loginInfo.getCity());
        req2Boss.setCustid(req.getCustid());
        req2Boss.setDescribe(req.getDescribe());
        req2Boss.setWgserialno(req.getWgserialno());
        //代办开户必传：BIZ_FGB_AGENTUSER_NEW
        req2Boss.setBizCode(req.getBizCode());

        //bankid 传0 会报错，如果遇到0就置为null --张耀其 2019-11-08
        try {
            if ("0".equals(req.getInstallparams().get(0).getBankid())) {
                req.getInstallparams().get(0).setBankid(null);
            }
        } catch (Exception e) {

        }
        req2Boss.setInstallparams(req.getInstallparams());
        req2Boss.setIscrtorder(req.getIscrtorder());
        req2Boss.setMemo(req.getMemo());
        req2Boss.setOncefeeparams(req.getOncefeeparams());
        req2Boss.setOpcode(req.getOpcode());
        req2Boss.setOperator(loginInfo.getOperid());
        req2Boss.setOprdep(loginInfo.getDeptid());
        req2Boss.setOptime(req.getOptime());
        req2Boss.setPrdparams(getPrdParams(products, req.getInstallparams().get(0), orderid));
        req2Boss.setSystemid(req.getSystemid());
        req2Boss.setUserparams(req.getUserparams());
//		req2Boss.setFitattr(req.getFitattr());
//		req2Boss.setFitkind(req.getFitkind());
//		req2Boss.setFitpid(req.getFitpid());
//		req2Boss.setFituseprop(req.getFituseprop());
        req2Boss.setIscrtorder("Y");
        req2Boss.setPayway(req.getPayway());
        if ("H".equals(req.getPayway())) {
            req2Boss.setSubpayway("042700");
        } else if ("G".equals(req.getPayway())) {
            req2Boss.setSubpayway("043700");
        } else if ("44".equals(req.getPayway())) {
            req2Boss.setSubpayway("");
        }
		/*if(null != req2Boss.getAddrparam() && StringUtils.isBlank(req2Boss.getAddrparam().getHouseid())) {
			req2Boss.getAddrparam().setHouseid(houseid);
		}*/
//		req2Boss.setExdevList(req.getExdevList());
//		req2Boss.setRecycleFitList(req.getRecycleFitList());

        //ywp后面要去掉R，如果kind不为空，则改为R
//		if(null!=req2Boss.getExdevList()){
//			for(ExdevList item:req2Boss.getExdevList()){
//				if(null!=item.getKind()&&!"null".equals(item.getKind())){
//					item.setKind("R");
//				}
//			}
//		}
        if (req2Boss.getInstallparams() != null && req2Boss.getInstallparams().size() > 0) {
            if (null != req2Boss.getInstallparams().get(0).getExdevList()) {
                for (ExdevList item : req2Boss.getInstallparams().get(0).getExdevList()) {
                    if (null != item.getKind()
                            && !"null".equals(item.getKind())) {
                        //是否OTTr-2转换
                        Rule rule = ruleService.getRule("*", ottRtoTwoSwitch);
                        if (rule != null && rule.getValue() != null) {
                            if (rule.getValue().equalsIgnoreCase("Y")) {
                                item.setKind("2");
                                item.setIsopen("N");
                            } else {
                                item.setKind("R");
                            }
                        }

                    }

                }
            }
        }

        //是否缴清欠费
        if (StringUtils.isEmpty(req.getBizfeein())) {
            req2Boss.setBizfeein("Y");
        } else {
            req2Boss.setBizfeein(req.getBizfeein());
        }

        //退订参数
        if (req.getEjectparams() != null) {

            req2Boss.setEjectparams(req.getEjectparams());
        }


        req2Boss.setDeveloper(req.getDeveloper());

        req2Boss.setPreacceptserialno(req.getPreacceptserialno());

        req2Boss.setStoreplace(req.getStoreplace());

        //校园卡参数
        req2Boss.setCellscode(req.getCellscode());
        req2Boss.setCollcode(req.getCollcode());
        // 充返参数
        req2Boss.setContract_list(req.getContractList());

        req2Boss.setSerialno(req.getSerialno());


        try {
            if (req.getPoid() != null && req.getPoid() > 0L) {
                //查询有效时间内的流程对应的记录
                String sql = "select * from BIZ_AUTH_ORDER t where t.poid=? and  t.status='2' order by createtime desc ";
                List<BizAuthOrder> list = getDAO().find(sql, BizAuthOrder.class, req.getPoid());
                if (list != null && list.size() > 0) {
                    req2Boss.setCertOrderid(list.get(0).getOrderid());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //开户订购的可选包参数
        req2Boss.setPacks(req.getPacks());

        // 代办开户
        if ("BIZ_FGB_AGENTUSER_NEW".equals(req.getBizCode())) {
            req2Boss.setOptType("01");
        }

        return req2Boss;
    }

    private List<SyncPrdParam> getPrdParams(List<ApplyProduct> products, ChlInstallParam installParam, Long orderid)
            throws Exception {
        List<SyncPrdParam> prdParams = new ArrayList<SyncPrdParam>();
        for (ApplyProduct product : products) {
            SyncPrdParam param = new SyncPrdParam();
            param.setCount(product.getCount().toString());
            param.setIspostpone(product.getIspostpone());
            param.setLogicdevno(installParam.getLogicdevno());
            param.setStime(product.getStime());
            if (product.getPid() != null) {
                param.setPid(product.getPid().toString());
            }
            if (product.getSalespkgid() != null) {
                param.setSalespkgid(product.getSalespkgid().toString());
            }
            if (product.getSalesid() != null) {
                param.setSalesid(product.getSalesid().toString());
            }
            // param.setServid(servid);可以为空 置空

            param.setServid(product.getServid());

            constructSelprds(product.getKnowid(), orderid, param);
            if (StringUtils.isNotBlank(product.getUnit())) {
                param.setUnit(product.getUnit());
            }
            //ywp 加上最小使用期限
            if (StringUtils.isNotBlank(product.getMindate()) && !product.getMindate().equals("null")) {
                param.setMindate(product.getMindate());
            }
            //ywp mincount去掉
			/*if(StringUtils.isNotBlank(product.getMindate())&&!product.getMindate().equals("null")){
				param.setMincount(product.getMindate());
			}*/
            param.setHarddevnobos(product.getHarddevnobos());
            param.setRelsalesid(product.getRelsalesid());
            prdParams.add(param);
        }
        return prdParams;
    }

    private void constructSelprds(Long knowid, Long orderid, SyncPrdParam param) throws Exception {
        ApplyProductSelect applyProductSelect = new ApplyProductSelect();
        applyProductSelect.setOrderid(orderid);
        applyProductSelect.setKnowid(knowid);
        List<ApplyProductSelect> li = getDAO().find(applyProductSelect);
        if (li != null && !li.isEmpty()) {
            StringBuffer selprds = new StringBuffer();
            for (ApplyProductSelect productSelect : li) {
                selprds.append(productSelect.getPid());
                selprds.append(",");
            }
            param.setSelprds(selprds.toString());
            param.setSelectid(li.get(0).getSelectid().toString());
        }
    }

    /**
     * 开户确认提交接口
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo bizInstallCommit(BizInstallCommitReq req, BizInstallCommitResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
//		LoginInfo loginInfo = tmpService.getGridTestLoginInfo();
        CheckUtils.checkNull(loginInfo, CommonNotice.LOGIN_OUT_NOTICE);
        CheckUtils.checkNull(req, "请求对象不能为空");
        CheckUtils.checkEmpty(req.getBizorderid(), "业务订单号不能为空");
        CheckUtils.checkNull(req.getCustOrderid(), "订单编号不能为空");


        CustOrder custOrder = (CustOrder) getDAO().find(CustOrder.class, req.getCustOrderid());
        CheckUtils.checkNull(custOrder, "查询不到该订单记录");
        BizPortalOrder bizPortalOrder = (BizPortalOrder) getDAO().find(BizPortalOrder.class, req.getCustOrderid());
        CheckUtils.checkNull(bizPortalOrder, "查询不到该订单记录");

        String orderStatus = bizPortalOrder.getStatus();
        if (orderStatus.equals(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_LOGICDEL)
                || orderStatus.equals(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)
                || orderStatus.equals(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_OVERDUE)
                || orderStatus.equals(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_BOSSPAD)) {
            CheckUtils.checkNull(null, "该订单状态不支持支付,请确认");
        }
        String multipay = "N";
        if (StringUtils.isNotBlank(req.getMultipaywayflag()) && req.getMultipaywayflag().equalsIgnoreCase("Y")) {
            if (StringUtils.isNotBlank(req.getCashe())) {
                double payFees = Double.parseDouble(bizPortalOrder.getFees()) - Double.parseDouble(req.getCashe());
                if (payFees > 0.0) {
                    bizPortalOrder.setMultipay("Y");
                    multipay = "Y";
                }
                bizPortalOrder.setPayFees(Double.toString(payFees));
            }
        }

        //如果是工单支付，payway传N  --张耀其--2020-09-24
        if ("Y".equals(req.getPaylessflag())) {
            req.setPayway("N");
        }

        String resultInfo = null;
        try {
            resultInfo = quest2Boss(req.getBizorderid(), custOrder, bizPortalOrder, req.getBankaccno(), req.getPaycode(), req.getPayreqid(), req.getPayway(), multipay, req.getPaylessflag(), loginInfo, req.getGwPayOrderInfo());
        } catch (CustomException e) {
            bizPortalOrder.setWgpayway(req.getPayway());
            bizPortalOrder.setPayway(req.getPayway());
            bizPortalOrder.setPaycode(req.getPaycode());
            if (StringUtils.isNotEmpty(req.getPayreqid())) {
                bizPortalOrder.setPayreqid(req.getPayreqid());
            }
            bizPortalOrder.setPaytime(new Date());

            bizPortalOrder.setPaylessflag(req.getPaylessflag());
            if ("Y".equals(req.getPaylessflag())) {
                bizPortalOrder.setPayway("N");
            }
            bizPortalOrder.setStatus(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED);

            // 2022/12/02 确定提交：0[未提交],1[已提交]
            bizPortalOrder.setSubmitStatus("1");
            getDAO().update(bizPortalOrder);
            getDAO().update(custOrder);
            return returnInfo;
        }
        //getWgpaywayChangegetPayway
        bizPortalOrder.setWgpayway(req.getPayway());
        bizPortalOrder.setPayway(req.getPayway());
        bizPortalOrder.setPaycode(req.getPaycode());
        if (StringUtils.isNotEmpty(req.getPayreqid())) {
            bizPortalOrder.setPayreqid(req.getPayreqid());
        }
        bizPortalOrder.setPaytime(new Date());

        bizPortalOrder.setPaylessflag(req.getPaylessflag());
        if ("Y".equals(req.getPaylessflag())) {
            bizPortalOrder.setPayway("N");
        }

        handlerData(resultInfo, bizPortalOrder, custOrder);
        resp.setOrderid(custOrder.getId().toString());
        resp.setSerialno(custOrder.getBossserialno());
        ReturnVisitTaskService.addTask(req.getCustOrderid());

        if (StringUtils.isNotEmpty(custOrder.getPreacceptinfo())) {
            newDigitHomeBusOrderService.saveNewDigtOrderLog(custOrder.getPreacceptinfo(), loginInfo.getOperid(), "开户");
        }

        return returnInfo;
    }

    @Autowired
    private NewDigitHomeBusOrderService newDigitHomeBusOrderService;

    protected String quest2Boss(String bizOrderid, CustOrder custOrder, BizPortalOrder bizPortalOrder, String bankaccno, String paycode, String payreqid, String payway, String multipaywayflag, String paylessflag, LoginInfo loginInfo, JSONObject gwPayOrderInfo) throws Exception {
        BizInstallCommit2BossReq req2Boss = getBizInstallCommit2BossReq(custOrder, bizPortalOrder, bankaccno, paycode, payreqid, payway, multipaywayflag, paylessflag, gwPayOrderInfo, loginInfo);
//		ParamsManager.isCorrectData(payway,paycode);
        if ("33".equals(payway) || "U".equals(payway)) {//这里不检查null==payway&&null==paycode的情况了
            if (null == paycode || "000000".equals(paycode)) {//说明当前是错误数据,把U或者33(为统一支付平台的一级支付编码),而000000是现金支付  两者同时出现视为错误数据
                throw new BusinessException("当前数据已过期,请更新至最新版本!请移步设置界面更新或重新打开应用更新至最新!");
            }
        }
        String resultInfo = getBossHttpInfOutput(bizOrderid, BossInterfaceService.BIZ_INSTALL_COMMIT, req2Boss, loginInfo);
        return resultInfo;
    }

    protected void handlerData(String jsonStr, BizPortalOrder bizPortalOrder, CustOrder custOrder) throws Exception {
        BizInstallCommit2BossResp resp2Boss = (BizInstallCommit2BossResp) BeanUtil.jsonToObject(jsonStr, BizInstallCommit2BossResp.class);
        bizPortalOrder.setStatus(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED);

        // 2022/12/02 确定提交：0[未提交],1[已提交]
        bizPortalOrder.setSubmitStatus("1");

        custOrder.setBossserialno(resp2Boss.getSerialno());
        getDAO().update(bizPortalOrder);
        getDAO().update(custOrder);
    }

    private BizInstallCommit2BossReq getBizInstallCommit2BossReq(
            CustOrder custOrder, BizPortalOrder bizPortalOrder, String bankaccno
            , String paycode, String payreqid, String payway, String multipaywayflag, String paylessflag
            , JSONObject gwPayOrderInfo, LoginInfo loginInfo) {
        BizInstallCommit2BossReq req2Boss = new BizInstallCommit2BossReq();
        req2Boss.setBankaccno(bankaccno);
        req2Boss.setCustid(custOrder.getCustid().toString());
        req2Boss.setOrderid(bizPortalOrder.getResporderid().toString());
        req2Boss.setPaycode(paycode);
        req2Boss.setGwPayOrderInfo(gwPayOrderInfo);
        req2Boss.setPayreqid(payreqid);
        if (StringUtils.isEmpty(payway)) {
            payway = bizPortalOrder.getPayway();
        }
        req2Boss.setPayway(payway);
        if ("H".equals(payway)) {
            req2Boss.setSubpayway("042700");
        } else if ("G".equals(payway)) {
            req2Boss.setSubpayway("043700");
        } else if ("44".equals(payway)) {
            req2Boss.setSubpayway("");
            //代理商信息不为空，并且是现金，不是副卡开户(金额大于0)
            if (StringUtils.isNotEmpty(loginInfo.getAgentid())) {
                double payFees = Double.parseDouble(bizPortalOrder.getFees());
                if (payFees > 0.0) {
                    loginInfo.setAgentPayFlag("Y");
                }
            }
        }

        req2Boss.setMultipaywayflag(multipaywayflag);
        if (StringUtils.isNotBlank(custOrder.getBusinessdescipt())) {
            String[] strings = custOrder.getBusinessdescipt().split(",", -1);
            if (strings.length > 0 && StringUtils.isNotBlank(strings[0])) {
                req2Boss.setInstalltype(strings[0]);
            }
            if (strings.length > 1 && StringUtils.isNotBlank(strings[1])) {
                req2Boss.setHouseid(strings[1]);
            }
        }
        req2Boss.setFeebacktype(1);

        req2Boss.setPaylessflag(paylessflag);
        if ("Y".equals(paylessflag)) {//Y-工单环节结算打票,如果是Y，要选择上门安装
            req2Boss.setInstalltype("1");
        }

        return req2Boss;
    }

    /**
     * 查询号码池
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queCmccAcctno(QueCmccAcctnoReq req, QueCmccAcctnoResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        QueCmccAcctnoReq req2Boss = new QueCmccAcctnoReq();
        BeanUtils.copyPropertiesNotSuperClass(req2Boss, req);

        String responseStr = getBossHttpInfOutput(req.getBizorderid(), BossInterfaceService.QUE_CMMC_ACCTNO, req2Boss, loginInfo);
        QueAcctnoBossResp bossResp = (QueAcctnoBossResp) BeanUtil.jsonToObject(responseStr, QueAcctnoBossResp.class);
        resp.setDatas(bossResp.getResult());
        return returnInfo;
    }


    public ReturnInfo bizLockCmccAcctno(BizLockCmccAcctnoReq req) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkEmpty(req.getAcctno(), "宽带帐号信息不能为空");


        BizLockCmccAcctnoReq req2Boss = new BizLockCmccAcctnoReq();
        BeanUtils.copyPropertiesNotSuperClass(req2Boss, req);

        String response = getBossHttpInfOutput(req.getBizorderid(), BossInterfaceService.LOCK_CMCC_ACCTNO, req2Boss, loginInfo);
        return returnInfo;
    }

    public ReturnInfo bizUnLockCmccAcctno(BizLockCmccAcctnoReq req) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkEmpty(req.getAcctno(), "宽带帐号信息不能为空");


        BizLockCmccAcctnoReq req2Boss = new BizLockCmccAcctnoReq();
        BeanUtils.copyPropertiesNotSuperClass(req2Boss, req);

        String response = getBossHttpInfOutput(req.getBizorderid(), BossInterfaceService.UNLOCK_CMCC_ACCTNO, req2Boss, loginInfo);
        return returnInfo;
    }

    /**
     * 查询设备产品信息
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queDevPrdinfo(QueDevPrdinfoReq req, QueDevPrdinfoResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        QueDevPrdinfoReq req2Boss = new QueDevPrdinfoReq();
        BeanUtils.copyPropertiesNotSuperClass(req2Boss, req);

        //是否OTTr-2转换
        Rule rule = ruleService.getRule("*", ottRtoTwoSwitch);
        if (rule != null && rule.getValue() != null) {
            if (rule.getValue().equalsIgnoreCase("Y")) {
                if (req2Boss.getKind().equalsIgnoreCase("R")) {
                    req2Boss.setKind("2");
                }
            }
        }
        String outputStr = getBossHttpInfOutput(req.getBizorderid(), BossInterfaceService.QUE_DEVPRDINFO, req2Boss, loginInfo);
        /**
         * 判断设备是否在库，是否使用，
         * 只有在库未使用这个设备才能用
         */

        QueDevstoreReq devstoreReq = new QueDevstoreReq();

        BeanUtils.copyProperties(devstoreReq, req);

        //调用查询库存信息
        ResDevice resDevice = deviceAllocationService.queryResDevice(devstoreReq);

        if (resDevice != null) {
            if (!("1".equals(resDevice.getStatus()) && "0".equals(resDevice.getAppstatus()))) {
                throw new BusinessException("只有在库未使用这个设备才能用！");
            }
        }
        List<QueDevPrdinfoBossResp> datas = new Gson().fromJson(outputStr, new TypeToken<List<QueDevPrdinfoBossResp>>() {
        }.getType());
        resp.setDatas(datas);
        return returnInfo;
    }

    @Override
    protected List<PrvSysparam> getParams(String gcode, String mcodes) throws Exception {
        List<PrvSysparam> sysparamList = SysparamCache.getSysparamList(gcode);

        List<PrvSysparam> retList = new ArrayList<>();

        if (sysparamList != null && !sysparamList.isEmpty() && StringUtils.isNotBlank(mcodes)) {
            String[] split = StringUtils.split(mcodes, ",");
            for (int i = 0; i < split.length; i++) {
                String mcode = split[i];
                Iterator<PrvSysparam> iterator = sysparamList.iterator();
                while (iterator.hasNext()) {
                     PrvSysparam next = iterator.next();
                    if (StringUtils.equals(mcode, next.getMcode())) {
                        retList.add(next);
                        break;
                    }
                }
            }
        }

        return retList;
    }

    protected void setFeekindParam2Resp(String city, QueSyncPercombResp resp) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("		SELECT a.FEEKIND feekind,a.PERMARK permark ");
        sb.append("		FROM sys_feekind_param a");
        sb.append("		WHERE a.CITY = ? ");
        sb.append("		ORDER BY a.SORT ");
        List<PercombFeekindInfo> datas = getDAO().find(sb.toString(), PercombFeekindInfo.class, city);
        List<PercombFeekindParam> feekindParamList = new ArrayList<>();
        resp.setFeekindParamList(feekindParamList);
        if (datas != null && !datas.isEmpty()) {
            for (PercombFeekindInfo info : datas) {
                PercombFeekindParam feekindParam = new PercombFeekindParam();
                List<PrvSysparam> feekindList = getParams(feekindGcode, info.getFeekind());
                feekindParam.setCity(info.getCity());
                feekindParam.setPermark(info.getPermark());
                feekindParam.setFeekind(feekindList);
                feekindParamList.add(feekindParam);
            }
        }
    }

    /**
     * 	集团预开激活新接口
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo bizGroupActivation(BizGroupActiveReq req, BizGroupActiveResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req.getCustOrderid(), "订单号不能为空");
        CheckUtils.checkEmpty(req.getPayway(), "支付方式不能为空");

        CustOrder custOrder = (CustOrder) getDAO().find(CustOrder.class, req.getCustOrderid());
        CheckUtils.checkNull(custOrder, String.format("【%s】查询不到订单", req.getCustOrderid()));

        BizPortalOrder order = (BizPortalOrder) getDAO().find(BizPortalOrder.class, req.getCustOrderid());
        CheckUtils.checkNull(order, String.format("【%s】查询不到订单", req.getCustOrderid()));

        String orderStatus = order.getStatus();
        if (orderStatus.equals(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_LOGICDEL)
                || orderStatus.equals(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)
                || orderStatus.equals(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_OVERDUE)
                || orderStatus.equals(PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_BOSSPAD)) {
            CheckUtils.checkNull(null, String.format("【%s】该订单状态不支持支付,请确认。", req.getCustOrderid()));
        }

        // 如果已支付，则判断是否已提交：1【已提交】、0或null【未提交】
        String submitStatus = order.getSubmitStatus();
        if ("1".equals(submitStatus)) {
            throw new BusinessException(String.format("CMMS:【%s】订单状态为已完成，请勿重复提交！", req.getCustOrderid()));
        }

        BizGroupActiveBossReq bossReq = getBizGroupActiveBossReq(req, custOrder);
        String response = getBossHttpInfOutput(req.getBizorderid(), BizConstant.M5gInterface.BIZ_VPMN_BATCH_ACTIVATION , bossReq, loginInfo);

        order.setPayreqid(req.getPayreqid());
        order.setWgpayway(req.getPayway());
        order.setPayway(req.getPayway());
        order.setPaycode(req.getPaycode());
        order.setPaytime(new Date());
        order.setSubmitStatus("1");
        order.setStatus(BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED);
        getDAO().update(order);

        custOrder.setDescrip(bossReq.getCertId());
        custOrder.setOrderstatus(BizConstant.BizCustorderOrderstatus.SYNC);
        getDAO().update(custOrder);

        resp.setSerialno(custOrder.getBossserialno());
        resp.setOrderid(String.valueOf(custOrder.getId()));

        return  returnInfo;
    }

    private BizGroupActiveBossReq getBizGroupActiveBossReq(BizGroupActiveReq req, CustOrder custOrder) throws Exception {
        BizGroupActivate activate = new BizGroupActivate();
        activate.setOrderid(custOrder.getId());
        List<BizGroupActivate> activateList = getDAO().find(activate);
        if (CollectionUtil.isEmpty(activateList)) {
            throw new BusinessException(String.format("CMMS:【%s】查询不到激活记录", req.getCustOrderid()));
        }

        BizGroupActivate bizGroupActivate = activateList.get(0);
        if (StringUtils.isBlank(bizGroupActivate.getContent())) {
            throw new BusinessException(String.format("CMMS:【%s】查询不到激活记录", req.getCustOrderid()));
        }

        BizGroupActiveBossReq bossReq = JSONObject.parseObject(bizGroupActivate.getContent(), BizGroupActiveBossReq.class);

        if (StringUtils.isBlank(req.getCertId())) {
            Long poid = custOrder.getPoid();
            String sql = " select * from biz_process_order_detail bpod where cpcode ='GIDCONF' and poid = ? ";
            List<BizProcessOrderDetailBO> bizProcessOrderDetails = getDAO().find(sql, BizProcessOrderDetailBO.class, poid);
            if (CollectionUtil.isEmpty(bizProcessOrderDetails)) {
                throw new BusinessException(String.format("CMMS:【%s】查询不到订单实名认证记录", req.getCustOrderid()));
            }

            BizProcessOrderDetailBO bizProcessOrderDetailBO = bizProcessOrderDetails.get(0);
            if (StringUtils.isBlank(bizProcessOrderDetailBO.getData())
                    || bizProcessOrderDetailBO.getStatus() == null
                    || bizProcessOrderDetailBO.getStatus() != BizProcessOrderDetailBO.POD_STATUS_DONE) {
                throw new BusinessException(String.format("CMMS:【%s】查询不到订单实名认证记录", req.getCustOrderid()));
            }

            JSONObject object = JSON.parseObject(bizProcessOrderDetailBO.getData());
            String personId = (String) object.get("personId");

            if (StringUtils.isBlank(personId)) {
                throw new BusinessException(String.format("CMMS:【%s】查询不到订单实名认证记录", req.getCustOrderid()));
            }

            bossReq.setCertId(personId);
        }

        bossReq.setPayway(req.getPayway());
        bossReq.setSubpayway(req.getPaycode());
        bossReq.setGwPayOrderInfo(req.getGwPayOrderInfo());
        if (req.getGwPayOrderInfo() != null) {
            JSONObject gwPayOrderInfo = req.getGwPayOrderInfo();
            String tradeNo = (String) gwPayOrderInfo.get("trade_no");
            bossReq.setTradeno(tradeNo);
        }

        return bossReq;
    }

}
