package com.maywide.biz.inter.pojo.unifiedaccount.unifiedacountpaylog;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName UnfiedAccountPayLog
 * @description: TODO
 * @date 2023年11月16日
 * @version: 1.0
 */
@Data
public class UnfiedAccountPayLogVO {
    /**
     * 业务流水号
     */
    private String businessId;

    /**
     * 操作时间
     */
    private String tradeDate;

    /**
     * 转移金额
     */
    private String tradeFee;

    /**
     * 扣费类型
     */
    private String transferType;

    /**
     * 扣费类型值
     */
    private String transferTypeValue;

    /**
     * 接入方流水号
     *
     */
    private String peerBusinessId;
}
