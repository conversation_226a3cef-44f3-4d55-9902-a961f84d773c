package com.maywide.biz.pay.unify.entity;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "pay_record_log")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class PayRecordLog implements Serializable {

	/*初始化*/
	public static final String PAY_RESULT_INIT = "0";
	/*支付完成*/
	public static final String PAY_RESULT_PAYED = "1";
	/*支付失败*/
	public static final String PAY_RESULT_PAYFAIL = "2";

	/*退款成功*/
	public static final String PAY_RESULT_REFUND = "3";
	/*退款失败*/
	public static final String PAY_RESULT_REFUNDFAIL = "4";

	/*支付*/
	public static final String ACTION_TYPE_PAY = "01";
	/*退款*/
	public static final String ACTION_TYPE_REFUND = "02";

	private Long recid;

	private Long orderid;
	
	private Date paydate;
	
	private String fees;
	
	private String payresult;
	
	private String redicetStr;
	
	private String bizexmsg;
	
	private String paybackexmsg;

	private String actionType;

	private Long custorderid;
	

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="recid")
	public Long getRecid() {
		return recid;
	}

	public void setRecid(Long recid) {
		this.recid = recid;
	}

	@Column(name="orderid",nullable=false)
	public Long getOrderid() {
		return orderid;
	}

	public void setOrderid(Long orderid) {
		this.orderid = orderid;
	}

	public Date getPaydate() {
		return paydate;
	}

	public void setPaydate(Date paydate) {
		this.paydate = paydate;
	}

	public String getFees() {
		return fees;
	}

	public void setFees(String fees) {
		this.fees = fees;
	}

	public String getPayresult() {
		return payresult;
	}

	public void setPayresult(String payresult) {
		this.payresult = payresult;
	}

	public String getRedicetStr() {
		return redicetStr;
	}

	public void setRedicetStr(String redicetStr) {
		this.redicetStr = redicetStr;
	}

	public String getBizexmsg() {
		return bizexmsg;
	}

	public void setBizexmsg(String bizexmsg) {
		this.bizexmsg = bizexmsg;
	}

	public String getPaybackexmsg() {
		return paybackexmsg;
	}

	public void setPaybackexmsg(String paybackexmsg) {
		this.paybackexmsg = paybackexmsg;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public Long getCustorderid() {
		return custorderid;
	}

	public void setCustorderid(Long custorderid) {
		this.custorderid = custorderid;
	}
}
