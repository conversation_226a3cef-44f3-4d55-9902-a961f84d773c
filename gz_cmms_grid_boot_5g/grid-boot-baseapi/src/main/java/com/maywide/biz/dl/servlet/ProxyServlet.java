package com.maywide.biz.dl.servlet;

import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.inter.pojo.sendrandomcode.SendRandomCodeInterReq;
import com.maywide.biz.inter.pojo.sendrandomcode.SendRandomCodeInterResp;
import com.maywide.biz.inter.service.PubService;
import com.maywide.core.context.SpringContextHolder;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.security.remote.BossHttpClientImpl;
import org.apache.commons.httpclient.methods.GetMethod;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

public class ProxyServlet extends HttpServlet {
	
/*	@Audited
	private ParamService paramService;

	private MeritPayService meritPayService;*/

	private PubService pubService;

	@Override
	public void init() throws ServletException {
		super.init();
//		meritPayService = SpringContextHolder.getBean(MeritPayService.class);

		pubService = SpringContextHolder.getBean(PubService.class);
	}

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		SendRandomCodeInterReq sendRandomCodeInterReq = new SendRandomCodeInterReq();
		sendRandomCodeInterReq.setBizorderid(pubService.getBizorderid());
		sendRandomCodeInterReq.setAreaid("681");
		sendRandomCodeInterReq.setCity("FS");
		sendRandomCodeInterReq.setCustid("104129");
		sendRandomCodeInterReq.setPhoneno("13726806194");
		sendRandomCodeInterReq.setUserip("***********");
		sendRandomCodeInterReq.setSmscontent("短信内容");
		SendRandomCodeInterResp sendRandomCodeInterResp = new SendRandomCodeInterResp();
		AuthContextHolder.setLoginInfo(testLoginInfo());
		try {
			ReturnInfo returnInfo = pubService.sendRandomCode(sendRandomCodeInterReq,sendRandomCodeInterResp);
			System.out.println("");
		} catch (Exception e) {
			e.printStackTrace();
		}
//		String bizOrderid = req.getParameter("bizOrderid");

			/*try {
			SendMicroMsgReq sendMicroMsgReq = new SendMicroMsgReq();
			sendMicroMsgReq.setCustid(Long.parseLong(custid));
			sendMicroMsgReq.setCustName(custName);
			sendMicroMsgReq.setBizorderid(bizOrderid);
			sendMicroMsgReq.setHouseid(Long.parseLong(houseId));
			AuthContextHolder.setLoginInfo(testLoginInfo());
			ReturnInfo returnInfo = messageService.sendMicroMsg(sendMicroMsgReq);
			System.out.println(returnInfo.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
		}*/
		/*JsonObject jsonObject = new JsonObject();
		jsonObject.addProperty("msgcont","测试中文是否会存在乱码");
		jsonObject.addProperty("custid","760003113079");
		jsonObject.addProperty("publishnum","gh_896573c65f7f");
		MQUtils.sendByExchange("topic-test","msgpush.micro","topic",jsonObject.toString());*/
		/*mqProducer.sendDataToQueue("msgpush.micro",jsonObject);*/

		/*try{
			String markNo = req.getParameter("markNo");
			String url = "http://10.129.10.150:18080/bdinterface/custportray/getCustPortray";

			HttpClient httpClient = new HttpClient();
			Map<String, String> params = new HashMap<String, String>();
			params.put("markNo", markNo);
			GetMethod getMethod = getPostMethod(url, params);
			httpClient.getParams().setContentCharset("UTF-8");
			int code = httpClient.executeMethod(getMethod);
			String responseStr = getMethod.getResponseBodyAsString().trim();
			responseStr = responseStr.replace("../", "http://222.161.198.187:18080/bdinterface/");
			resp.getWriter().write(responseStr);
		}catch(Exception e){
			resp.getWriter().write(404);
		}*/
	}
	
	private GetMethod getPostMethod(String url,Map<String, String> params){
		if (url.indexOf("?") == -1) {
			url += "?";
		} else {
			url += "&";
		}
		url += BossHttpClientImpl.encodeParameters(params, "UTF-8");
		GetMethod getMethod = new GetMethod(url);
		return getMethod;
	}

	private LoginInfo testLoginInfo(){
		LoginInfo loginInfo = new LoginInfo();
		loginInfo.setCity("ZS");
		loginInfo.setOperid(68503L);
		loginInfo.setDeptid(101055L);
		return loginInfo;
	}

	
}
