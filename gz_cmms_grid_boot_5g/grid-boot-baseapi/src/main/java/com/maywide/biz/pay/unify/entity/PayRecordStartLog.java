package com.maywide.biz.pay.unify.entity;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "pay_record_start_log")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class PayRecordStartLog implements Serializable {

	private Long recid;

	private Date paydate;
	
	private String actionType;

	private Long custorderid;
	

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="recid")
	public Long getRecid() {
		return recid;
	}

	public void setRecid(Long recid) {
		this.recid = recid;
	}


	public Date getPaydate() {
		return paydate;
	}

	public void setPaydate(Date paydate) {
		this.paydate = paydate;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public Long getCustorderid() {
		return custorderid;
	}

	public void setCustorderid(Long custorderid) {
		this.custorderid = custorderid;
	}
}
