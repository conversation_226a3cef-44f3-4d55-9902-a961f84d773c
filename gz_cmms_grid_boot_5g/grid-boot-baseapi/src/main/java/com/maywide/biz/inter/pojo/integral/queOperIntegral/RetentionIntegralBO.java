package com.maywide.biz.inter.pojo.integral.queOperIntegral;

public class RetentionIntegralBO {
    private double retentionIntergal;
    private int retentionUserCount;


    public double getRetentionIntergal() {
        return retentionIntergal;
    }

    public void setRetentionIntergal(double retentionIntergal) {
        this.retentionIntergal = retentionIntergal;
    }

    public int getRetentionUserCount() {
        return retentionUserCount;
    }

    public void setRetentionUserCount(int retentionUserCount) {
        this.retentionUserCount = retentionUserCount;
    }
}
