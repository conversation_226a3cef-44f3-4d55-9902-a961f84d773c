package com.maywide.biz.pay.gzboss.pojo;

import com.maywide.biz.core.pojo.api.BaseApiRequest;

public class QueQdUnifyPayInfoReq extends BaseApiRequest {

	private static final long serialVersionUID = 1L;

	private String custorderid;// 客户订单id

	private String payway;//支付方式

	private String subpayway;

	private String appType; //苹果应用内H5：iOS  ，Android应用内H5：Android ， 手机浏览器：Wap


	public String getCustorderid() {
		return custorderid;
	}

	public void setCustorderid(String custorderid) {
		this.custorderid = custorderid;
	}

	public String getPayway() {
		return payway;
	}

	public void setPayway(String payway) {
		this.payway = payway;
	}

	public String getSubpayway() {
		return subpayway;
	}

	public void setSubpayway(String subpayway) {
		this.subpayway = subpayway;
	}

	public String getAppType() {
		return appType;
	}

	public void setAppType(String appType) {
		this.appType = appType;
	}
}
