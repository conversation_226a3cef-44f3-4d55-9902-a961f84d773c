package com.maywide.biz.pay.gzboss.pojo;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class PayInfo implements Serializable {

    /*微信支付*/
    public static final String PAY_TYPE_WECHAT = "01";
    /*支付宝支付*/
    public static final String PAY_TYPE_ALIPAY = "02";

    /**
     * 商户订单号，商户系统唯一标识，建议 3 位随机数+时 间戳，不重复随机。
     */
    private String mchntOrderNo;
    /**
     * 应付金额，单位分
     */
    private String amount;
    /**
     * 微信/支付宝支付条码，注意：不是二维码
     */
    private String authCode;
    private String payType;
    /**
     * 退款的时候使用。订单号，支付返回的订单号orderNo
     */
    private String orderNo;

    public String getMchntOrderNo() {
        return mchntOrderNo;
    }

    public void setMchntOrderNo(String mchntOrderNo) {
        this.mchntOrderNo = mchntOrderNo;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
