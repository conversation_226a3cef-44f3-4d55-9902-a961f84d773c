package com.maywide.biz.pay.gzboss.pojo;

import java.io.Serializable;

public class GwOrderItemBo implements Serializable{
	private String peer_item_id;	//费用项id, 由调用方自定义，仅支持字母、数字、下划线且需保证在不重复
	private String item_fee;	//支付金额, （单位：分）
	private String item_name;	// 费用名称
	private String item_code;	//费用编码, 比如电话卡的商品编码
	private String item_type;	//费用类型, 比如电话卡的商品类型


	/******响应报文内容 开始******/
	private String item_num;	//费用明细序号
	private String detail_id;	//交易明细ID, 支付中心生成
	private String item_id;	//费用明细ID, 支付中心生成
	/******响应报文内容 结束******/



	public String getPeer_item_id(){
		return peer_item_id;
	}
	public String getItem_num(){
		return item_num;
	}
	public String getDetail_id(){
		return detail_id;
	}
	public String getItem_id(){
		return item_id;
	}
	public void setItem_num(String item_num){
		this.item_num = item_num;
	}
	public void setDetail_id(String detail_id){
		this.detail_id = detail_id;
	}
	public void setItem_id(String item_id){
		this.item_id = item_id;
	}
	public String getItem_fee(){
		return item_fee;
	}
	public String getItem_name(){
		return item_name;
	}
	public String getItem_code(){
		return item_code;
	}
	public String getItem_type(){
		return item_type;
	}
	public void setPeer_item_id(String peer_item_id){
		this.peer_item_id = peer_item_id;
	}
	public void setItem_fee(String item_fee){
		this.item_fee = item_fee;
	}
	public void setItem_name(String item_name){
		this.item_name = item_name;
	}
	public void setItem_code(String item_code){
		this.item_code = item_code;
	}
	public void setItem_type(String item_type){
		this.item_type = item_type;
	}

}
