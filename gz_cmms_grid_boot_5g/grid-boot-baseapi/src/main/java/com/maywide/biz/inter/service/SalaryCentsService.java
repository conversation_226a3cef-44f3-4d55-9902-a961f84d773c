package com.maywide.biz.inter.service;

import com.maywide.biz.ass.topatch.entity.BizGridInfo;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.entity.Rule;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.TokenReturnInfo;
import com.maywide.biz.core.service.ParamService;
import com.maywide.biz.core.service.RuleService;
import com.maywide.biz.core.servlet.SpringBeanUtil;
import com.maywide.biz.inter.pojo.integralRealShare.queryShareRealIntegral.QueryShareRealIntegralBo;
import com.maywide.biz.inter.pojo.integralRealShare.queryShareRealIntegral.QueryShareRealIntegralReq;
import com.maywide.biz.inter.pojo.salaryCents.*;
import com.maywide.biz.inter.pojo.salaryCents.CancelShareReq;
import com.maywide.biz.inter.pojo.salaryCents.ShareRep;
import com.maywide.biz.salary.SalaryConstants;
import com.maywide.biz.salary.pojo.BeforehandRealDetailBO;
import com.maywide.biz.salary.repbo.*;
import com.maywide.biz.salary.reqbo.*;
import com.maywide.biz.salary.service.BaseWageService;
import com.maywide.biz.salary.service.BeforehandRealService;
import com.maywide.biz.salary.service.ExplicationConfigService;
import com.maywide.biz.salary.service.OthersKpiService;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.dao.support.Page;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.security.remote.dcHttpCall.DcApiResp;
import com.maywide.core.security.remote.dcHttpCall.DcHttpPost;
import com.maywide.core.security.remote.dcHttpCall.DcReqBean;
import com.maywide.core.service.PersistentService;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Service
@Transactional(rollbackFor = Exception.class)
public class SalaryCentsService {
    private static Logger log = LoggerFactory.getLogger(SalaryCentsService.class);
    @Autowired
    private BeforehandRealService beforehandRealService;
    @Autowired
    private ExplicationConfigService explicationConfigService;
    @Autowired
    private OthersKpiService othersKpiService;
    @Autowired
    private BaseWageService baseWageService;
    @Autowired
    private PersistentService persistentService;
    @Autowired
    private ParamService paramService;
    @Autowired
    private RuleService ruleService;

    /**
     * 查询实积分
     * @param req
     * @return
     * @throws Exception
     */
    public RealRep queryReal(QueryRealReq req) throws Exception{
        RealRep result = new RealRep();
        req.setCycleid(req.getCycleid().replace("-",""));
        CentSumReq sumReq = new CentSumReq();
        sumReq.setCycleid(req.getCycleid());
        sumReq.setOperator(req.getOperator());
        BizGridInfo grid = getGrids();
        sumReq.setWhgridcode(grid.getGridcode());
        sumReq.setStatus("1"); //审核通过
        sumReq.setPagesize(req.getPagesize());
        sumReq.setCurrentPage(req.getCurrentPage());
        CentSumRep rep = beforehandRealService.queryCentSum(sumReq);
        result.setCode(Long.valueOf(rep.getRtcode()));
        result.setMessage(rep.getMessage());
        result.setCurrentPage(Integer.valueOf(req.getCurrentPage()));
        result.setPagesize(Integer.valueOf(req.getPagesize()));
        Double realNum = 0D;
        if(rep!=null && rep.getCentlist()!=null && rep.getCentlist().size()>0){
            String real = rep.getCentlist().get(0).getSrccents();
            if(StringUtils.isNotBlank(real)){
                realNum = realNum + Double.valueOf(real);
            }
            //合并调整积分
            String adjust = rep.getCentlist().get(0).getAdjustcents();
            if(StringUtils.isNotBlank(adjust) &&
                "Y".equals(req.getExistsMerge())){
                realNum = realNum + Double.valueOf(adjust);
            }
        }
        result.setTotalcents(realNum);
        result.setTotalnums(rep.getTotalnums());
        result.setData(rep.getCentlist());
        return result;
    }

    /**
     * 查询预积分
     * @param req
     * @return
     * @throws Exception
    RealRep */
    public BeforehandRep queryBeforehand(QueryBeforehandReq req) throws Exception{
        BeforehandRep result = new BeforehandRep();
        req.setCycleid(req.getCycleid().replace("-",""));
        QueryBeforehandRep rep = beforehandRealService.queryBeforehand(req);
        //过滤积分为0数据
        if(rep!=null && rep.getCentlist()!=null && rep.getCentlist().size()>0) {
            Iterator<QueryBeforehandRep.Detail> it = rep.getCentlist().iterator();
            while (it.hasNext()) {
                QueryBeforehandRep.Detail detail = it.next();
                if (StringUtils.isEmpty(detail.getCent()) || "0".equals(detail.getCent())) {
                    it.remove();
                }
            }
        }
        result.setCode(Long.valueOf(rep.getRtcode()));
        result.setMessage(rep.getMessage());
        result.setCurrentPage(Integer.valueOf(req.getCurrentPage()));
        result.setPagesize(Integer.valueOf(req.getPagesize()));
        result.setTotalcents(rep.getTotalcents());
        result.setTotalnums(rep.getTotalnums());
        result.setData(rep.getCentlist());
        return result;
    }

    /**
     * 查询分享积分
     * @param req
     * @return
     * @throws Exception
     */
    public ShareRep queryShare(QueryShareReq req) throws Exception{
        ShareRep result =new ShareRep();
        //分享状态无效数据
        List<QueryShareRep.Detail> removes = new ArrayList<QueryShareRep.Detail>();
        req.setCurrentPage("1");
        req.setPagesize(Integer.MAX_VALUE+"");
        req.setCycleid(req.getCycleid().replace("-",""));

        QueryShareRep shareRep = new QueryShareRep();
        shareRep.setSharelist(new ArrayList<QueryShareRep.Detail>());
        shareRep.setTotalcents(0D);
        shareRep.setTotalnums(0);

        //----------------查询我分享给别人数据--------------------
        if(StringUtils.isNotEmpty(req.getSendoper())){
            QueryShareReq sendoperReq = new QueryShareReq();
            BeanUtils.copyProperties(req,sendoperReq);
            sendoperReq.setAccoper(null);
            QueryShareRep rep = beforehandRealService.queryShare(sendoperReq);
            //合并分享积分 我分享给别人

            if("0".equals(rep.getRtcode()) && rep.getSharelist()!=null) {
                //我分享给别人的转为负数
                for (QueryShareRep.Detail detail : rep.getSharelist()) {
                    if(Double.valueOf(detail.getCent())!=0) {
                        Double cent = -Double.valueOf(detail.getCent());
                        detail.setCent(cent.toString());
                    }
                }
                shareRep.getSharelist().addAll(rep.getSharelist());
                //汇总总积分字段  我分享别人的转为负数
                Double repTotalcents = rep.getTotalcents()==0?0:-rep.getTotalcents();
                shareRep.setTotalcents(shareRep.getTotalcents()+repTotalcents);
                shareRep.setTotalnums(shareRep.getTotalnums()+rep.getTotalnums());
            }
        }

        //-----------------查询别人分享给我数据----------------------
        if(StringUtils.isNotEmpty(req.getAccoper())){
            //查询别人分享给我数据
            QueryShareReq accoperReq = new QueryShareReq();
            BeanUtils.copyProperties(req,accoperReq);
            accoperReq.setSendoper(null);
            QueryShareRep rep2 = beforehandRealService.queryShare(accoperReq);
            //合并分享积分  别人分享给我
            if("0".equals(rep2.getRtcode()) && rep2.getSharelist()!=null) {
                shareRep.getSharelist().addAll(rep2.getSharelist());
                shareRep.setTotalcents(shareRep.getTotalcents()+rep2.getTotalcents());
                shareRep.setTotalnums(shareRep.getTotalnums()+rep2.getTotalnums());
            }
        }
        //----------------排除掉状态无效数据--------------------------
        for (QueryShareRep.Detail detail : shareRep.getSharelist()) {
            //排除状态为无效数据
            if("1".equals(detail.getStatus())){
                shareRep.setTotalcents(shareRep.getTotalcents());
                shareRep.setTotalnums(shareRep.getTotalnums());
                removes.add(detail);
            }
        }
        if(removes.size()>0) {
            shareRep.getSharelist().removeAll(removes);
        }


        result.setCode(0L);
        result.setMessage(shareRep.getMessage());
        result.setTotalcents(shareRep.getTotalcents());
        result.setTotalnums(shareRep.getTotalnums());
        result.setData(shareRep.getSharelist());
        return result;
    }

    /**
     * 查询剔除积分
     * @param req
     * @return
     * @throws Exception
     */
    public TokenReturnInfo queryAdjustment(QueryAdjustmentReq req) throws Exception{
        TokenReturnInfo result =new TokenReturnInfo();
        req.setCycleid(req.getCycleid().replace("-",""));
        double totalcents = 0d;
        //查询调整积分
        req.setOptype("0");
        QueryAdjustmentRep rep1 = beforehandRealService.queryAdjustment(req);
        if(StringUtils.isNotEmpty(rep1.getTotalcents())){
            totalcents = totalcents + Double.valueOf(rep1.getTotalcents());
        }
        //查询新增积分
        req.setOptype("1");
        QueryAdjustmentRep rep2 = beforehandRealService.queryAdjustment(req);
        if(StringUtils.isNotEmpty(rep2.getTotalcents())){
            totalcents = totalcents + Double.valueOf(rep2.getTotalcents());
        }
        result.setCode(0L);
        result.setMessage("");
        result.setData(totalcents);
        return result;
    }

    /**
     * 查询积分排名
     * @param req
     * @return
     * @throws Exception
     */
    public TokenReturnInfo rankCent(RankCentReq req) throws Exception {
        TokenReturnInfo result = new TokenReturnInfo();
        req.setScycleid(req.getScycleid().replace("-",""));
        req.setEcycleid(req.getEcycleid().replace("-",""));
        req.setSorttype("0");//分公司排名
        RankCentRep rep = beforehandRealService.rankCent(req);
        req.setSorttype("1");//业务区排名
        RankCentRep rep2 = beforehandRealService.rankCent(req);
        List<RankCentRep.Detail> list = rep.getSortlist();
        List<RankCentRep.Detail> list2 = rep2.getSortlist();
        if(list!=null && list2!=null && list.size()>0 && list2.size()>0){
            result.setCode(Long.valueOf(rep.getRtcode()));
            result.setMessage("");
            JSONObject obj = new JSONObject();
            obj.put("cityorder",list.get(0).getOrder());
            obj.put("areaorder",list2.get(0).getOrder());
            result.setData(obj);
        }else{
            result.setCode(Long.valueOf(rep.getRtcode()));
            result.setMessage(rep.getMessage());
        }
        return result;
    }

    /**
     * 搜索用户
     * @param req
     * @return
     * @throws Exception
     */
    public TokenReturnInfo searchOperator(SalaryReq req) throws Exception {
        TokenReturnInfo result = new TokenReturnInfo();
        JSONArray ja = beforehandRealService.searchOperator(req.getSearch());
        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(ja);
        return result;
    }

    /**
     * 分享积分
     * @param req
     * @return
     * @throws Exception
     */
    public TokenReturnInfo beforehandShare(ShareReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        String[] accopers = req.getAccoper().split(",");
        String[] accdepts =  req.getAccdept().split(",");
        String[] cents =  req.getCent().split(",");
        result.setCode(0L);
        result.setMessage("分享成功!");
        for (int i=0;i<accopers.length ;i++) {
            ShareReq sr = new ShareReq();
            BeanUtils.copyProperties(req,sr);
            sr.setAccoper(accopers[i]);
            sr.setAccdept(accdepts[i]);
            sr.setCent(cents[i]);
            com.maywide.biz.salary.repbo.ShareRep rep = beforehandRealService.beforehandShare(sr);
            if(!"0".equals(rep.getRtcode())){
                result.setCode(Long.valueOf(rep.getRtcode()));
                result.setMessage(rep.getMessage());
                break;
            }
        }
        result.setData("");
        return result;
    }
    /**
     * 取消分享积分
     * @param req
     * @return
     * @throws Exception
     */
    public TokenReturnInfo cancelShare(CancelShareReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        req.setCycleid(req.getCycleid().replace("-",""));
        CancelShareRep rep = beforehandRealService.cancelShare(req.getBeforehandSrcid(),
                req.getCycleid(),req.getOperid(),req.getDisreason());
        if(rep == null){
            result.setCode(-1L);
            result.setMessage("找不到分享积分，无法取消");
            return result;
        }
        result.setCode(Long.valueOf(rep.getRtcode()));
        result.setMessage(rep.getMessage());
        result.setData("");
        return result;
    }

    /**
     * 查询页面说明项
     * @return
     * @throws Exception
    */
    public TokenReturnInfo querySalaryConfig(SalaryReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        BizGridInfo grid = getGrids();
        JSONArray ja = explicationConfigService.queryByAreaid(req.getCity(),req.getAreaid(),req.getDateMonth(),grid.getId()+"");
        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(ja);
        return result;
    }

    /**
     * 首页-实积分分组查询
     * @return
     * @throws Exception
     */
    public TokenReturnInfo othersKpiGroup(SalaryReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        req.setDateMonth(req.getDateMonth().replace("-",""));
//        BizGridInfo grid =getGrids();

        JSONArray ja = new JSONArray();
        if(StringUtils.isEmpty(req.getOperid()) || StringUtils.isEmpty(req.getDateMonth())){
            throw new Exception("网格人员id和月份不能为空!");
        }
        //--------------销售积分---------------


        JSONObject zsbusiJson = new JSONObject();
        CentSumReq csreq = new CentSumReq();
        csreq.setCycleid(req.getDateMonth());
        csreq.setOperator(req.getOperid());
        csreq.setGroupcode(SalaryConstants.OthersKpi.ZSBUSI);
//        csreq.setWhgridcode(grid.getGridcode());
        csreq.setStatus("1"); //审核通过
        csreq.setPagesize(Integer.MAX_VALUE+"");
        csreq.setCurrentPage("1");
        CentSumRep zsbusiRep = beforehandRealService.queryCentSum(csreq);
        Double zsbusiSrccents = 0D;
        Double zsbusiSharecents = 0D;

        double integralShared = 0d;
        double integralBeShared = 0d;

        if(zsbusiRep!=null && zsbusiRep.getCentlist()!=null && zsbusiRep.getCentlist().size()>0){
            for (CentSumRep.Detail detail : zsbusiRep.getCentlist()) {
                String srccents = StringUtils.isNotEmpty(detail.getSrccents())?
                        detail.getSrccents():"0";
                zsbusiSrccents = zsbusiSrccents + Double.valueOf(srccents);
                //是否有剔除积分，如果有则合并
                String adjust = detail.getAdjustcents();
                if(StringUtils.isNotBlank(adjust)){
                    zsbusiSrccents = zsbusiSrccents + Double.valueOf(adjust);
                }
                String share = detail.getSharecents();
                if(StringUtils.isNotEmpty(share)){
                    zsbusiSharecents = zsbusiSharecents + Double.valueOf(share);
                    if (Double.valueOf(share)<0){
                        integralShared = -integralShared+ Double.valueOf(share);
                    }else{
                        integralBeShared = integralBeShared+ Double.valueOf(share);
                    }
                }
            }
        }
        double realIntegralShared = queryRealIntegralSharedToOper(req.getOperid(),req.getDateMonth(),"2");
        double realIntegralBeShared = queryRealIntegralBeShareToOper(req.getOperid(),req.getDateMonth(),"2");

        zsbusiJson.put("srccents",zsbusiSrccents); //个人实积分
        zsbusiJson.put("sharecents",zsbusiSharecents-realIntegralShared+realIntegralBeShared);  //分享积分   加上实积分分享和被分享的
        zsbusiJson.put("groupcode",SalaryConstants.OthersKpi.ZSBUSI);
        zsbusiJson.put("groupname","销售积分");
        zsbusiJson.put("integralShared",integralShared+realIntegralShared);
        zsbusiJson.put("integralBeShared",integralBeShared+realIntegralBeShared);
        ja.put(zsbusiJson);

        //---------------安装积分---------------
        JSONObject zssetupJson = new JSONObject();
        //查询个人实积分
        csreq.setGroupcode(SalaryConstants.OthersKpi.ZSSETUP);//安装积分
        CentSumRep zssetupRep = beforehandRealService.queryCentSum(csreq);
        Double zssetupSrccents = 0D;
        Double zssetupSharecents = 0D;

        integralShared = 0d;
        integralBeShared = 0d;
        if(zssetupRep!=null && zssetupRep.getCentlist()!=null && zssetupRep.getCentlist().size()>0){
            for (CentSumRep.Detail detail : zssetupRep.getCentlist()) {
                String srccents = StringUtils.isNotEmpty(detail.getSrccents())?
                        detail.getSrccents():"0";
                zssetupSrccents = zssetupSrccents + Double.valueOf(srccents);
                //是否有剔除积分，如果有则合并
                String adjust = detail.getAdjustcents();
                if(StringUtils.isNotBlank(adjust)){
                    zssetupSrccents = zssetupSrccents + Double.valueOf(adjust);
                }
                String share = detail.getSharecents();
                if(StringUtils.isNotBlank(share)){
                    zssetupSharecents = zssetupSharecents + Double.valueOf(share);
                    if (Double.valueOf(share)<0){
                        integralShared = integralShared+ Double.valueOf(share);
                    }else{
                        integralBeShared = integralBeShared+ Double.valueOf(share);
                    }
                }
            }
        }
        double realIntegralShared2 = queryRealIntegralSharedToOper(req.getOperid(),req.getDateMonth(),"1");
        double realIntegralBeShared2 = queryRealIntegralBeShareToOper(req.getOperid(),req.getDateMonth(),"1");

        zssetupJson.put("srccents",zssetupSrccents); //个人实积分
        zssetupJson.put("sharecents",zssetupSharecents-realIntegralShared2+realIntegralBeShared2);  //分享积分  加上实积分分享和被分享的
        zssetupJson.put("groupcode",SalaryConstants.OthersKpi.ZSSETUP);
        zssetupJson.put("groupname","安装积分");
        zssetupJson.put("integralShared",-integralShared+realIntegralShared2);
        zssetupJson.put("integralBeShared",integralBeShared+realIntegralBeShared2);
        ja.put(zssetupJson);


        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(ja);
        return result;
    }

    @Autowired
    private IntegralRealShareService integralRealShareService;

    //给操作员填充分享出去的积分
    public double queryRealIntegralSharedToOper(String operId,String date,String integralType) throws Exception{
        QueryShareRealIntegralReq queryShareRealIntegralReq = new QueryShareRealIntegralReq();
        queryShareRealIntegralReq.setMonth_start(date);
        queryShareRealIntegralReq.setMonth_end(date);
        queryShareRealIntegralReq.setShare_cent_id(operId);
        queryShareRealIntegralReq.setCtype(integralType);

        //查询分享人是这个人的明细，将分数相加即是它分享出去的分
        TokenReturnInfo queryShareRealIntegralInfo = integralRealShareService.queryShareRealIntegral(queryShareRealIntegralReq);
        List<QueryShareRealIntegralBo> centlist = ((List<QueryShareRealIntegralBo> )queryShareRealIntegralInfo.getData());
        double realIntegralShared = 0d;
        if (centlist!=null) {
            for (QueryShareRealIntegralBo integralItem : centlist) {
                realIntegralShared += Double.valueOf(integralItem.getCent());
            }
        }
        return realIntegralShared;

    }
    //给操作员填充被分享的积分
    public double queryRealIntegralBeShareToOper(String operId,String date,String integralType) throws Exception{
        QueryShareRealIntegralReq queryShareRealIntegralReq = new QueryShareRealIntegralReq();
        queryShareRealIntegralReq.setMonth_start(date);
        queryShareRealIntegralReq.setMonth_end(date);
        queryShareRealIntegralReq.setRecv_cent_id(operId);
        queryShareRealIntegralReq.setCtype(integralType);

        //查询被分享人是这个人的明细，将分数相加即是别人分享给他的分
        TokenReturnInfo queryShareRealIntegralInfo = integralRealShareService.queryShareRealIntegral(queryShareRealIntegralReq);
        List<QueryShareRealIntegralBo> centlist = ((List<QueryShareRealIntegralBo> )queryShareRealIntegralInfo.getData());
        double realIntegralBeShared = 0d;
        if (centlist!=null) {
            for (QueryShareRealIntegralBo integralItem : centlist) {
                realIntegralBeShared += Double.valueOf(integralItem.getCent());
            }
        }
        return realIntegralBeShared;

    }

    /**
     * 首页-预积分分组查询
     * @return
     * @throws Exception
     */
    public TokenReturnInfo preGroup(SalaryReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        req.setDateMonth(req.getDateMonth().replace("-",""));

        JSONArray ja = new JSONArray();
        if(StringUtils.isEmpty(req.getOperid()) || StringUtils.isEmpty(req.getDateMonth())){
            throw new Exception("网格人员id和月份不能为空!");
        }

        //--------------销售积分---------------
        //查询个人预积分汇总
        JSONObject zsbusiJson = new JSONObject();
        QueryBeforehandReq qbreq = new QueryBeforehandReq();
        qbreq.setCycleid(req.getDateMonth());
        qbreq.setOperator(req.getOperid());
        qbreq.setGroupcode(SalaryConstants.OthersKpi.ZSBUSI);
        qbreq.setStatus("Y");
        qbreq.setPagesize("1");
        qbreq.setCurrentPage("1");
        QueryBeforehandRep zsbusiRep = beforehandRealService.queryBeforehand(qbreq);
        //查询分享积分
        QueryShareReq qsreq = new QueryShareReq();
        qsreq.setCycleid(req.getDateMonth());
        qsreq.setSendoper(req.getOperid());
        qsreq.setAccoper(req.getOperid());
        qsreq.setGroupcode(SalaryConstants.OthersKpi.ZSBUSI);
        ShareRep zsbusisrep = queryShare(qsreq);

        Double zsbusiSrccents = 0D;
        Double zsbusiSharecents = 0D;
        if(zsbusiRep!=null && zsbusiRep.getTotalcents()!=null){
            zsbusiSrccents = zsbusiRep.getTotalcents();
        }
        if(zsbusisrep!=null && zsbusisrep.getTotalcents()!=null){
            zsbusiSharecents = zsbusisrep.getTotalcents();
        }
        zsbusiJson.put("srccents",zsbusiSrccents); //个人实积分
        zsbusiJson.put("sharecents",zsbusiSharecents);  //分享积分
        zsbusiJson.put("groupcode",SalaryConstants.OthersKpi.ZSBUSI);
        zsbusiJson.put("groupname","销售积分");
        ja.put(zsbusiJson);

        //---------------安装积分---------------
        JSONObject zssetupJson = new JSONObject();
        //查询个人预积分汇总
        qbreq.setGroupcode(SalaryConstants.OthersKpi.ZSSETUP);//安装积分
        QueryBeforehandRep zssetupRep = beforehandRealService.queryBeforehand(qbreq);
        //查询分享积分
        qsreq.setGroupcode(SalaryConstants.OthersKpi.ZSSETUP);//安装积分
        ShareRep zssetupsRep = queryShare(qsreq);
        Double zssetupSrccents = 0D;
        Double zssetupSharecents = 0D;
        if(zssetupRep!=null && zssetupRep.getTotalcents()!=null){
            zssetupSrccents = zssetupRep.getTotalcents();
        }
        if(zssetupsRep!=null && zssetupsRep.getTotalcents()!=null){
            zssetupSharecents = zssetupsRep.getTotalcents();
        }
        zssetupJson.put("srccents",zssetupSrccents); //个人实积分
        zssetupJson.put("sharecents",zssetupSharecents);  //分享积分
        zssetupJson.put("groupcode",SalaryConstants.OthersKpi.ZSSETUP);
        zssetupJson.put("groupname","安装积分");
        ja.put(zssetupJson);

        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(ja);
        return result;
    }

    /**
     * 首页-中山预积分分组查询
     * @return
     * @throws Exception
     */
    public TokenReturnInfo zsPreGroup(SalaryReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();

        Map<String, Object> object = new HashMap<>();

        // 中山积分
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, "登陆已失效，请重新登录!");

        // 查询是否有规则
        Rule rule = ruleService.getCityRuleOrDefault("CENTS_FROM_DC", loginInfo.getCity());

        if (rule != null && StringUtils.equalsIgnoreCase(rule.getPerMission(), "Y")
                && StringUtils.containsIgnoreCase(rule.getValue(), loginInfo.getCity())) {
            LocalDate dataTime = getDataTime(req.getDateMonth());
            // 中山积分查询
            ZsIntegral zsIntegral = new ZsIntegral();
            // 实积分
            ZsIntegral.ActualBean actualBean = new ZsIntegral.ActualBean();
            zsIntegral.setActual(actualBean);

            // 实积分查询
            setTruePoint(req.getOperid(), dataTime.format(DateTimeFormatter.ofPattern("yyyyMM")), actualBean);

            // 预积分
            ZsIntegral.EstimateBean estimateBean = new ZsIntegral.EstimateBean();
            zsIntegral.setEstimate(estimateBean);

            // 销售积分
            DcReqBean reqBean = new DcReqBean();
            reqBean.setServiceId(BizConstant.DcApi.SALES_SP);
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("OPERID", req.getOperid());
            conditions.put("PAYDATE", dataTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            reqBean.setConditions(conditions);
            DcApiResp<SalesSp> dcApiResponse = DcHttpPost.getDcApiResponse(reqBean, SalesSp.class, "SALES_SP");
            List<SalesSp> pageData = dcApiResponse.getPageData();
            if (pageData != null && !pageData.isEmpty()) {
                SalesSp salesSp = pageData.get(0);
                double sp = salesSp.getSP();
                estimateBean.setEstimateSellIntegral(sp);
            }

            // 安装积分
            DcReqBean insReq = new DcReqBean();
            insReq.setServiceId(BizConstant.DcApi.INSTALL_POINT);
            Map<String, Object> insCondition = new HashMap<>();
            insCondition.put("STADATE", dataTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            insCondition.put("OPERID", req.getOperid());
            insReq.setConditions(insCondition);
            DcApiResp<InstallPoint> insResp = DcHttpPost.getDcApiResponse(insReq, InstallPoint.class, "INSTALL_POINT");
            List<InstallPoint> insPageData = insResp.getPageData();
            if (insPageData != null && !insPageData.isEmpty()) {
                InstallPoint installSp = insPageData.get(0);
                String nums = installSp.getNUMS();
                // 转成double
                if (StringUtils.isNotBlank(nums)) {
                    estimateBean.setEstimateInstallIntegral(Double.valueOf(nums));
                }

            }

            estimateBean.setEstimateIntegral(estimateBean.getEstimateSellIntegral() + estimateBean.getEstimateInstallIntegral());

            object.put("otherIntegral", null);
            object.put("zsIntegral", zsIntegral);
        } else {
            req.setDateMonth(req.getDateMonth().replace("-",""));

            List<Object> ja = new ArrayList<>();
            if(StringUtils.isEmpty(req.getOperid()) || StringUtils.isEmpty(req.getDateMonth())){
                throw new Exception("网格人员id和月份不能为空!");
            }

            //--------------销售积分---------------
            //查询个人预积分汇总
            Map<String, Object> zsbusiJson = new HashMap<>();
            QueryBeforehandReq qbreq = new QueryBeforehandReq();
            qbreq.setCycleid(req.getDateMonth());
            qbreq.setOperator(req.getOperid());
            qbreq.setGroupcode(SalaryConstants.OthersKpi.ZSBUSI);
            qbreq.setStatus("Y");
            qbreq.setPagesize("1");
            qbreq.setCurrentPage("1");
            QueryBeforehandRep zsbusiRep = beforehandRealService.queryBeforehand(qbreq);
            //查询分享积分
            QueryShareReq qsreq = new QueryShareReq();
            qsreq.setCycleid(req.getDateMonth());
            qsreq.setSendoper(req.getOperid());
            qsreq.setAccoper(req.getOperid());
            qsreq.setGroupcode(SalaryConstants.OthersKpi.ZSBUSI);
            ShareRep zsbusisrep = queryShare(qsreq);

            Double zsbusiSrccents = 0D;
            Double zsbusiSharecents = 0D;
            if(zsbusiRep!=null && zsbusiRep.getTotalcents()!=null){
                zsbusiSrccents = zsbusiRep.getTotalcents();
            }
            if(zsbusisrep!=null && zsbusisrep.getTotalcents()!=null){
                zsbusiSharecents = zsbusisrep.getTotalcents();
            }
            zsbusiJson.put("srccents",zsbusiSrccents); //个人实积分
            zsbusiJson.put("sharecents",zsbusiSharecents);  //分享积分
            zsbusiJson.put("groupcode",SalaryConstants.OthersKpi.ZSBUSI);
            zsbusiJson.put("groupname","销售积分");
            ja.add(zsbusiJson);

            //---------------安装积分---------------
            Map<String, Object> zssetupJson = new HashMap<>();
            //查询个人预积分汇总
            qbreq.setGroupcode(SalaryConstants.OthersKpi.ZSSETUP);//安装积分
            QueryBeforehandRep zssetupRep = beforehandRealService.queryBeforehand(qbreq);
            //查询分享积分
            qsreq.setGroupcode(SalaryConstants.OthersKpi.ZSSETUP);//安装积分
            ShareRep zssetupsRep = queryShare(qsreq);
            Double zssetupSrccents = 0D;
            Double zssetupSharecents = 0D;
            if(zssetupRep!=null && zssetupRep.getTotalcents()!=null){
                zssetupSrccents = zssetupRep.getTotalcents();
            }
            if(zssetupsRep!=null && zssetupsRep.getTotalcents()!=null){
                zssetupSharecents = zssetupsRep.getTotalcents();
            }
            zssetupJson.put("srccents",zssetupSrccents); //个人实积分
            zssetupJson.put("sharecents",zssetupSharecents);  //分享积分
            zssetupJson.put("groupcode",SalaryConstants.OthersKpi.ZSSETUP);
            zssetupJson.put("groupname","安装积分");
            ja.add(zssetupJson);

            // 其他积分
            object.put("otherIntegral", ja);
            object.put("zsIntegral", null);
        }

        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(object);
        return result;
    }

    /**
     * 获得统计时间
     *
     * @param month
     * @return
     */
    public static LocalDate getDataTime(String month) {
        // 参数 month 格式：yyyyMM
        LocalDate now = LocalDate.now();
        DateTimeFormatter yyyyMM = DateTimeFormatter.ofPattern("yyyyMM");
        String curMonth = now.format(yyyyMM);
        // 如果是当月，查前一天
        if (StringUtils.equalsIgnoreCase(month, curMonth)) {
            LocalDate lastDay = now.minusDays(1);
            return lastDay;
        } else {
            // 非当月查这个月的最后一天
            LocalDate parse = LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
            LocalDate lastDayOfMonth = parse.with(TemporalAdjusters.lastDayOfMonth());
            return lastDayOfMonth;
        }
    }

    /**
     * 查询实积分
     * @param operid
     * @param stadate
     * @param actualBean
     */
    private void setTruePoint(String operid, String stadate, ZsIntegral.ActualBean actualBean) {

        try {
            DcReqBean tpReq = new DcReqBean();
            tpReq.setServiceId(BizConstant.DcApi.ACTUAL_POINT);
            // conditions
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("operid", operid);
            conditions.put("@SQL_stamonth@", stadate);
            tpReq.setConditions(conditions);
            DcApiResp<TruePoint> tpResp = DcHttpPost.getDcApiResponse(tpReq, TruePoint.class, "ACTUAL_POINT");

            if (tpResp != null && tpResp.getPageData() != null && tpResp.getPageData().size() == 1) {
                TruePoint truePoint = tpResp.getPageData().get(0);
                actualBean.setActualIntegral(truePoint.getTotalpoint());
                actualBean.setActualInstallIntegral(truePoint.getSetpoint());
                actualBean.setActualSellIntegral(truePoint.getSaleponit());
                actualBean.setActualRank(truePoint.getTruerank());
            }

        } catch (Exception e) {
            log.error("实积分获取失败:", e);
        }

    }

    /**
     * 获得销售积分
     * @return
     */
    public TokenReturnInfo getSalePointSec(SalaryReq req) throws Exception {
        TokenReturnInfo result = new TokenReturnInfo();

        result.setCode(0L);
        result.setMessage("success");
        result.setData(null);

        String dateMonth = req.getDateMonth();
        String operid = req.getOperid();

        CheckUtils.checkEmpty(dateMonth, "查询时间不能为空!");
        CheckUtils.checkEmpty(operid, "查询操作员不能为空!");

        // 查询销售积分二级汇总
        DcReqBean reqBean = new DcReqBean();
        reqBean.setServiceId(BizConstant.DcApi.SALE_POINT_SEC);
        Map<String, Object> conditions = new HashMap<>();
        reqBean.setConditions(conditions);

        // 查询条件
        LocalDate dataTime = getDataTime(req.getDateMonth());
        conditions.put("PAYDATE", dataTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        conditions.put("OPERID", operid);

        // 调接口
        DcApiResp<SalePointSec> dcApiResponse = DcHttpPost.getDcApiResponse(reqBean, SalePointSec.class, "SALE_POINT_SEC");
        List<SalePointSec> pageData = dcApiResponse.getPageData();

        // 接口数据
        List<Map<String, Object>> data = new ArrayList<>();

        if (pageData != null && !pageData.isEmpty()) {
            // 转换成返回数据
            for (SalePointSec sps : pageData) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("data", sps);
                List<KeyValueData> box = new ArrayList<>();
                box.add(new KeyValueData("原商品", sps.getLAST_PROD()));
                box.add(new KeyValueData("办理的商品名称", sps.getSALESNAME()));
                box.add(new KeyValueData("促销优惠", sps.getSALESPKGNAME()));
                box.add(new KeyValueData("提成金额", sps.getFEE_PER() + ""));
                box.add(new KeyValueData("个数", sps.getNUMS() + ""));
                box.add(new KeyValueData("提成总额", sps.getFEE_TOTAL() + ""));
                map.put("display", box);
                data.add(map);
            }
        }

        result.setData(data);

        return result;
    }

    /**
     * 获得销售积分明细
     * @return
     */
    public TokenReturnInfo getSalePonitLs(SalePonitLsReq req) throws Exception {
        TokenReturnInfo result = new TokenReturnInfo();

        result.setCode(0L);
        result.setMessage("success");
        result.setData(null);

        String dateMonth = req.getPaydate();
        String operid = req.getOperid();
        String lastprod = req.getLastprod();
        String salesname = req.getSalesname();

        CheckUtils.checkEmpty(dateMonth, "查询时间不能为空!");
        CheckUtils.checkEmpty(operid, "查询操作员不能为空!");
        CheckUtils.checkEmpty(req.getSalesname(), "商品不能为空!");

        // 查询销售积分二级汇总
        DcReqBean reqBean = new DcReqBean();
        reqBean.setServiceId(BizConstant.DcApi.SALE_PONIT_LS);
        Map<String, Object> conditions = new HashMap<>();
        reqBean.setConditions(conditions);

        // 查询条件
        conditions.put("PAYDATE", dateMonth);
        conditions.put("OPERID", operid);
        conditions.put("SALESNAME", salesname);
        conditions.put("LAST_PROD", lastprod == null ? "" : lastprod.trim());

        // 调接口
        DcApiResp<SalePonitLs> dcApiResponse = DcHttpPost.getDcApiResponse(reqBean, SalePonitLs.class, "SALE_PONIT_LS");
        List<SalePonitLs> pageData = dcApiResponse.getPageData();

        // Get SYS_SYSTEM
        List<PrvSysparam> systems = paramService.getData("SYS_SYSTEM");

        // 接口数据
        List<Map<String, Object>> data = new ArrayList<>();

        if (pageData != null && !pageData.isEmpty()) {
            // 转换成返回数据
            for (SalePonitLs spl : pageData) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("data", spl);
                List<KeyValueData> box = new ArrayList<>();
                box.add(new KeyValueData("操作时间", spl.getOPTIME()));
                box.add(new KeyValueData("商品名称", spl.getSALESNAME()));
                box.add(new KeyValueData("客户ID", spl.getCUSTID() + ""));
                box.add(new KeyValueData("客户名称", spl.getCUST_NAME() + ""));
                box.add(new KeyValueData("操作部门", spl.getDEPM_NAME() + ""));
                box.add(new KeyValueData("操作员", spl.getOPER_NAME() + ""));
                box.add(new KeyValueData("操作账号", spl.getLOGINNAME() + ""));
                box.add(new KeyValueData("操作渠道", transSys(spl.getOPWAY(), systems) + ""));
                box.add(new KeyValueData("流水号", spl.getSERIALNO() + ""));
                box.add(new KeyValueData("发展人", spl.getDEVNAME() + ""));
                map.put("display", box);
                data.add(map);
            }
        }

        result.setData(data);

        return result;
    }

    /**
     * 获得安装积分
     * @return
     */
    public TokenReturnInfo getInstallPointType(SalaryReq req) throws Exception {
        TokenReturnInfo result = new TokenReturnInfo();

        result.setCode(0L);
        result.setMessage("success");
        result.setData(null);

        String dateMonth = req.getDateMonth();
        String operid = req.getOperid();

        CheckUtils.checkEmpty(dateMonth, "查询时间不能为空!");
        CheckUtils.checkEmpty(operid, "查询操作员不能为空!");

        // 查安装积分结算类型汇总
        DcReqBean reqBean = new DcReqBean();
        reqBean.setServiceId(BizConstant.DcApi.INSTALL_POINT_TYPE);
        Map<String, Object> conditions = new HashMap<>();
        reqBean.setConditions(conditions);

        // 查询条件
        LocalDate dataTime = getDataTime(req.getDateMonth());
        conditions.put("STADATE", dataTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        conditions.put("OPERID", operid);

        // 调接口
        DcApiResp<InstallPointType> dcApiResponse = DcHttpPost.getDcApiResponse(reqBean, InstallPointType.class, "INSTALL_POINT_TYPE");
        List<InstallPointType> pageData = dcApiResponse.getPageData();

        // 接口数据
        List<Map<String, Object>> data = new ArrayList<>();

        if (pageData != null && !pageData.isEmpty()) {
            // 转换成返回数据
            for (InstallPointType ipt : pageData) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("data", ipt);
                List<KeyValueData> box = new ArrayList<>();
                box.add(new KeyValueData(ipt.getSTYPE(), ipt.getNUMS()));
                map.put("display", box);
                data.add(map);
            }
        }

        result.setData(data);

        return result;
    }

    /**
     * 获得安装积分明细
     * @return
     */
    public TokenReturnInfo getInstallPointLs(InstallPointLsReq req) throws Exception {
        TokenReturnInfo result = new TokenReturnInfo();

        result.setCode(0L);
        result.setMessage("success");
        result.setData(null);

        String dateMonth = req.getStadate();
        String operid = req.getOperid();
        String stype = req.getStype();

        CheckUtils.checkEmpty(dateMonth, "查询时间不能为空!");
        CheckUtils.checkEmpty(operid, "查询操作员不能为空!");
        CheckUtils.checkEmpty(req.getStype(), "结算类型不能为空!");

        int pageNo = req.getPageNo() == 0 ? 1 : req.getPageNo();
        int pageSize = req.getPageSize() == 0 ? 10 : req.getPageSize();

        // 查询销售积分二级汇总
        DcReqBean reqBean = new DcReqBean();
        reqBean.setPageIndex(pageNo);
        reqBean.setPageSize(pageSize);
        reqBean.setServiceId(BizConstant.DcApi.INSTALL_PONIT_LS);
        Map<String, Object> conditions = new HashMap<>();
        reqBean.setConditions(conditions);

        // 查询条件
        conditions.put("STADATE", dateMonth);
        conditions.put("OPERID", operid);
        conditions.put("STYPE", stype);

        // 调接口
        DcApiResp<InstallPointLs> dcApiResponse = DcHttpPost.getDcApiResponse(reqBean, InstallPointLs.class, "INSTALL_PONIT_LS");
        List<InstallPointLs> pageData = dcApiResponse.getPageData();

        // 接口数据
        List<Map<String, Object>> data = new ArrayList<>();

        Page<Map<String, Object>> page = new Page<>();
        page.setPageNo(dcApiResponse.getPageNo());
        page.setPageSize(dcApiResponse.getPageSize());
        page.setResult(data);
        page.setTotalCount(dcApiResponse.getRecordCount());

        if (pageData != null && !pageData.isEmpty()) {
            // 转换成返回数据
            for (InstallPointLs spl : pageData) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("data", spl);
                List<KeyValueData> box = new ArrayList<>();
                box.add(new KeyValueData("客户名称", spl.getCUSTNAME()));
                box.add(new KeyValueData("受理时间", spl.getACCEPT_DATE()));
                box.add(new KeyValueData("工单类型", spl.getORDERTYPE() + ""));
                box.add(new KeyValueData("机顶盒类型", spl.getSUBKIND() + ""));
                box.add(new KeyValueData("机顶盒号", spl.getDEVNO() + ""));
                box.add(new KeyValueData("更换前机顶盒号", spl.getODEVNO() + ""));
                box.add(new KeyValueData("更换前机顶盒型号", spl.getOSUBKINDNAME() + ""));
                box.add(new KeyValueData("回单时间", spl.getBACK_DATE()));
                box.add(new KeyValueData("处理时长", transMinuteTo(spl.getDEALHOUR()) + ""));
                box.add(new KeyValueData("维护人", spl.getDEFENDER() + ""));
                box.add(new KeyValueData("BOSS处理人", spl.getOPERATORS() + ""));
                box.add(new KeyValueData("积分", spl.getNUMS() + ""));
                map.put("display", box);
                data.add(map);
            }
        }

        result.setData(page);

        return result;
    }

    /**
     * 分钟转成 x天x小时x天
     * @param minutes
     * @return
     */
    private String transMinuteTo(String minutes) {
        StringBuffer ret = new StringBuffer();
        // 天
        Double minutesd = Double.valueOf(minutes);
        int day = (int) (minutesd / 60 / 24);
        if (day > 0) {
            ret.append(day).append("天");
        }
        // 小时
        double lastMinutes = minutesd - day * 24 * 60;
        int hour = (int) (lastMinutes / 60);
        ret.append(hour).append("小时");
        // 分
        Double minu = lastMinutes - hour * 60;
        minu = Math.floor(minu);
        ret.append(minu.intValue()).append("分");
        return ret.toString();
    }

    /**
     * 操作渠道 转换
     * @param opway
     * @param systems
     * @return
     */
    private String transSys(String opway, List<PrvSysparam> systems) {

        if (systems != null && !systems.isEmpty()) {
            for (PrvSysparam system : systems) {
                String mname = system.getMname();
                String mcode = system.getMcode();
                if (StringUtils.equalsIgnoreCase(opway, mcode)) {
                    opway = mname;
                    break;
                }
            }
        }

        return opway;
    }

    /**
     * 其他积分查询
     * @return
     * @throws Exception
     */
    public TokenReturnInfo othersKpi(SalaryReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        JSONArray obj = othersKpiService.othersKpi(req.getOperid(),req.getDateMonth());
        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(obj);
        return result;
    }

    /**
     * 基本薪酬和运维薪酬
     * @return
     * @throws Exception
     */
    public TokenReturnInfo baseWage(SalaryReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        JSONArray ja = baseWageService.baseWage(req.getOperid(),req.getDateMonth());
        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(ja);
        return result;
    }

    /**
     * 查询分组明细
     * @param req
     * @return
     * @throws Exception
     */
    public RealDetailRep othersKpiGroupDetail(OperatorDetailReq req) throws Exception{
        RealDetailRep result = new RealDetailRep();
        req.setCycleid(req.getCycleid().replace("-",""));

        List<BeforehandRealDetailBO> rep =  beforehandRealService.findDetail(req);
        List<BeforehandRealDetailBO> remvoes = new ArrayList<BeforehandRealDetailBO>();
        if(rep!=null && rep.size()>0){
            for (BeforehandRealDetailBO detail : rep) {
                //过滤积分为0的数据
                if((StringUtils.isEmpty(detail.getSrccents()) && StringUtils.isEmpty(detail.getAdjustcents())) ||
                        ("0".equals(detail.getSrccents()) && "0".equals(detail.getAdjustcents())) ){
                    remvoes.add(detail);
                    continue;
                }
                detail.setCents("0");
                if(StringUtils.isNotEmpty(detail.getSrccents())){
                    detail.setCents(detail.getSrccents());
                }
                if(StringUtils.isNotEmpty(detail.getAdjustcents())){
                    Double adjustcents = Double.valueOf(detail.getAdjustcents());
                    detail.setCents((Double.valueOf(detail.getCents())+adjustcents)+"");
                }
            }
            if(remvoes.size()>0) {
                rep.removeAll(remvoes);
            }
        }
        //输出
        result.setCode(0L);
        result.setMessage("");
        result.setData(rep);
        return result;
    }
    /**
     * 预积分分组
     * @return
     * @throws Exception
     */
    public TokenReturnInfo beforehandGroup(PreCentQryTotalReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        req.setCycleid(req.getCycleid().replace("-",""));
        PreCentQryTotalRep rep = beforehandRealService.beforehandGroup(req);
        if(rep!=null && rep.getScenelist()!=null && rep.getScenelist().size()>0){
            List<PreCentQryTotalRep.Detail> remvoes = new ArrayList<PreCentQryTotalRep.Detail>();
            for (PreCentQryTotalRep.Detail detail : rep.getScenelist()) {
                //过滤积分为0的数据
                if((StringUtils.isEmpty(detail.getTotalcents()) || "0".equals(detail.getTotalcents())) &&
                        (StringUtils.isEmpty(detail.getSrccents()) || "0".equals(detail.getSrccents()))){
                    remvoes.add(detail);
                    continue;
                }
                if(StringUtils.isNotEmpty(detail.getScene())) {
                    String sceneName = paramService.getMname(SalaryConstants.getSceneGcode(req.getGroupcode()), detail.getScene());
                    detail.setSceneName(sceneName);
                }else{
                    detail.setScene("OT");//如果为空的情况，则设置OT，在去调用列表明细的接口时传入这个字段
                    detail.setSceneName("其他");
                }
            }
            if(remvoes.size()>0) {
                rep.getScenelist().removeAll(remvoes);
            }
        }
        result.setCode(Long.valueOf(rep.getRtcode()));
        result.setMessage(rep.getMessage());
        result.setData(rep.getScenelist());
        return result;
    }
    /**
     * 实积分分组
     * @return
     * @throws Exception
     */
    public TokenReturnInfo realGroup(RealCentQryTotalReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        req.setCycleid(req.getCycleid().replace("-",""));
        RealCentQryTotalRep rep = beforehandRealService.realGroup(req);
        if(rep!=null && rep.getScenelist()!=null && rep.getScenelist().size()>0){
            List<RealCentQryTotalRep.Detail> remvoes = new ArrayList<RealCentQryTotalRep.Detail>();
            for (RealCentQryTotalRep.Detail detail : rep.getScenelist()) {
                //过滤积分为0的数据
                if((StringUtils.isEmpty(detail.getTotalcents()) || "0".equals(detail.getTotalcents())) &&
                        (StringUtils.isEmpty(detail.getSrccents()) || "0".equals(detail.getSrccents()))){
                    remvoes.add(detail);
                    continue;
                }
                if(StringUtils.isNotEmpty(detail.getAdjustcents())){
                    Double srccents = Double.valueOf(detail.getSrccents())+Double.valueOf(detail.getAdjustcents());
                    detail.setSrccents(srccents.toString());
                }
                if(StringUtils.isNotEmpty(detail.getScene())) {
                    String gcode = SalaryConstants.getSceneGcode(req.getGroupcode());
                    String sceneName = paramService.getMname(gcode,detail.getScene());
                    detail.setSceneName(sceneName);
                }else{
                    detail.setScene("OT");//如果为空的情况，则设置OT，在去调用列表明细的接口时传入这个字段
                    detail.setSceneName("其他");
                }
            }

            if(remvoes.size()>0) {
                rep.getScenelist().removeAll(remvoes);
            }
        }
        result.setCode(Long.valueOf(rep.getRtcode()));
        result.setMessage(rep.getMessage());
        result.setData(rep.getScenelist());
        return result;
    }

    /**
     * 获取积分用户数
     * @return
     */
    public TokenReturnInfo getSalaryUserNum(SalaryReq req) throws Exception {
        TokenReturnInfo result = new TokenReturnInfo();
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        BizGridInfo grid = getGrids();
        String sql="select a.stadate month ,a.whgridcode gridCode,a.whgridname gridName,a.kpiid flag ,b.kpiname " +
                "flagName,a.kpivalue flagSum,etl.code2name(a.city,'PRV_CITY') city from dm.dm_kpi_whgrid a,frntpc.TR_KPICODE_CFG " +
                "b where a.kpiid = b.kpiid and a.city =? and a.kpiid = 'S01001012021000' and a.stadate != " +
                "'NNN' and a.stadate =? and a.whgridcode =?  and a.areaid = 'NNN'" +
                "group by a.stadate,a.whgridcode,a.whgridname,a.kpiid,b.kpiname,a.kpivalue,a.city " +
                "order by a.stadate,a.whgridcode,a.whgridname,a.kpiid,b.kpiname,a.kpivalue,a.city";
       List<Object> list = SpringBeanUtil.getPersistentService().findObjectList(sql,loginInfo.getCity(),req.getDateMonth(),grid.getGridcode());
        result.setCode(Long.valueOf(0));
        result.setMessage("");
        if(list!=null && list.size()>0) {
            Object[] data = (Object[])list.get(0);
            result.setData(data[5]);
        }
        return result;
    }
    /**
     * 获取用户信息login
     * @return
     * @throws Exception
     */
     public TokenReturnInfo getTokenLoginInfo() throws Exception{
         TokenReturnInfo result = new TokenReturnInfo();
         LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
         result.setCode(Long.valueOf(0));
         result.setMessage("");
         result.setData(loginInfo);
        return result;
    }

    /**
     * 获取页面模块集合
     * @return
     * @throws Exception
    
    public TokenReturnInfo findPageModule(SalaryReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        if(req==null || req.getCity()==null){
            result.setCode(IErrorDefConstant.ERROR_PROPERTY_MISSING_CODE);
            result.setMessage("请求参数city不能为空!");
            return result;
        }
        String sql="select distinct id,module_type moduleType from SALARY_MODULE where status='0'" +
                " and hierarchy=1 and city=?";
        List<SalaryModule> modules = persistentService.find(sql,SalaryModule.class,req.getCity());
        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(modules);
        return result;
    } */
 /**
     * 获取模块内容
     * @return
     * @throws Exception
     
    public TokenReturnInfo getModule(SalaryReq req) throws Exception{
        TokenReturnInfo result = new TokenReturnInfo();
        String sql="select distinct id,module_type moduleType from SALARY_MODULE where status='0' and hierarchy=1 and id=?";
        List<SalaryModule> modules = persistentService.find(sql,SalaryModule.class,req.getSearch());

        result.setCode(Long.valueOf(0));
        result.setMessage("");
        result.setData(modules);
        return result;
    }*/



    public BizGridInfo getGrids() throws Exception {
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        Long operId = loginInfo.getOperid();

        StringBuffer sql = new StringBuffer();

        List paramList = new ArrayList();

        sql.append(" SELECT *  FROM (");
        sql.append(" SELECT t.gridid as id, ");
        sql.append("t.gridcode, ");
        sql.append("t.gridname, ");
        sql.append("t.gtype, ");
        sql.append("t.mnrid, ");
        sql.append("t.prio, ");
        sql.append("t.previd, ");
        sql.append("t.statid, ");
        sql.append("t.patchid, ");
        sql.append("t.memo ");
        sql.append(" FROM biz_grid_info t, biz_grid_manager m ");
        sql.append(" WHERE t.gridid=m.gridid  ");
        sql.append(" AND t.city = ? AND m.operid = ? AND ismain='Y'");
        paramList.add(loginInfo.getCity());
        paramList.add(operId);
        sql.append(" ) v ");

        persistentService.clear();
        List<BizGridInfo> gridlist = persistentService.find(sql.toString(), BizGridInfo.class, paramList.toArray());
        if (gridlist != null && gridlist.size() > 0) {
            return gridlist.get(0);
        }else{
            throw new BusinessException("查找不到当前登录地市关联的主网格!");
        }

    }
}
