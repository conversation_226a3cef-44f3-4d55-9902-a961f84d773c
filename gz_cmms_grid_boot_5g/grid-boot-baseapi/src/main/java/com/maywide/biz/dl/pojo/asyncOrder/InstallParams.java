package com.maywide.biz.dl.pojo.asyncOrder;

public class InstallParams {

	private String permark;
	
	private String feekind;
	
	private String bankid;
	
	private String payway;
	
	private String sm_useprop;
	
	private String logicdevno;
	
	private String smnopid;
	
	private String stbno;
	
	private String stb_useprop;
	
	private String stbpid;
	
	private String cmno;
	
	private String cmuseprop;
	
	private String cmpid;
	
	private String memo;
	
	public String getPermark() {
		return permark;
	}

	public void setPermark(String permark) {
		this.permark = permark;
	}

	public String getFeekind() {
		return feekind;
	}

	public void setFeekind(String feekind) {
		this.feekind = feekind;
	}

	public String getBankid() {
		return bankid;
	}

	public void setBankid(String bankid) {
		this.bankid = bankid;
	}

	public String getPayway() {
		return payway;
	}

	public void setPayway(String payway) {
		this.payway = payway;
	}

	public String getSm_useprop() {
		return sm_useprop;
	}

	public void setSm_useprop(String sm_useprop) {
		this.sm_useprop = sm_useprop;
	}

	public String getLogicdevno() {
		return logicdevno;
	}

	public void setLogicdevno(String logicdevno) {
		this.logicdevno = logicdevno;
	}

	public String getSmnopid() {
		return smnopid;
	}

	public void setSmnopid(String smnopid) {
		this.smnopid = smnopid;
	}

	public String getStbno() {
		return stbno;
	}

	public void setStbno(String stbno) {
		this.stbno = stbno;
	}

	public String getStb_useprop() {
		return stb_useprop;
	}

	public void setStb_useprop(String stb_useprop) {
		this.stb_useprop = stb_useprop;
	}

	public String getStbpid() {
		return stbpid;
	}

	public void setStbpid(String stbpid) {
		this.stbpid = stbpid;
	}

	public String getCmno() {
		return cmno;
	}

	public void setCmno(String cmno) {
		this.cmno = cmno;
	}

	public String getCmuseprop() {
		return cmuseprop;
	}

	public void setCmuseprop(String cmuseprop) {
		this.cmuseprop = cmuseprop;
	}

	public String getCmpid() {
		return cmpid;
	}

	public void setCmpid(String cmpid) {
		this.cmpid = cmpid;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	
}
