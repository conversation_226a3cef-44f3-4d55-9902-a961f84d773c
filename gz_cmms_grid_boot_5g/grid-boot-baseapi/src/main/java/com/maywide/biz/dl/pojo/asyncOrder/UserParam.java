package com.maywide.biz.dl.pojo.asyncOrder;

public class UserParam {

	private String logicdevno;
	
	private String permark;
	
	private String servtype;
	
	private String pservid;
	
	private String iscabl;
	
	private String subinputway;
	
	private String authmode;
	
	private String ipmode;
	
	private String ipnum;
	
	private String cmacctno;
	
	private String password;
	
	private String isinner;
	
	private String inputway;
	
	private String outdevno;

	public String getLogicdevno() {
		return logicdevno;
	}

	public void setLogicdevno(String logicdevno) {
		this.logicdevno = logicdevno;
	}

	public String getPermark() {
		return permark;
	}

	public void setPermark(String permark) {
		this.permark = permark;
	}

	public String getServtype() {
		return servtype;
	}

	public void setServtype(String servtype) {
		this.servtype = servtype;
	}

	public String getPservid() {
		return pservid;
	}

	public void setPservid(String pservid) {
		this.pservid = pservid;
	}

	public String getIscabl() {
		return iscabl;
	}

	public void setIscabl(String iscabl) {
		this.iscabl = iscabl;
	}

	public String getSubinputway() {
		return subinputway;
	}

	public void setSubinputway(String subinputway) {
		this.subinputway = subinputway;
	}

	public String getAuthmode() {
		return authmode;
	}

	public void setAuthmode(String authmode) {
		this.authmode = authmode;
	}

	public String getIpmode() {
		return ipmode;
	}

	public void setIpmode(String ipmode) {
		this.ipmode = ipmode;
	}

	public String getIpnum() {
		return ipnum;
	}

	public void setIpnum(String ipnum) {
		this.ipnum = ipnum;
	}

	public String getCmacctno() {
		return cmacctno;
	}

	public void setCmacctno(String cmacctno) {
		this.cmacctno = cmacctno;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getIsinner() {
		return isinner;
	}

	public void setIsinner(String isinner) {
		this.isinner = isinner;
	}

	public String getOutdevno() {
		return outdevno;
	}

	public void setOutdevno(String outdevno) {
		this.outdevno = outdevno;
	}

	public String getInputway() {
		return inputway;
	}

	public void setInputway(String inputway) {
		this.inputway = inputway;
	}
	
	
	
}
