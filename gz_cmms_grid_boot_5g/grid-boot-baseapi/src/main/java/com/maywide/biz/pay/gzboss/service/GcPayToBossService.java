package com.maywide.biz.pay.gzboss.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.inter.entity.CustBizNetWorkOrderPool;
import com.maywide.biz.inter.pojo.chargeFeeBook.QuePayOrderResp;
import com.maywide.biz.market.bo.BizPayRefundLogBo;
import com.maywide.biz.market.entity.BizPayRefundLog;
import com.maywide.biz.market.entity.BizPortalOrder;
import com.maywide.biz.market.entity.CustOrder;
import com.maywide.biz.pay.gzboss.pojo.*;
import com.maywide.biz.pay.gzboss.pojo.GwPayOrderInfoBo;
import com.maywide.biz.pay.task.OrderExceptionMonitorTask;
import com.maywide.biz.pay.task.bean.OrderInfoBean;
import com.maywide.biz.pay.task.entity.BizExceptionRecord;
import com.maywide.biz.pay.unify.entity.PayRecordLog;
import com.maywide.biz.pay.unify.entity.PayRecordStartLog;
import com.maywide.biz.pay.unify.pojo.UnifyPayCondition;
import com.maywide.biz.pay.unify.service.PayNoticeService;
import com.maywide.biz.pay.unify.service.UnifyPayService;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.dao.support.Page;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.maywide.biz.pay.unify.entity.PayRecordLog.ACTION_TYPE_REFUND;
import static com.maywide.biz.pay.unify.entity.PayRecordLog.PAY_RESULT_PAYFAIL;

/**
 * 贵州BOSS支付
 *
 * <AUTHOR>
 */
@Service
public class GcPayToBossService extends CommonService {

    @Autowired
    private PayNoticeService noticeService;
    @Autowired
    private UnifyPayService unifyPayService;

    /**
     * 前端记录支付开始日志
     *
     * @param req
     * @param resp
     * @return
     */
    public  ReturnInfo recordStartLog(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        CheckUtils.checkEmpty(req.getPayType(), "CMMS:支付方式为空");
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        try {
            PayRecordStartLog recordLog = new PayRecordStartLog();
            recordLog.setActionType(req.getPayType());
            recordLog.setCustorderid(Long.valueOf(req.getCustorderid()));
            recordLog.setPaydate(new Date());
            getDAO().save(recordLog);
        } catch (Exception e) {
            // 记录失败
            throw new BusinessException(e.getMessage());
        }
        return returnInfo;
    }

    /**
     * 贵州BOSS支付接口，扫客户支付码
     *
     * @param req
     * @param resp
     * @return
     */
    public synchronized ReturnInfo bScanCToPayByBoss(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getPayType(), "CMMS:支付方式为空");
        CheckUtils.checkEmpty(req.getAuthCode(), "CMMS:支付条码为空");
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        CheckUtils.checkNull(req.getCondition(), "CMMS:支付条件为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }

        // 2. 获取流水号
        Long snowflakeOrderId = getSnowflakeOrderId();
        custOrder.getPortalOrder().setPayid(snowflakeOrderId);
        log.info("=>[{}]支付流失号: {}", req.getCustorderid(), snowflakeOrderId);
        // 3. 初始化支付记录
        PayRecordLog recordLog = initPayRecordLog(snowflakeOrderId, custOrder, PayRecordLog.ACTION_TYPE_PAY);
        // 4. 封装BOSS请求
        BScanCPayReq bossreq = new BScanCPayReq();
        bossreq.setBizorderid(req.getBizorderid());
        bossreq.setPayCode(BScanCPayReq.PAYCODE_PAY);
        bossreq.setPayInfo(new PayInfo());
        bossreq.setCustInfo(new HashMap<>());
        bossreq.getPayInfo().setPayType(req.getPayType());
        bossreq.getPayInfo().setMchntOrderNo(snowflakeOrderId.toString());
        bossreq.getPayInfo().setAuthCode(req.getAuthCode());
        bossreq.getPayInfo().setAmount(getAmount(custOrder.getPortalOrder()));
        bossreq.getCustInfo().put("custName", custOrder.getName());
        bossreq.getCustInfo().put("cust_id", custOrder.getCustid());
        bossreq.getCustInfo().put("custareaid", custOrder.getAreaid());
        bossreq.getCustInfo().put("city", custOrder.getCity());

        String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(req.getCustorderid());
        if (StringUtils.isNotBlank(gwPayOrderInfo)) {
            JSONObject customInfo = JSONObject.parseObject(gwPayOrderInfo);
            bossreq.setBizCustomInfo(customInfo);
            bossreq.getCustInfo().put("servid", customInfo.get("servid"));
        }

        // 5. 调用BOSS接口支付
        try {
            log.info("[{}]调用BOSS接口支付.", req.getCustorderid());
            BScanCPayResp payResp = bScanCPay(bossreq, loginInfo);
            if (payResp != null) {
                String tranStatus = payResp.getStatus();
                if (StringUtils.equals(tranStatus, BScanCPayResp.STATUS_FAIL)) {
                    throw new BusinessException("CMMS:支付失败.");
                }
            }

            // 将bossorderno补充到支付完成的数据里面
            JSONObject customInfo = payResp.getBizCustomInfo();
            if (customInfo != null && bossreq.getBizCustomInfo() != null) {
                JSONObject bizCustomInfo = (JSONObject) bossreq.getBizCustomInfo();
                customInfo.put("bossorderno", bizCustomInfo.getString("bossorderno"));
            }

            // 6. 支付成功，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYED);
            recordLog.setPaydate(new Date());
            recordLog.setRedicetStr(JSON.toJSONString(payResp));
            updatePayRecordLog(recordLog);

            // 更新biz_portal_order表的payid字段
            getDAO().executeSql("update biz_portal_order set payid = ? where orderid = ?",
                    custOrder.getPortalOrder().getPayid(), custOrder.getId());

            BeanUtils.copyProperties(resp, payResp);
        } catch (Exception e) {
            log.error(String.format("=>[%s]-[%s]支付失败", req.getCustorderid(), snowflakeOrderId), e);
            // 6. 支付失败，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYFAIL);
            recordLog.setPaydate(new Date());
            recordLog.setBizexmsg(e.getMessage());
            updatePayRecordLog(recordLog);
            throw new BusinessException(e.getMessage());
        }

        log.info("=>[{}]订单确认.", req.getCustorderid());
        // 7. 订单确认
        bossOrderCommit(custOrder, resp, req.getCondition(), loginInfo);

        return returnInfo;
    }


    /**
     * 贵州BOSS二维码支付接口，客户扫描二维码支付
     * prv_interface_2_service表配置入口
     * @param req
     * @param resp
     * @return
     */
    public  ReturnInfo gwPayMent(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getPayType(), "CMMS:支付方式为空");
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_REFUND)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经退款，无法再提交", req.getCustorderid()));
        }
        boolean locked = false;
        //1.1查询是否已经生成二维码地址
        if(orderLocked(req.getCustorderid())!=1) {
            locked=true;
        }
//        locked = false;
            //已经生成二维码-且是未确认的时候
        if(null!=custOrder.getPortalOrder().getScanurl() && !"".equals(custOrder.getPortalOrder().getScanurl())){
            if(!StringUtils.equals(req.getPayType(),custOrder.getPortalOrder().getSubpayway())){
                if(locked){
                    throw new BusinessException("CMMS:支付请求生成中，请稍等一分钟后再试");
                }
                getDAO().executeSql("update biz_portal_order set gworderno=?,scanurl=?,subpayway=? where orderid = ?", "","","", custOrder.getId());

//                throw new BusinessException(String.format("CMMS:切换支付失败，暂不支持切换支付方式", req.getCustorderid()));

                //暂时屏蔽撤销接口
//                //如果发生支付方式变更要进行撤销，再重新支付
//                JSONObject queryResult = gWCancelOrder(req.getBizorderid(),custOrder.getPortalOrder().getGworderno(), loginInfo);
//                if (queryResult != null) {
//                    String queryStatus = queryResult.getString("status");
//                    if (StringUtils.equals(queryStatus, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_OVERDUE)) {
//                        // 更新biz_portal_order表的payid字段
//                        getDAO().executeSql("update biz_portal_order set gworderno=?,scanurl=?,subpayway=? where orderid = ?", "","","", custOrder.getId());
//                        resp.setStatus("3");//撤销，过期支付
//                        return returnInfo;
//                    }else{
//                        throw new BusinessException(String.format("CMMS:撤销支付失败", req.getCustorderid()));
//                    }
//                }
            }else{
//                if(custOrder.getPortalOrder().getLongtime()+10*60*1000>System.currentTimeMillis()) {
//                    resp.setStatus("1");//已经支付过了
//                    resp.setScanUrl(custOrder.getPortalOrder().getScanurl());
//                    return returnInfo;
//                }
            }
        }
        if(locked){
            throw new BusinessException("CMMS:支付请求生成中，请稍等一分钟后再试");
        }

        // 2. 获取流水号
        Long snowflakeOrderId = getSnowflakeOrderId();
        custOrder.getPortalOrder().setPayid(snowflakeOrderId);
        log.info("=>[{}]支付流失号: {}", req.getCustorderid(), snowflakeOrderId);

        // 3. 初始化支付记录
        PayRecordLog recordLog = initPayRecordLog(snowflakeOrderId, custOrder, PayRecordLog.ACTION_TYPE_PAY);
        // 4. 封装国网订单BOSS请求
        // 获得国网订单信息,payorderno
        String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(req.getCustorderid());
        String bossorderno = JSONObject.parseObject(gwPayOrderInfo).get("bossorderno").toString();
        BizQdElecpayReq reqBoss = new BizQdElecpayReq();
        reqBoss.setPayorderno(bossorderno);
        reqBoss.setPayway("6");
        if("01".equals(req.getPayType())){
            reqBoss.setSubpayway("042700");
        }else if("02".equals(req.getPayType())){
            reqBoss.setSubpayway("043700");
        }else if("03".equals(req.getPayType())){
            reqBoss.setPayway("K");
            reqBoss.setSubpayway("JH_QR");
        }
        reqBoss.setCustid(custOrder.getCustid()+"");
        reqBoss.setClient_ip(getRemortIP());
        reqBoss.setPayreqid(snowflakeOrderId+"");
        log.info("[{}]调用国网BOSS接口支付.，流水号[{}]", req.getCustorderid(),snowflakeOrderId);
        // 5. 调用BOSS接口支付
        try {
            BScanCPayResp payResp =  cScanBPay(req.getBizorderid(),reqBoss,loginInfo);
//            String bossRespOutput = getBossHttpInfOutput(req.getBizorderid(),BizConstant.BossInterfaceService.BIZ_QD_ELECPAY,reqBoss, loginInfo);
//            if(StringUtils.isBlank(bossRespOutput)) {
//
//                throw new BusinessException("CMMS:支付二维码生成失败.");
//
//            }
//            GwOrderPayBo orderPayBo = JSON.parseObject(bossRespOutput,GwOrderPayBo.class);
//            BScanCPayResp payResp = new BScanCPayResp();
//            payResp.setPaycode(reqBoss.getSubpayway());
//            payResp.setPayway(reqBoss.getPayway());
//            payResp.setOrderNo(orderPayBo.getOrder_no();
//            payResp.setOrderAmount(orderPayBo.getOrder_amount());
//            payResp.setScanUrl(orderPayBo.getScan_url());
//            payResp.setStatus(orderPayBo.getPay_status());
//            GwPayOrderInfoBo gwPayOrderInfoBo = new GwPayOrderInfoBo();
//            com.maywide.core.util.BeanUtils.copyProperties(gwPayOrderInfoBo, orderPayBo);
//            gwPayOrderInfoBo.setBossorderno(bossorderno);
//            payResp.setBizCustomInfo(JSONObject.parseObject(JSON.toJSONString(gwPayOrderInfoBo)));

            if(payResp==null){
                throw new BusinessException("CMMS:支付二维码生成失败.");
            }else if("2".equals(payResp.getStatus())){  //2:已支付
                if(StringUtils.isNotBlank(payResp.getScanUrl())&&payResp.getScanUrl().equals(custOrder.getPortalOrder().getScanurl())){
                    resp.setStatus("2");//已经支付过了
//                    resp.setScanUrl(custOrder.getPortalOrder().getScanurl());
                    return returnInfo;
                }
                 throw new BusinessException("CMMS:支付二维码生成失败.");
            }
            // 6. 支付成功，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYED);
            recordLog.setPaydate(new Date());
            recordLog.setRedicetStr(JSON.toJSONString(payResp));
            updatePayRecordLog(recordLog);
            // 更新biz_portal_order表的payid字段
            getDAO().executeSql("update biz_portal_order set payid = ?,gworderno=?,scanurl=?,subpayway=? where orderid = ?",
                    custOrder.getPortalOrder().getPayid(),payResp.getOrderNo(),payResp.getScanUrl(),req.getPayType(), custOrder.getId());
            resp.setScanUrl(payResp.getScanUrl());
            resp.setStatus("1");
            //保存国网的支付成功信息
            saveOrderSource(Long.valueOf(req.getCustorderid()), BizConstant.SourceCode.GW_CODE_PAY_ORDERINFO, JSON.toJSONString(payResp));

            OrderInfoBean orderInfoBean = new OrderInfoBean();
            orderInfoBean.setLoginInfo(loginInfo);
            orderInfoBean.setOrderId(req.getCustorderid());
            orderInfoBean.setCreateTime(System.currentTimeMillis());
            orderInfoBean.setPayment(req.getPayType());
            // 监控订单
            OrderExceptionMonitorTask.add(orderInfoBean);

        } catch (Exception e) {
            log.error(String.format("=>[%s]-[%s]支付失败", req.getCustorderid(), snowflakeOrderId), e);
            // 6. 支付失败，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYFAIL);
            recordLog.setPaydate(new Date());
            recordLog.setBizexmsg(e.getMessage());
            updatePayRecordLog(recordLog);

            //释放锁
            getDAO().executeSql("update biz_portal_order set longtime=0 where orderid = ?  ",req.getCustorderid());

            throw new BusinessException(e.getMessage());
        }

        return returnInfo;



    }

    private int orderLocked(String orderid){
        long longtime = System.currentTimeMillis();
        try {
            return getDAO().executeSql("update biz_portal_order set locked='locked',longtime=? where orderid = ? and status!=2 and (locked is null or (locked ='locked' and  longtime < ?) ) ",
                    longtime, orderid,longtime-1*30*1000);
        } catch (Exception e) {

            return 0;
        }
    }

    private int orderCommitLocked(String orderid){
        long longtime = System.currentTimeMillis();
        try {
            return getDAO().executeSql("update biz_portal_order set locked='commit',longtime=? where orderid = ? and status!=2 and (locked!='commit' or locked is null or (locked ='commit' and longtime < ? )) ",
                    longtime, orderid,longtime-1*60*1000);
        } catch (Exception e) {

            return 0;
        }
    }


    private String getRemortIP() throws Exception {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = "";
        if (request.getHeader("x-forwarded-for") == null) {
            ip = request.getRemoteAddr();
        }else{
            ip = request.getHeader("x-forwarded-for");
        }
        return ip;
    }


    /**
     * 查询订单支付方式
     *
     * @param req
     * @param resp
     * @return
     */
    public  ReturnInfo queryPayWay(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }
        try {
            //支付方式
            resp.setPayway(custOrder.getPortalOrder().getSubpayway());

            List<PrvSysparam> sysPayway = paramService.getData("SYS_PAYWAY");
            Map<String, String> map = sysPayway.stream().collect(Collectors.toMap(PrvSysparam::getMcode, PrvSysparam::getMname));

            if (StringUtils.isNotBlank(custOrder.getPortalOrder().getSubpayway())) {
                resp.setPayname(map.get(custOrder.getPortalOrder().getSubpayway()));
            } else {
                resp.setPayname(map.get(custOrder.getPortalOrder().getPayway()));
            }

        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }

        return returnInfo;
    }


    /**
     * 贵州BOSS二维码支付查询接口
     *
     * @param req
     * @param resp
     * @return
     */
    public  ReturnInfo queryOrderResult(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            resp.setStatus("200");
            return returnInfo;
        }
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_REFUND)) {
            resp.setStatus("7");
            return returnInfo;
        }
        if(StringUtils.isBlank(custOrder.getPortalOrder().getScanurl())){
            resp.setStatus("0");
            return returnInfo;
        }
        try {
            // 获得国网订单信息,payorderno
            String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(req.getCustorderid());
            String orderNo = JSONObject.parseObject(gwPayOrderInfo).get("bossorderno").toString();
//            String orderNo= custOrder.getPortalOrder().getGworderno();//国网订单号
            if(null!=orderNo && !"".equals(orderNo)){
                JSONObject queryResult = queryOrderResultBoss(req.getBizorderid(),orderNo,custOrder.getCustid()+"", loginInfo);
                if (queryResult != null) {
                    String queryStatus = queryResult.getString("pay_status");
                    if("4".equals(queryStatus)){//支付失败
                        getDAO().executeSql("update biz_portal_order set gworderno=?,scanurl=?,subpayway=? where orderid = ?", "","","", custOrder.getId());
                    }else if("5".equals(queryStatus)) {//支付超时
                        getDAO().executeSql("update biz_portal_order set gworderno=?,scanurl=?,subpayway=? where orderid = ?", "", "", "", custOrder.getId());
                    }
                    resp.setStatus(queryStatus);

                    // 2022/12/02 如果支付状态是成功，更新订单状态 0[未支付],1[已支付]
                    if ("2".equals(queryStatus)) {
                        getDAO().executeSql("update biz_portal_order set pay_status = ? where orderid = ?", "1", custOrder.getId());
                    }
                }
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }

        return returnInfo;
    }

    /**
     * 贵州BOSS二维码支付确认接口
     *
     * @param req
     * @param resp
     * @return
     */
    public  ReturnInfo gWBossOrderCommitOld(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_REFUND)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经退款，无法再提交", req.getCustorderid()));
        }
        if(orderCommitLocked(req.getCustorderid())!=1){
            throw new BusinessException(String.format("CMMS:[%s]订单处理中，请稍等一分钟后再试", req.getCustorderid()));
        }
        //获取国网二维码订单信息
        String gwPayOrderInfo = unifyPayService.getGwCodePayOrderInfo(req.getCustorderid());
        if (StringUtils.isNotBlank(gwPayOrderInfo)) {
            BScanCPayResp customInfo = JSONObject.parseObject(gwPayOrderInfo,BScanCPayResp.class);
            com.maywide.core.util.BeanUtils.copyProperties(resp, customInfo);
        }
        try {
            if(req!=null&&req.getPoid()!=null&&req.getPoid()>0){
                resp.setPoid(req.getPoid());
            }
            bossOrderCommit(custOrder, resp, req.getCondition(), loginInfo);
            resp.setStatus("2");//支付成功

            // 查询异常记录并设置已读
            updateExceptionRecordReadStatus(req.getCustorderid());

        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return returnInfo;
    }

    /**
     * 提交订单
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public  ReturnInfo gWBossOrderCommit(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        // 1. 查询订单数据
        BizPortalOrder bizPortalOrderQuery = new BizPortalOrder();
        bizPortalOrderQuery.setId(Long.valueOf(req.getCustorderid()));
        Page<BizPortalOrder> bizPortalOrderPage = DAO.find(getOne(),bizPortalOrderQuery);
        List<BizPortalOrder> list = bizPortalOrderPage.getResult();
        if (list == null || list.size() == 0) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }

        // 2. 判断支付状态与提交状态：1【已支付】、0或null【未支付】
        BizPortalOrder bizPortalOrder = list.get(0);
        String payStatus = bizPortalOrder.getPayStatus();
        if ("1".equals(payStatus)) {

            // 设置支付状态
            resp.setPayStatus(payStatus);

            // 3. 如果已支付，则判断是否已提交：1【已提交】、0或null【未提交】
            String submitStatus = bizPortalOrder.getSubmitStatus();
            if ("1".equals(submitStatus)) {
                throw new BusinessException(String.format("CMMS:[%s]订单状态为已完成，请勿重复提交！", req.getCustorderid()));
            } else {
                try {
                    //获取国网二维码订单信息
                    String gwPayOrderInfo = unifyPayService.getGwCodePayOrderInfo(req.getCustorderid());
                    if (StringUtils.isNotBlank(gwPayOrderInfo)) {
                        BScanCPayResp customInfo = JSONObject.parseObject(gwPayOrderInfo,BScanCPayResp.class);
                        com.maywide.core.util.BeanUtils.copyProperties(resp, customInfo);
                    }
                    if(req.getPoid()!=null && req.getPoid()>0){
                        resp.setPoid(req.getPoid());
                    }

                    // boss提交订单
                    bossOrderCommit(getCustOrder(req.getCustorderid()), resp, req.getCondition(), loginInfo);

                    // 返回支付成功
                    resp.setStatus("2");

                    // 设置提交状态
                    resp.setSubmitStatus("1");

                    // 查询异常记录并设置已读
                    updateExceptionRecordReadStatus(req.getCustorderid());

                } catch (Exception e) {
                    throw new BusinessException(e.getMessage());
                }
            }
        } else {
            throw new BusinessException(String.format("CMMS:[%s]订单状态为未支付，请稍后更新状态后再提交！", req.getCustorderid()));
        }

        return returnInfo;
    }

    private Page getOne() {
        Page page = new Page();
        page.setPageSize(1);
        page.setPageNo(1);
        return page;
    }

    /**
     * 查询订单状态
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queryOrderStatus(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();

        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单号不能为空");

        BizPortalOrder bizPortalOrderQuery = new BizPortalOrder();
        bizPortalOrderQuery.setId(Long.valueOf(req.getCustorderid()));
        Page<BizPortalOrder> bizPortalOrderPage = DAO.find(getOne(),bizPortalOrderQuery);
        List<BizPortalOrder> list = bizPortalOrderPage.getResult();
        if (list == null || list.size() == 0) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }

        BizPortalOrder bizPortalOrder = list.get(0);

        // 设置订单状态
        resp.setPayStatus(bizPortalOrder.getPayStatus());
        resp.setSubmitStatus(bizPortalOrder.getSubmitStatus());
        resp.setStatus(bizPortalOrder.getStatus());
        resp.setPayway(bizPortalOrder.getSubpayway() != null ? bizPortalOrder.getSubpayway() : bizPortalOrder.getPayway());

        return returnInfo;
    }

    /**
     *
     * @param orderId
     */
    public void updateExceptionRecordReadStatus(String orderId) {
        // 查询异常记录并设置已读
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(BizExceptionRecord.class);
        detachedCriteria.add(Restrictions.eq("orderId", orderId));

        List<BizExceptionRecord> list = DAO.findPage(detachedCriteria, 1,  1);

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        try {
            getDAO().executeSql("update biz_exception_record set read_status = ? where id = ? ", "read", list.get(0).getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 订单确认
     *
     * @param custOrder
     * @param resp
     * @param condition
     * @param loginInfo
     */
    private void bossOrderCommit(CustOrder custOrder, BScanCPayResp resp, UnifyPayCondition condition, LoginInfo loginInfo) throws Exception {
        String biztype = condition.getBizType();
        String extra = condition.getExtra();
        log.info("=========start biz business======");
        try {
            QuePayOrderResp order = new QuePayOrderResp();
            order.setOrderNo(custOrder.getId().toString());
            order.setPaySerialNo(resp.getOrderNo());
            order.setPayway(resp.getPayway());
            order.setBossPaycode(resp.getPaycode());
            order.setTotalFee(Double.parseDouble(custOrder.getPortalOrder().getFees()));
            Object bizCustomInfo = resp.getBizCustomInfo();
            JSONObject bizCustomInfoJson = JSONObject.parseObject(JSON.toJSONString(bizCustomInfo));
            if (BizConstant.BizOpcode.BIZ_UP_NOTPAIDFEES.equals(custOrder.getOpcode())) {
                noticeService.bizConfirmaPay(order, extra);
            } else if (BizConstant.BizOpcode.BIZ_USER_NEW.equals(custOrder.getOpcode()) || BizConstant.BizOpcode.BIZ_FGUSER_NEW.equals(custOrder.getOpcode())) {
                noticeService.bizInstallCommit(order, extra, null, null, bizCustomInfoJson);
            } else if (BizConstant.BizOpcode.BIZ_PARTS_SALES.equals(custOrder.getOpcode())) {
                noticeService.bizSaleCommit(custOrder.getId().toString(), order, extra, null, null);
            } else if (BizConstant.BizOpcode.BIZ_CUSTADDR_CHG.equals(custOrder.getOpcode())) {
                noticeService.bizCustAddrCommit(custOrder.getId().toString(), order, null, null);
            } else if (BizConstant.M5gInterface.GW_DAMAGES_PAY.equals(custOrder.getOpcode())
                    ||BizConstant.M5gInterface.BIZ_FGB_MARKET.equals(custOrder.getOpcode())
                    ||BizConstant.M5gInterface.GW_CHANGE_CARD.equals(custOrder.getOpcode())
                    ||BizConstant.M5gInterface.BIZ_FGBPACK_ORDER.equals(custOrder.getOpcode())
                    ||BizConstant.PAY_BIZ_TYPE.ORDER.equals(biztype)
                    || BizConstant.BossInterfaceService.BIZ_FEEIN.equals(biztype)
                    || BizConstant.BizOpcode.BIZ_SALES_TERMINAL.equals(custOrder.getOpcode())
                    || BizConstant.BizOpcode.BIZ_CHL_RESTART.equals(custOrder.getOpcode())) {
                noticeService.orderCommit(order, extra, null, null, bizCustomInfoJson);
            } else if (BizConstant.PAY_BIZ_TYPE.CHG_DEV.equals(biztype)) {
                noticeService.changeDevice(order, extra, null, null);
            } else if (BizConstant.M5gInterface.BIZ_VPMN_BATCH_ACTIVATION.equals(custOrder.getOpcode())) {
                noticeService.bizGroupActivation(order, bizCustomInfoJson);
            } else {
                throw new BusinessException("传入的业务类型不正确！");
            }
        } catch (Exception e) {
            boolean f = false;

            log.error(String.format("=>[%s]订单确认异常:", custOrder.getId()), e);
            // 订单确认异常, 发起退款
            PayRecordLog recordLog = initPayRecordLog(custOrder.getPortalOrder().getPayid(), custOrder, PayRecordLog.ACTION_TYPE_REFUND);
            //二维码支付
            if("01".equals(custOrder.getPortalOrder().getSubpayway()) || "02".equals(custOrder.getPortalOrder().getSubpayway()) || "03".equals(custOrder.getPortalOrder().getSubpayway())){
                try {
                    // 请求BOSS接口回退
                    JSONObject reverse =null;
                    if(e.getMessage().contains("国网返回") && !e.getMessage().equals("服务调用异常")){
                        reverse = gwOrderRefund(getBizorderid(),resp!=null&resp.getPoid()!=null&&resp.getPoid()>0?resp.getPoid():null,custOrder, loginInfo,"SYSTEM");
                        if (reverse == null || StringUtils.equals(reverse.getString("refund_status"), "REFUND_FAIL")) {
                            throw new BusinessException(String.format("退款失败: %s", reverse.getString("errMsg")));
                        }
                        f = true;
                        //更多订单表 设置为退款状态 ：7
                        getDAO().executeSql("update biz_portal_order set status='7'  where orderid = ?", custOrder.getId());
                    }

                    // 回退成功
                    recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUND);
                    recordLog.setPaydate(new Date());
                    recordLog.setRedicetStr(JSON.toJSONString(reverse));
                    recordLog.setBizexmsg(e.getMessage());
                    updatePayRecordLog(recordLog);
                } catch (Exception ex) {
                    recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUNDFAIL);
                    recordLog.setPaydate(new Date());
                    recordLog.setBizexmsg(ex.getMessage());
                    updatePayRecordLog(recordLog);
                    throw new BusinessException(String.format("CMMS:订单确认发生异常: %s, 支付退款异常: %s", e.getMessage(), ex.getMessage()));
                }
            }else{//扫码支付
                try {
                    // 请求BOSS接口回退
                    ReverseResp reverse = reverse(resp.getOrderNo(), loginInfo, getBizorderid());
                    if (reverse == null || StringUtils.equals(reverse.getStatus(), ReverseResp.STATUS_FAIL)) {
                        throw new BusinessException(String.format("退款失败: %s", reverse.getMessage()));
                    }
                    // 回退成功
                    recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUND);
                    recordLog.setPaydate(new Date());
                    recordLog.setRedicetStr(JSON.toJSONString(reverse));
                    recordLog.setBizexmsg(e.getMessage());
                    updatePayRecordLog(recordLog);
                } catch (Exception ex) {
                    recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUNDFAIL);
                    recordLog.setPaydate(new Date());
                    recordLog.setBizexmsg(ex.getMessage());
                    updatePayRecordLog(recordLog);
                    throw new BusinessException(String.format("CMMS:订单确认发生异常: %s, 支付退款异常: %s", e.getMessage(), ex.getMessage()));
                }
            }
            if(f) {
                throw new BusinessException(String.format("CMMS:订单确认发生异常, 钱已退款: %s", e.getMessage()));
            }else {
                throw new BusinessException(String.format("CMMS:订单确认发生异常: %s", e.getMessage()));
            }
        }
    }

    /**
     * @param recordLog
     * @throws Exception
     */
    private void updatePayRecordLog(PayRecordLog recordLog) throws Exception {
        getDAO().saveOrUpdate(recordLog);
    }

    private String getAmount(BizPortalOrder portalOrder) {
        String fees = portalOrder.getFees();
        BigDecimal feesYuan = new BigDecimal(fees);
        BigDecimal feesFen = feesYuan.multiply(new BigDecimal("100"));

        return feesFen.intValue() + "";
    }

    private PayRecordLog initPayRecordLog(Long orderid, CustOrder custOrder, String actionType) throws Exception {
        CheckUtils.checkEmpty(custOrder.getPortalOrder().getFees(), "CMMS:支付金额为空.");

        PayRecordLog recordLog = new PayRecordLog();
        recordLog.setActionType(actionType);
        recordLog.setOrderid(orderid);
        recordLog.setCustorderid(custOrder.getId());
        recordLog.setPaydate(new Date());
        recordLog.setPayresult(PayRecordLog.PAY_RESULT_INIT);
        recordLog.setFees(custOrder.getPortalOrder().getFees());
        getDAO().save(recordLog);
        return recordLog;
    }

    private CustOrder getCustOrder(String custorderid) throws Exception {
        CustOrder custOrder = new CustOrder();
        custOrder.setId(Long.valueOf(custorderid));
        List<CustOrder> list = getDAO().find(custOrder);
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    public BScanCPayResp bScanCPay(BScanCPayReq req, LoginInfo loginInfo) throws Exception {
        CheckUtils.checkEmpty(req.getPayCode(), "CMMS: 支付场景为空.");
        CheckUtils.checkNull(req.getPayInfo(), "CMMS: 支付信息为空");
        String payCode = req.getPayCode();
        if (StringUtils.equals(payCode, BScanCPayReq.PAYCODE_REFUND)) {
            // 退款
            CheckUtils.checkEmpty(req.getPayInfo().getOrderNo(), "CMMS:退款时支付订单号为空.");
        } else if (StringUtils.equals(payCode, BScanCPayReq.PAYCODE_PAY)) {
            // 支付
            CheckUtils.checkEmpty(req.getPayInfo().getMchntOrderNo(), "CMMS:支付流失号为空.");
            CheckUtils.checkEmpty(req.getPayInfo().getAmount(), "CMMS:支付金额为空.");
            CheckUtils.checkEmpty(req.getPayInfo().getPayType(), "CMMS:支付方式为空.");
            switch (req.getPayInfo().getPayType()) {
                case PayInfo.PAY_TYPE_WECHAT:
                    CheckUtils.checkEmpty(req.getPayInfo().getAuthCode(), "CMMS:微信支付条码为空.");
                    break;
                case PayInfo.PAY_TYPE_ALIPAY:
                    CheckUtils.checkEmpty(req.getPayInfo().getAuthCode(), "CMMS:支付宝支付条码为空.");
                    break;
                default:
                    throw new BusinessException(String.format("CMMS:[%s]支付方式不存在.", req.getPayInfo().getPayType()));
            }
        } else {
            throw new BusinessException(String.format("CMMS:[%s]支付场景不存在.", payCode));
        }

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("out_trade_no", req.getPayInfo().getMchntOrderNo());
        bossreq.put("authCode", req.getPayInfo().getAuthCode());
        bossreq.put("payType", req.getPayInfo().getPayType());
        bossreq.put("amount", req.getPayInfo().getAmount());
        bossreq.put("custInfo", req.getCustInfo());
        bossreq.put("bizCustomInfo", req.getBizCustomInfo());
        String output = getBossHttpInfOutput(req.getBizorderid(), BizConstant.BossInterfaceService.B_SCAN_C_PAY, bossreq, loginInfo);
        BScanCPayResp bScanCPayResp = JSON.parseObject(output, BScanCPayResp.class);

        return bScanCPayResp;
    }

    /**
     * 二维码支付
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public BScanCPayResp cScanBPay(String bizorderid,BizQdElecpayReq reqBoss, LoginInfo loginInfo) throws Exception {
        CheckUtils.checkEmpty(reqBoss.getSubpayway(), "CMMS: 支付场景为空.");
        CheckUtils.checkNull(reqBoss.getPayway(), "CMMS: 支付信息为空");

        String bossRespOutput = getBossHttpInfOutput(bizorderid,BizConstant.BossInterfaceService.BIZ_QD_ELECPAY,reqBoss, loginInfo);
        if(StringUtils.isBlank(bossRespOutput)) {

            throw new BusinessException("CMMS:支付二维码生成失败.");

        }
        GwOrderPayBo orderPayBo = JSON.parseObject(bossRespOutput,GwOrderPayBo.class);
        BScanCPayResp payResp = new BScanCPayResp();
        payResp.setPaycode(reqBoss.getSubpayway());
        payResp.setPayway(reqBoss.getPayway());
        payResp.setOrderNo(orderPayBo.getOrder_no());
        payResp.setOrderAmount(orderPayBo.getOrder_amount());
        payResp.setScanUrl(orderPayBo.getScan_url());
        payResp.setStatus(orderPayBo.getPay_status());
        GwPayOrderInfoBo gwPayOrderInfoBo = new GwPayOrderInfoBo();
        BeanUtils.copyProperties(gwPayOrderInfoBo, orderPayBo);
        gwPayOrderInfoBo.setBossorderno(reqBoss.getPayorderno());
        payResp.setBizCustomInfo(JSONObject.parseObject(JSON.toJSONString(gwPayOrderInfoBo)));
        return  payResp;
    }

    /**
     * 二维码支付查询
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public JSONObject queryOrderResultBoss(String bizorderid,String orderNo,String custid, LoginInfo loginInfo) throws Exception {
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("payorderno", orderNo);
        bossreq.put("custid", custid);

        String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.QUE_QD_PAY, bossreq, loginInfo);
        JSONObject resultResp = JSONObject.parseObject(output);
        return resultResp;
    }

    /**
     * 二维码支付撤销
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public JSONObject gWCancelOrder(String bizorderid,String orderNo, LoginInfo loginInfo) throws Exception {
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("orderNo", orderNo);
        String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.GW_CANCELORDER, bossreq, loginInfo);
        JSONObject resultResp = JSONObject.parseObject(output);
        return resultResp;
    }


    /**
     * 二维码支付退款
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public JSONObject gwOrderRefund(String bizorderid,Long poid,CustOrder custOrder , LoginInfo loginInfo,String opcode) throws Exception {
        if(poid!=null&&poid>0) {
            m5gCancelMobileAndSimOccupy(custOrder, poid, loginInfo);
        }
        BizPayRefundLog bizPayRefundLog = new  BizPayRefundLog();
        bizPayRefundLog.setCustname(custOrder.getName());
        bizPayRefundLog.setCustid(custOrder.getCustid()+"");
        bizPayRefundLog.setFee(custOrder.getPortalOrder().getFees());
        bizPayRefundLog.setOrderno(custOrder.getId()+"");
        if(poid!=null&&poid>0) {
            bizPayRefundLog.setPoid(poid);
        }
        bizPayRefundLog.setOpcode(opcode);
        bizPayRefundLog.setReqtime(new Date());
        bizPayRefundLog.setStatus(BizPayRefundLog.REFUND_NOT);//0,未退款,1:退款中;2,退款成功;3,退款失败
        getDAO().save(bizPayRefundLog);
        try {
            if(poid!=null&&poid>0) {
                getDAO().executeSql("update biz_process_order  set status='3' where recid=? ",poid);
            }
            // 获得国网订单信息,payorderno
            String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(custOrder.getId()+"");
            String bossorderno = JSONObject.parseObject(gwPayOrderInfo).get("bossorderno").toString();
            if (StringUtils.isNotBlank(bossorderno)) {
                bizPayRefundLog.setPayorderno(bossorderno);
            }
            Map<String, Object> bossreq = new HashMap<>();
            bossreq.put("payorderno", bossorderno);
            bossreq.put("custid", custOrder.getCustid());
            String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.GW_QDPAY_REFUND, bossreq, loginInfo);
            JSONObject resultResp = JSONObject.parseObject(output);

            bizPayRefundLog.setStatus(BizPayRefundLog.REFUND_ING);
            if(resultResp!=null) {
                if(StringUtils.equals(resultResp.getString("refund_status"), "REFUND_FAIL")){
                    bizPayRefundLog.setStatus(BizPayRefundLog.REFUND_FILE);

                }else if(StringUtils.equals(resultResp.getString("refund_status"), "REFUND_SUCCESS")){
                    bizPayRefundLog.setStatus(BizPayRefundLog.REFUND_END);

                }else {
                    bizPayRefundLog.setStatus(BizPayRefundLog.REFUND_ING);
                }
            }
            bizPayRefundLog.setResptime(new Date());
            bizPayRefundLog.setRespdesc(output);
            getDAO().saveOrUpdate(bizPayRefundLog);
            return resultResp;
        }catch (Exception e){

            bizPayRefundLog.setStatus(BizPayRefundLog.REFUND_FILE);
            bizPayRefundLog.setResptime(new Date());
            bizPayRefundLog.setRespdesc(e.getMessage());
            getDAO().saveOrUpdate(bizPayRefundLog);
            throw e;
        }
    }


    public ReverseResp reverse(String orderNo, LoginInfo loginInfo, String bizorderid) throws Exception {
        CheckUtils.checkEmpty(orderNo, "CMMS:BOSS支付订单号为空");

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("orderNo", orderNo);
        String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.REVERSE, bossreq, loginInfo);
        ReverseResp resp = JSON.parseObject(output, ReverseResp.class);

        return resp;
    }
    public  ReturnInfo gwOrderRefund(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        if (StringUtils.equals(custOrder.getPortalOrder().getStatus(), BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已提交，无法退款进行", req.getCustorderid()));
        }
        try {
            JSONObject reverse = this.gwOrderRefund(getBizorderid(),req!=null&&req.getPoid()!=null&&req.getPoid()>0?req.getPoid():null,custOrder,loginInfo,loginInfo.getLoginname());
            if (reverse == null || StringUtils.equals(reverse.getString("refund_status"), "REFUND_FAIL")) {
                throw new BusinessException(String.format("退款失败: %s", reverse.getString("errMsg")));
            }
            //更多订单表 设置为退款状态 ：7
            getDAO().executeSql("update biz_portal_order set status='7'  where orderid = ?",  custOrder.getId());
            resp.setStatus("2");
        }catch (Exception e){
            if(e.getMessage().contains("已经退款")){
                getDAO().executeSql("update biz_portal_order set status='7'  where orderid = ?",  custOrder.getId());
            }
            throw new BusinessException(String.format("CMMS:订单[%s]退款失败: %s",  req.getCustorderid(),e.getMessage()));

        }

        return returnInfo;
    }

    public  ReturnInfo qryGwRefundOrder(BScanCToPayByBossReq req, QryGwRefundOrderResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        BizPayRefundLog bizPayRefundLog = new  BizPayRefundLog();
        bizPayRefundLog.setOrderno(req.getCustorderid());
        List<BizPayRefundLog> list =  getDAO().find(bizPayRefundLog);
        getDAO().clear();

        List<BizPayRefundLogBo> bizPayRefundLogBos = getBizPayRefundLogBos(req, list);

        resp.setRefundLogs(bizPayRefundLogBos);
        return returnInfo;
    }

    private List<BizPayRefundLogBo> getBizPayRefundLogBos(BScanCToPayByBossReq req, List<BizPayRefundLog> list) throws Exception {
        List<BizPayRefundLogBo> bizPayRefundLogBos = new ArrayList<>();
        for (BizPayRefundLog payRefundLog : list) {
            BizPayRefundLogBo bizPayRefundLogBo = new BizPayRefundLogBo();
            com.maywide.core.util.BeanUtils.copyProperties(bizPayRefundLogBo, payRefundLog);
            bizPayRefundLogBos.add(bizPayRefundLogBo);
        }

        // 查询支付记录日志
        PayRecordLog payRecordQuery = new PayRecordLog();
        payRecordQuery.setCustorderid(Long.valueOf(req.getCustorderid()));
        payRecordQuery.setActionType(ACTION_TYPE_REFUND);
        List<PayRecordLog> payRecordLogs = getDAO().find(payRecordQuery);

        // 将支付记录中的异常消息映射到退款日志
        Map<String, String> recordLogMap = payRecordLogs.stream()
                .collect(Collectors.toMap(log -> String.valueOf(log.getCustorderid()), PayRecordLog::getBizexmsg));

        for (BizPayRefundLogBo refundLogBo : bizPayRefundLogBos) {
            String bizexmsg = recordLogMap.get(refundLogBo.getOrderno());
            if (bizexmsg != null) {
                refundLogBo.setBizexmsg(bizexmsg);
            }
        }
        return bizPayRefundLogBos;
    }

    /**
     * 释放手机号和SIM卡预站
     */

    private void m5gCancelMobileAndSimOccupy( CustOrder custOrder , Long poid, LoginInfo loginInfo){
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (BizConstant.BizOpcode.BIZ_USER_NEW.equals(custOrder.getOpcode()) || BizConstant.BizOpcode.BIZ_FGUSER_NEW.equals(custOrder.getOpcode())) {
                            // 获得国网订单信息,payorderno
                            String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(custOrder.getId() + "");
                            String servid = JSONObject.parseObject(gwPayOrderInfo).get("servid").toString();
                            m5gCancelMobileOccupy(custOrder, poid, servid, loginInfo);
                            m5gCancelSimOccupy(custOrder, poid, servid, loginInfo);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }).start();

    }
    /**
     * 手机号码释放预占接口
     */
    private void m5gCancelMobileOccupy(CustOrder custOrder ,Long poid, String servid,LoginInfo loginInfo)  {
        try {
            if (poid == null || poid <= 0) {
                return;
            }
            if (BizConstant.BizOpcode.BIZ_USER_NEW.equals(custOrder.getOpcode()) || BizConstant.BizOpcode.BIZ_FGUSER_NEW.equals(custOrder.getOpcode())) {
                String sql = "select * from biz_process_order_detail t where t.poid=? and t.cpcode='M5GNO' ";

                List<PoidDeatailBo> list =  getDAO().find(sql,PoidDeatailBo.class,poid);
                if(list==null||list.size()<=0){
                    return ;
                }
                PoidDeatailBo poidDeatailBo = list.get(0);
                if(StringUtils.isBlank(poidDeatailBo.getData())){
                    return ;
                }
                String resNum = JSONObject.parseObject(poidDeatailBo.getData()).get("resNum").toString();

                ReturnInfo returnInfo = initReturnInfo();
                Map<String, String> bossreq = new HashMap<>();
                bossreq.put("serialno", String.valueOf(UUID.randomUUID()));
                bossreq.put("cust_id", custOrder.getCustid() + "");
                bossreq.put("serv_id",servid);
                bossreq.put("access_num", resNum);
                bossreq.put("city", custOrder.getCity());
                String output = getBossHttpInfOutput(getBizorderid(), BizConstant.BossInterfaceService.GW_RC_CANCELPREOCCUPYNUMBER, bossreq, loginInfo);

            }
        }catch (Exception e) {
            e.printStackTrace();

        }

    }

    /**
     * SIM释放预占接口
     */
    private void m5gCancelSimOccupy(CustOrder custOrder ,Long poid, String servid,LoginInfo loginInfo) {
        try {
            if (poid == null || poid <= 0) {
                return;
            }
            if (BizConstant.BizOpcode.BIZ_USER_NEW.equals(custOrder.getOpcode()) || BizConstant.BizOpcode.BIZ_FGUSER_NEW.equals(custOrder.getOpcode())) {
                String sql = "select * from biz_process_order_detail t where t.poid=? and t.cpcode='M5GNOBS' ";
               List<PoidDeatailBo> list =  getDAO().find(sql,PoidDeatailBo.class,poid);
               if(list==null||list.size()<=0){
                   return ;
               }
                PoidDeatailBo poidDeatailBo = list.get(0);

                if(StringUtils.isBlank(poidDeatailBo.getData())){
                    return ;
                }
                String sim = JSONObject.parseObject(poidDeatailBo.getData()).get("sim").toString();

                ReturnInfo returnInfo = initReturnInfo();
                Map<String, String> bossreq = new HashMap<>();
                bossreq.put("serialno", String.valueOf(UUID.randomUUID()));
                bossreq.put("cust_id", custOrder.getCustid() + "");
                bossreq.put("serv_id", servid);
                bossreq.put("icc_id", sim);
                bossreq.put("city", custOrder.getCity());

                String output = getBossHttpInfOutput(getBizorderid(), BizConstant.BossInterfaceService.GW_RC_CANCELPREOCCUPYSIMCARD, bossreq, loginInfo);
            }
        }catch (Exception e) {
            e.printStackTrace();

        }
    }
    public  ReturnInfo qryRefundStatus(BScanCToPayByBossReq req,BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        BizPayRefundLog bizPayRefundLog = new  BizPayRefundLog();
        bizPayRefundLog.setOrderno(req.getCustorderid());
        List<BizPayRefundLog> list =  getDAO().find(bizPayRefundLog);
        if(list!=null&&list.size()>0){

            if(BizPayRefundLog.REFUND_END.equals(list.get(0))){
                resp.setStatus(BizPayRefundLog.REFUND_END);
                return returnInfo;
            }

            Map<String, Object> bossreq = new HashMap<>();
            bossreq.put("payorderno", list.get(0).getPayorderno());
            bossreq.put("custid", list.get(0).getCustid());
            String output = getBossHttpInfOutput(getBizorderid(), BizConstant.BossInterfaceService.QUE_FGB_REFUND_RESULT, bossreq, loginInfo);
            JSONObject resultResp = JSONObject.parseObject(output);
            if(resultResp!=null) {
                if(StringUtils.equals(resultResp.getString("refund_status"), "REFUND_FAIL")){
                    resp.setStatus(BizPayRefundLog.REFUND_FILE);
                }else if(StringUtils.equals(resultResp.getString("refund_status"), "REFUND_SUCCESS")){
                    resp.setStatus(BizPayRefundLog.REFUND_END);
                    getDAO().executeSql("update BIZ_PAYREFUND_LOG set status='2'  where orderno = ? and payorderno=? ",req.getCustorderid(),list.get(0).getPayorderno());
                }else {
                    resp.setStatus(BizPayRefundLog.REFUND_ING);
                }
            }else{
                resp.setStatus(BizPayRefundLog.REFUND_ING);
            }
        }else{
            resp.setStatus(BizPayRefundLog.REFUND_NOT);
        }

        return returnInfo;
    }


}
