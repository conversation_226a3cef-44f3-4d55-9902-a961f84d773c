package com.maywide.biz.inter.service;

import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.entity.Rule;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.service.RuleService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.constant.QueConstant.CommonNotice;
import com.maywide.biz.inter.pojo.addscreenshot.AddScreenshotReq;
import com.maywide.biz.inter.pojo.custordercardphoto.CustorderCardReq;
import com.maywide.biz.inter.pojo.custordercardphoto.CustorderCardResp;
import com.maywide.biz.inter.pojo.interOrderService.sendAcOrderToCust.PushPaperLessBossReq;
import com.maywide.biz.inter.pojo.interOrderService.sendAcOrderToCust.SendAcOrderToCustReq;
import com.maywide.biz.inter.pojo.interOrderService.sendAcOrderToCust.SendAcOrderToCustResp;
import com.maywide.biz.inter.pojo.quescreenshot.QueScreenshotReq;
import com.maywide.biz.market.entity.BizCustorderCard;
import com.maywide.biz.market.entity.BizCustorderPhoto;
import com.maywide.biz.market.entity.CustOrder;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class InterOrderService extends CommonService {

    @Autowired
    private RuleService ruleService;

    public ReturnInfo addScreenshot(AddScreenshotReq req) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, CommonNotice.LOGIN_OUT_NOTICE);

        CheckUtils.checkEmpty(req.getOrderid(), "订单号不能为空");
        CheckUtils.checkEmpty(req.getScreenshots(), "屏幕截图不能为空");

        List<Object> paramList = new ArrayList<Object>();
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE biz_custorder SET screenshots = ");
        if (req.isAppend()) {
            sql.append(" CASE WHEN screenshots IS NOT NULL AND LENGTH(TRIM( screenshots))>0 ");
            sql.append(" THEN CONCAT(screenshots,',', ? ) ELSE ? END ");
            paramList.add(req.getScreenshots());
            paramList.add(req.getScreenshots());
        } else {
            sql.append(" ? ");
            paramList.add(req.getScreenshots());
        }
        sql.append(" WHERE orderid = ? ");
        paramList.add(req.getOrderid());
        int result = getDAO().executeSql(sql.toString(), paramList.toArray());
        if (result <= 0) {
            throw new BusinessException("添加屏幕截图失败");
        }
        return returnInfo;
    }

    @SuppressWarnings("unchecked")
    public ReturnInfo queScreenshot(QueScreenshotReq req, ArrayList<String> resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, CommonNotice.LOGIN_OUT_NOTICE);

        CheckUtils.checkEmpty(req.getOrderid(), "订单号不能为空");

        String sql = "SELECT imagepath FROM biz_photo_list WHERE FIND_IN_SET(fileid ,(SELECT screenshots FROM biz_custorder WHERE orderid = ? )) ";
        List<String> list = getDAO().findObjectList(sql, req.getOrderid());
        if (list != null) {
            resp.addAll(list);
        }
        return returnInfo;
    }

    /**
     * 发送无纸化受理单pdf文件给客户
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo sendAcOrderToCust(SendAcOrderToCustReq req, SendAcOrderToCustResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        String ordercode = req.getOrdercode();
        String channel = req.getChannel();
        String serialno = req.getSerialno();
        String mobile = req.getMobile();

        // 规则判断是否有发送权限
        boolean sendAuth = false;
        Rule rule = ruleService.getCityRuleOrDefault("Send_Ac_To_Cust_Auth", loginInfo.getCity());
        if (rule != null) {
            String perMission = rule.getPerMission();
            String value = rule.getValue();
            if (StringUtils.equalsIgnoreCase("Y", perMission) && StringUtils.isNotBlank(value)
                    && StringUtils.contains(value, loginInfo.getCity())) {
                sendAuth = true;
            }
        }

        if (!sendAuth) {
            throw new BusinessException("您所在地市没有推送无纸化受理单权限，请联系管理员！");
        }

        CheckUtils.checkEmpty(channel, "请选择发送渠道!");

        if (StringUtils.isBlank(req.getSerialno())) {
            CheckUtils.checkEmpty(ordercode, "订单号为空！请退出重新操作!");
            // 查询订单信息
            CustOrder order = new CustOrder();
            order.setOrdercode(ordercode);
            List<CustOrder> orderlist = getDAO().find(order);
            if (orderlist != null && !orderlist.isEmpty()) {
                order = orderlist.get(0);
                serialno = order.getBossserialno();
            } else {
                throw new BusinessException("未找到该订单！请退出重新操作!");
            }
        }

        CheckUtils.checkEmpty(serialno, "该订单流水号不存在，无法推送！");

        PushPaperLessBossReq bossReq = new PushPaperLessBossReq();
        bossReq.setSerialno(serialno);
        bossReq.setPushway(channel);
        if(StringUtils.isNotBlank(mobile)){
            bossReq.setMobile(mobile);
        }

        getBossHttpInfOutput(req.getBizorderid(), BizConstant.BossInterfaceService.PUSH_PAPER_LESS,
                bossReq, loginInfo);

        return returnInfo;
    }

    /**
     * 保存证件图片数据
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo saveCardPhoto(CustorderCardReq req, CustorderCardResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = getLoginInfo();
        resp.setCustorderCardId(saveCardPhoto(req));
        return returnInfo;
    }

    /**
     * 保存证件图片数据
     *
     * @param req
     * @return
     * @throws BusinessException
     */
    public Long saveCardPhoto(CustorderCardReq req) throws Exception {
        CheckUtils.checkEmpty(req.getCardType(), "证件类型未选择");
        CheckUtils.checkEmpty(req.getCardNo(), "证件号码未输入");
        CheckUtils.checkNull(req.getPaths(), "图片路径未传");
        if (req.getPaths().size() == 0) {
            throw new BusinessException("图片路径个数为0");
        }
        BizCustorderCard bizCustorderCard = new BizCustorderCard();
        BeanUtils.copyProperties(req, bizCustorderCard);
        getDAO().save(bizCustorderCard);

        for (String path : req.getPaths()) {
            BizCustorderPhoto bizCustorderPhoto = new BizCustorderPhoto();
            bizCustorderPhoto.setBizCustorderCardId(bizCustorderCard.getId());
            bizCustorderPhoto.setPath(path);
            getDAO().save(bizCustorderPhoto);
        }
        return bizCustorderCard.getId();
    }

    /**
     * 证件图片关联订单
     *
     * @param custorderid
     * @param bizCustorderCardid
     * @return
     * @throws Exception
     */
    public void updateCustOrderCardPhoto(Long custorderid, Long bizCustorderCardid) throws Exception {
        CheckUtils.checkNull(custorderid, "订单编号不能为空");
        CheckUtils.checkNull(bizCustorderCardid, "证件图片信息编号不能为空");
        BizCustorderCard bizCustorderCard = (BizCustorderCard) getDAO().find(BizCustorderCard.class, bizCustorderCardid);
        if (bizCustorderCard == null) {
            throw new BusinessException("证件信息不存在！");
        }
        CustOrder custOrder = (CustOrder) getDAO().find(CustOrder.class, custorderid);
        custOrder.setBizCustorderCardId(bizCustorderCardid);
        getDAO().saveOrUpdate(custOrder);
    }

	/*public ReturnInfo quePapersUploadStatus(){

	}*/
}
