package com.maywide.biz.pay.gzboss.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.inter.pojo.chargeFeeBook.QuePayOrderResp;
import com.maywide.biz.market.entity.BizPortalOrder;
import com.maywide.biz.market.entity.CustOrder;
import com.maywide.biz.pay.gzboss.pojo.*;
import com.maywide.biz.pay.unify.entity.PayRecordLog;
import com.maywide.biz.pay.unify.entity.PayRecordStartLog;
import com.maywide.biz.pay.unify.pojo.UnifyPayCondition;
import com.maywide.biz.pay.unify.service.PayNoticeService;
import com.maywide.biz.pay.unify.service.UnifyPayService;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 贵州BOSS支付
 *
 * <AUTHOR>
 */
@Service
public class GzPayToBossService extends CommonService {

    @Autowired
    private PayNoticeService noticeService;
    @Autowired
    private UnifyPayService unifyPayService;

    /**
     * 前端记录支付开始日志
     *
     * @param req
     * @param resp
     * @return
     */
    public  ReturnInfo recordStartLog(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        CheckUtils.checkEmpty(req.getPayType(), "CMMS:支付方式为空");
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        try {
            PayRecordStartLog recordLog = new PayRecordStartLog();
            recordLog.setActionType(req.getPayType());
            recordLog.setCustorderid(Long.valueOf(req.getCustorderid()));
            recordLog.setPaydate(new Date());
            getDAO().save(recordLog);
        } catch (Exception e) {
            // 记录失败
            throw new BusinessException(e.getMessage());
        }
        return returnInfo;
    }

    /**
     * 贵州BOSS支付接口，扫客户支付码
     *
     * @param req
     * @param resp
     * @return
     */
    public synchronized ReturnInfo bScanCToPayByBoss(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getPayType(), "CMMS:支付方式为空");
        CheckUtils.checkEmpty(req.getAuthCode(), "CMMS:支付条码为空");
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");
        CheckUtils.checkNull(req.getCondition(), "CMMS:支付条件为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }

        // 2. 获取流水号
        Long snowflakeOrderId = getSnowflakeOrderId();
        custOrder.getPortalOrder().setPayid(snowflakeOrderId);
        log.info("=>[{}]支付流失号: {}", req.getCustorderid(), snowflakeOrderId);
        // 3. 初始化支付记录
        PayRecordLog recordLog = initPayRecordLog(snowflakeOrderId, custOrder, PayRecordLog.ACTION_TYPE_PAY);
        // 4. 封装BOSS请求
        BScanCPayReq bossreq = new BScanCPayReq();
        bossreq.setBizorderid(req.getBizorderid());
        bossreq.setPayCode(BScanCPayReq.PAYCODE_PAY);
        bossreq.setPayInfo(new PayInfo());
        bossreq.setCustInfo(new HashMap<>());
        bossreq.getPayInfo().setPayType(req.getPayType());
        bossreq.getPayInfo().setMchntOrderNo(snowflakeOrderId.toString());
        bossreq.getPayInfo().setAuthCode(req.getAuthCode());
        bossreq.getPayInfo().setAmount(getAmount(custOrder.getPortalOrder()));
        bossreq.getCustInfo().put("custName", custOrder.getName());
        bossreq.getCustInfo().put("cust_id", custOrder.getCustid());
        bossreq.getCustInfo().put("custareaid", custOrder.getAreaid());
        bossreq.getCustInfo().put("city", custOrder.getCity());

        String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(req.getCustorderid());
        if (StringUtils.isNotBlank(gwPayOrderInfo)) {
            JSONObject customInfo = JSONObject.parseObject(gwPayOrderInfo);
            bossreq.setBizCustomInfo(customInfo);
            bossreq.getCustInfo().put("servid", customInfo.get("servid"));
        }

        // 5. 调用BOSS接口支付
        try {
            log.info("[{}]调用BOSS接口支付.", req.getCustorderid());
            BScanCPayResp payResp = bScanCPay(bossreq, loginInfo);
            if (payResp != null) {
                String tranStatus = payResp.getStatus();
                if (StringUtils.equals(tranStatus, BScanCPayResp.STATUS_FAIL)) {
                    throw new BusinessException("CMMS:支付失败.");
                }
            }

            // 将bossorderno补充到支付完成的数据里面
            JSONObject customInfo = payResp.getBizCustomInfo();
            if (customInfo != null && bossreq.getBizCustomInfo() != null) {
                JSONObject bizCustomInfo = (JSONObject) bossreq.getBizCustomInfo();
                customInfo.put("bossorderno", bizCustomInfo.getString("bossorderno"));
            }

            // 6. 支付成功，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYED);
            recordLog.setPaydate(new Date());
            recordLog.setRedicetStr(JSON.toJSONString(payResp));
            updatePayRecordLog(recordLog);

            // 更新biz_portal_order表的payid字段
            getDAO().executeSql("update biz_portal_order set payid = ? where orderid = ?",
                    custOrder.getPortalOrder().getPayid(), custOrder.getId());

            BeanUtils.copyProperties(resp, payResp);
        } catch (Exception e) {
            log.error(String.format("=>[%s]-[%s]支付失败", req.getCustorderid(), snowflakeOrderId), e);
            // 6. 支付失败，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYFAIL);
            recordLog.setPaydate(new Date());
            recordLog.setBizexmsg(e.getMessage());
            updatePayRecordLog(recordLog);
            throw new BusinessException(e.getMessage());
        }

        log.info("=>[{}]订单确认.", req.getCustorderid());
        // 7. 订单确认
        bossOrderCommit(custOrder, resp, req.getCondition(), loginInfo);

        return returnInfo;
    }


    /**
     * 贵州BOSS二维码支付接口，客户扫描二维码支付
     *
     * @param req
     * @param resp
     * @return
     */
    public synchronized ReturnInfo gwPayMent(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getPayType(), "CMMS:支付方式为空");
        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }
        //1.1查询是否已经生成二维码地址
        //已经生成二维码-且是未确认的时候
        if(null!=custOrder.getPortalOrder().getScanurl() && !"".equals(custOrder.getPortalOrder().getScanurl())){
            if(!StringUtils.equals(req.getPayType(),custOrder.getPortalOrder().getSubpayway())){
                //如果发生支付方式变更要进行撤销，再重新支付
                JSONObject queryResult = gWCancelOrder(req.getBizorderid(),custOrder.getPortalOrder().getGworderno(), loginInfo);
                if (queryResult != null) {
                    String queryStatus = queryResult.getString("status");
                    if (StringUtils.equals(queryStatus, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_OVERDUE)) {
                        // 更新biz_portal_order表的payid字段
                        getDAO().executeSql("update biz_portal_order set gworderno=?,scanurl=?,subpayway=? where orderid = ?", "","","", custOrder.getId());
                        resp.setStatus("3");//撤销，过期支付
                        return returnInfo;
                    }else{
                        throw new BusinessException(String.format("CMMS:撤销支付失败", req.getCustorderid()));
                    }
                }
            }else{
                resp.setStatus("1");//已经支付过了
                resp.setScanUrl(custOrder.getPortalOrder().getScanurl());
                return returnInfo;
            }
        }
        // 2. 获取流水号
        Long snowflakeOrderId = getSnowflakeOrderId();
        custOrder.getPortalOrder().setPayid(snowflakeOrderId);
        log.info("=>[{}]支付流失号: {}", req.getCustorderid(), snowflakeOrderId);
        // 3. 初始化支付记录
        PayRecordLog recordLog = initPayRecordLog(snowflakeOrderId, custOrder, PayRecordLog.ACTION_TYPE_PAY);
        // 4. 封装BOSS请求
        BScanCPayReq bossreq = new BScanCPayReq();
        bossreq.setBizorderid(req.getBizorderid());
        bossreq.setPayCode(BScanCPayReq.PAYCODE_PAY);
        bossreq.setPayInfo(new PayInfo());
        bossreq.setCustInfo(new HashMap<>());
        bossreq.getPayInfo().setPayType(req.getPayType());
        bossreq.getPayInfo().setMchntOrderNo(snowflakeOrderId.toString());
        bossreq.getPayInfo().setAuthCode(req.getAuthCode());
        bossreq.getPayInfo().setAmount(getAmount(custOrder.getPortalOrder()));
        bossreq.getCustInfo().put("custName", custOrder.getName());
        bossreq.getCustInfo().put("cust_id", custOrder.getCustid());
        bossreq.getCustInfo().put("custareaid", custOrder.getAreaid());
        bossreq.getCustInfo().put("city", custOrder.getCity());

        String gwPayOrderInfo = unifyPayService.getGwPayOrderInfo(req.getCustorderid());
        if (StringUtils.isNotBlank(gwPayOrderInfo)) {
            JSONObject customInfo = JSONObject.parseObject(gwPayOrderInfo);
            bossreq.setBizCustomInfo(customInfo);
            bossreq.getCustInfo().put("servid", customInfo.get("servid"));
        }

        // 5. 调用BOSS接口支付
        try {
            log.info("[{}]调用BOSS接口支付.", req.getCustorderid());
            BScanCPayResp payResp = cScanBPay(bossreq, loginInfo);
            if (payResp != null) {
                String tranStatus = payResp.getStatus();
                if (StringUtils.equals(tranStatus, BScanCPayResp.STATUS_FAIL)) {
                    throw new BusinessException("CMMS:支付二维码生成失败.");
                }
            }

            // 将bossorderno补充到支付完成的数据里面
            JSONObject customInfo = payResp.getBizCustomInfo();
            if (customInfo != null && bossreq.getBizCustomInfo() != null) {
                JSONObject bizCustomInfo = (JSONObject) bossreq.getBizCustomInfo();
                customInfo.put("bossorderno", bizCustomInfo.getString("bossorderno"));
            }

            // 6. 支付成功，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYED);
            recordLog.setPaydate(new Date());
            recordLog.setRedicetStr(JSON.toJSONString(payResp));
            updatePayRecordLog(recordLog);

            // 更新biz_portal_order表的payid字段
            getDAO().executeSql("update biz_portal_order set payid = ?,gworderno=?,scanurl=?,subpayway=? where orderid = ?",
                    custOrder.getPortalOrder().getPayid(),payResp.getOrderNo(),payResp.getScanUrl(),req.getPayType(), custOrder.getId());
            com.maywide.core.util.BeanUtils.copyProperties(resp, payResp);
            //保存国网的支付成功信息
            saveOrderSource(Long.valueOf(req.getCustorderid()), BizConstant.SourceCode.GW_CODE_PAY_ORDERINFO, JSON.toJSONString(payResp));
        } catch (Exception e) {
            log.error(String.format("=>[%s]-[%s]支付失败", req.getCustorderid(), snowflakeOrderId), e);
            // 6. 支付失败，更新支付记录
            recordLog.setPayresult(PayRecordLog.PAY_RESULT_PAYFAIL);
            recordLog.setPaydate(new Date());
            recordLog.setBizexmsg(e.getMessage());
            updatePayRecordLog(recordLog);
            throw new BusinessException(e.getMessage());
        }

        return returnInfo;
    }

    /**
     * 查询订单支付方式
     *
     * @param req
     * @param resp
     * @return
     */
    public  ReturnInfo queryPayWay(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }
        try {
            //支付方式
            resp.setPayway(custOrder.getPortalOrder().getSubpayway());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }

        return returnInfo;
    }


    /**
     * 贵州BOSS二维码支付查询接口
     *
     * @param req
     * @param resp
     * @return
     */
    public  ReturnInfo queryOrderResult(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }

        if(StringUtils.isBlank(custOrder.getPortalOrder().getScanurl())){
            resp.setStatus("0");
            return returnInfo;
        }
        try {
            String orderNo= custOrder.getPortalOrder().getGworderno();//国网订单号
            if(null!=orderNo && !"".equals(orderNo)){
                JSONObject queryResult = queryOrderResultBoss(req.getBizorderid(),orderNo, loginInfo);
                if (queryResult != null) {
                    String queryStatus = queryResult.getString("status");
                    if("4".equals(queryStatus)){//支付失败
                        getDAO().executeSql("update biz_portal_order set gworderno=?,scanurl=?,subpayway=? where orderid = ?", "","","", custOrder.getId());
                    }else if("5".equals(queryStatus)) {//支付超时
                        getDAO().executeSql("update biz_portal_order set gworderno=?,scanurl=?,subpayway=? where orderid = ?", "", "", "", custOrder.getId());
                    }
                    resp.setStatus(queryStatus);
                }
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }

        return returnInfo;
    }

    /**
     * 贵州BOSS二维码支付确认接口
     *
     * @param req
     * @param resp
     * @return
     */
    public synchronized ReturnInfo gWBossOrderCommit(BScanCToPayByBossReq req, BScanCPayResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkEmpty(req.getCustorderid(), "CMMS:支付订单为空");

        // 1. 查询订单数据
        log.info("=>[{}]查询订单数据.", req.getCustorderid());
        CustOrder custOrder = getCustOrder(req.getCustorderid());
        if (custOrder == null || custOrder.getPortalOrder() == null) {
            throw new BusinessException(String.format("CMMS:[%s]支付订单不存在", req.getCustorderid()));
        }
        String status = custOrder.getPortalOrder().getStatus();
        if (StringUtils.equals(status, BizConstant.PORTAL_ORDER_STATUS.PORTAL_ORDER_STATUS_PAYED)) {
            throw new BusinessException(String.format("CMMS:[%s]订单已经支付，无需再支付", req.getCustorderid()));
        }
        //获取国网二维码订单信息
        String gwPayOrderInfo = unifyPayService.getGwCodePayOrderInfo(req.getCustorderid());
        if (StringUtils.isNotBlank(gwPayOrderInfo)) {
            BScanCPayResp customInfo = JSONObject.parseObject(gwPayOrderInfo,BScanCPayResp.class);
            com.maywide.core.util.BeanUtils.copyProperties(resp, customInfo);
        }
        try {
            bossOrderCommit(custOrder, resp, req.getCondition(), loginInfo);
            resp.setStatus("2");//支付成功
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return returnInfo;
    }


    /**
     * 订单确认
     *
     * @param custOrder
     * @param resp
     * @param condition
     * @param loginInfo
     */
    private void bossOrderCommit(CustOrder custOrder, BScanCPayResp resp, UnifyPayCondition condition, LoginInfo loginInfo) throws Exception {
        String biztype = condition.getBizType();
        String extra = condition.getExtra();
        log.info("=========start biz business======");
        try {
            QuePayOrderResp order = new QuePayOrderResp();
            order.setOrderNo(custOrder.getId().toString());
            order.setPaySerialNo(resp.getOrderNo());
            order.setPayway(resp.getPayway());
            order.setBossPaycode(resp.getPaycode());
            order.setTotalFee(Double.parseDouble(custOrder.getPortalOrder().getFees()));
            Object bizCustomInfo = resp.getBizCustomInfo();
            JSONObject bizCustomInfoJson = JSONObject.parseObject(JSON.toJSONString(bizCustomInfo));
            if (BizConstant.BizOpcode.BIZ_UP_NOTPAIDFEES.equals(custOrder.getOpcode())) {
                noticeService.bizConfirmaPay(order, extra);
            } else if (BizConstant.BizOpcode.BIZ_USER_NEW.equals(custOrder.getOpcode())) {
                noticeService.bizInstallCommit(order, extra, null, null, bizCustomInfoJson);
            } else if (BizConstant.BizOpcode.BIZ_PARTS_SALES.equals(custOrder.getOpcode())) {
                noticeService.bizSaleCommit(custOrder.getId().toString(), order, extra, null, null);
            } else if (BizConstant.BizOpcode.BIZ_CUSTADDR_CHG.equals(custOrder.getOpcode())) {
                noticeService.bizCustAddrCommit(custOrder.getId().toString(), order, null, null);
            } else if (BizConstant.PAY_BIZ_TYPE.ORDER.equals(biztype)
                    || BizConstant.BossInterfaceService.BIZ_FEEIN.equals(biztype)
                    || BizConstant.BizOpcode.BIZ_SALES_TERMINAL.equals(custOrder.getOpcode())
                    || BizConstant.BizOpcode.BIZ_CHL_RESTART.equals(custOrder.getOpcode())) {
                noticeService.orderCommit(order, extra, null, null, bizCustomInfoJson);
            } else if (BizConstant.PAY_BIZ_TYPE.CHG_DEV.equals(biztype)) {
                noticeService.changeDevice(order, extra, null, null);
            } else {
                throw new BusinessException("传入的业务类型不正确！");
            }
        } catch (Exception e) {
            log.error(String.format("=>[%s]订单确认异常:", custOrder.getId()), e);
            // 订单确认异常, 发起退款
            PayRecordLog recordLog = initPayRecordLog(custOrder.getPortalOrder().getPayid(), custOrder, PayRecordLog.ACTION_TYPE_REFUND);
            //二维码支付
            if("03".equals(custOrder.getPortalOrder().getSubpayway()) || "04".equals(custOrder.getPortalOrder().getSubpayway())){
                try {
                    // 请求BOSS接口回退
                    JSONObject reverse =null;
                    if(e.getMessage().contains("国网返回") && !e.getMessage().equals("服务调用异常")){
                        reverse = gwOrderRefund(getBizorderid(),resp.getOrderNo(),resp.getBizCustomInfo().getString("subject"),resp.getOrderAmount(), loginInfo);
                        if (reverse == null || StringUtils.equals(reverse.getString("status"), ReverseResp.STATUS_FAIL)) {
                            throw new BusinessException(String.format("退款失败: %s", reverse.getString("errMsg")));
                        }
                        //更多订单表 重新进行支付
                        getDAO().executeSql("update biz_portal_order set gworderno=?,scanurl=?,subpayway=? where orderid = ?", "","","", custOrder.getId());
                    }
                    // 回退成功
                    recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUND);
                    recordLog.setPaydate(new Date());
                    recordLog.setRedicetStr(JSON.toJSONString(reverse));
                    recordLog.setBizexmsg(e.getMessage());
                    updatePayRecordLog(recordLog);
                } catch (Exception ex) {
                    recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUNDFAIL);
                    recordLog.setPaydate(new Date());
                    recordLog.setBizexmsg(ex.getMessage());
                    updatePayRecordLog(recordLog);
                    throw new BusinessException(String.format("CMMS:订单确认发生异常: %s, 支付退款异常: %s", e.getMessage(), ex.getMessage()));
                }
            }else{//扫码支付
                try {
                    // 请求BOSS接口回退
                    ReverseResp reverse = reverse(resp.getOrderNo(), loginInfo, getBizorderid());
                    if (reverse == null || StringUtils.equals(reverse.getStatus(), ReverseResp.STATUS_FAIL)) {
                        throw new BusinessException(String.format("退款失败: %s", reverse.getMessage()));
                    }
                    // 回退成功
                    recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUND);
                    recordLog.setPaydate(new Date());
                    recordLog.setRedicetStr(JSON.toJSONString(reverse));
                    recordLog.setBizexmsg(e.getMessage());
                    updatePayRecordLog(recordLog);
                } catch (Exception ex) {
                    recordLog.setPayresult(PayRecordLog.PAY_RESULT_REFUNDFAIL);
                    recordLog.setPaydate(new Date());
                    recordLog.setBizexmsg(ex.getMessage());
                    updatePayRecordLog(recordLog);
                    throw new BusinessException(String.format("CMMS:订单确认发生异常: %s, 支付退款异常: %s", e.getMessage(), ex.getMessage()));
                }
            }
            throw new BusinessException(String.format("CMMS:订单确认发生异常, 钱已退款: %s", e.getMessage()));
        }
    }

    /**
     * @param recordLog
     * @throws Exception
     */
    private void updatePayRecordLog(PayRecordLog recordLog) throws Exception {
        getDAO().saveOrUpdate(recordLog);
    }

    private String getAmount(BizPortalOrder portalOrder) {
        String fees = portalOrder.getFees();
        BigDecimal feesYuan = new BigDecimal(fees);
        BigDecimal feesFen = feesYuan.multiply(new BigDecimal("100"));

        return feesFen.intValue() + "";
    }

    private PayRecordLog initPayRecordLog(Long orderid, CustOrder custOrder, String actionType) throws Exception {
        CheckUtils.checkEmpty(custOrder.getPortalOrder().getFees(), "CMMS:支付金额为空.");

        PayRecordLog recordLog = new PayRecordLog();
        recordLog.setActionType(actionType);
        recordLog.setOrderid(orderid);
        recordLog.setCustorderid(custOrder.getId());
        recordLog.setPaydate(new Date());
        recordLog.setPayresult(PayRecordLog.PAY_RESULT_INIT);
        recordLog.setFees(custOrder.getPortalOrder().getFees());
        getDAO().save(recordLog);
        return recordLog;
    }

    private CustOrder getCustOrder(String custorderid) throws Exception {
        CustOrder custOrder = new CustOrder();
        custOrder.setId(Long.valueOf(custorderid));
        List<CustOrder> list = getDAO().find(custOrder);
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    public BScanCPayResp bScanCPay(BScanCPayReq req, LoginInfo loginInfo) throws Exception {
        CheckUtils.checkEmpty(req.getPayCode(), "CMMS: 支付场景为空.");
        CheckUtils.checkNull(req.getPayInfo(), "CMMS: 支付信息为空");
        String payCode = req.getPayCode();
        if (StringUtils.equals(payCode, BScanCPayReq.PAYCODE_REFUND)) {
            // 退款
            CheckUtils.checkEmpty(req.getPayInfo().getOrderNo(), "CMMS:退款时支付订单号为空.");
        } else if (StringUtils.equals(payCode, BScanCPayReq.PAYCODE_PAY)) {
            // 支付
            CheckUtils.checkEmpty(req.getPayInfo().getMchntOrderNo(), "CMMS:支付流失号为空.");
            CheckUtils.checkEmpty(req.getPayInfo().getAmount(), "CMMS:支付金额为空.");
            CheckUtils.checkEmpty(req.getPayInfo().getPayType(), "CMMS:支付方式为空.");
            switch (req.getPayInfo().getPayType()) {
                case PayInfo.PAY_TYPE_WECHAT:
                    CheckUtils.checkEmpty(req.getPayInfo().getAuthCode(), "CMMS:微信支付条码为空.");
                    break;
                case PayInfo.PAY_TYPE_ALIPAY:
                    CheckUtils.checkEmpty(req.getPayInfo().getAuthCode(), "CMMS:支付宝支付条码为空.");
                    break;
                default:
                    throw new BusinessException(String.format("CMMS:[%s]支付方式不存在.", req.getPayInfo().getPayType()));
            }
        } else {
            throw new BusinessException(String.format("CMMS:[%s]支付场景不存在.", payCode));
        }

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("out_trade_no", req.getPayInfo().getMchntOrderNo());
        bossreq.put("authCode", req.getPayInfo().getAuthCode());
        bossreq.put("payType", req.getPayInfo().getPayType());
        bossreq.put("amount", req.getPayInfo().getAmount());
        bossreq.put("custInfo", req.getCustInfo());
        bossreq.put("bizCustomInfo", req.getBizCustomInfo());
        String output = getBossHttpInfOutput(req.getBizorderid(), BizConstant.BossInterfaceService.B_SCAN_C_PAY, bossreq, loginInfo);
        BScanCPayResp bScanCPayResp = JSON.parseObject(output, BScanCPayResp.class);

        return bScanCPayResp;
    }

    /**
     * 二维码支付
     * @param req
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public BScanCPayResp cScanBPay(BScanCPayReq req, LoginInfo loginInfo) throws Exception {
        CheckUtils.checkEmpty(req.getPayCode(), "CMMS: 支付场景为空.");
        CheckUtils.checkNull(req.getPayInfo(), "CMMS: 支付信息为空");
        String payCode = req.getPayCode();
        if (StringUtils.equals(payCode, BScanCPayReq.PAYCODE_REFUND)) {
            // 退款
            CheckUtils.checkEmpty(req.getPayInfo().getOrderNo(), "CMMS:退款时支付订单号为空.");
        } else if (StringUtils.equals(payCode, BScanCPayReq.PAYCODE_PAY)) {
            // 支付
            CheckUtils.checkEmpty(req.getPayInfo().getMchntOrderNo(), "CMMS:支付流失号为空.");
            CheckUtils.checkEmpty(req.getPayInfo().getAmount(), "CMMS:支付金额为空.");
            CheckUtils.checkEmpty(req.getPayInfo().getPayType(), "CMMS:支付方式为空.");
        } else {
            throw new BusinessException(String.format("CMMS:[%s]支付场景不存在.", payCode));
        }

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("out_trade_no", req.getPayInfo().getMchntOrderNo());
        bossreq.put("payType", req.getPayInfo().getPayType());
        bossreq.put("amount", req.getPayInfo().getAmount());
        bossreq.put("custInfo", req.getCustInfo());
        bossreq.put("bizCustomInfo", req.getBizCustomInfo());
        String a = JSON.toJSONString(bossreq);
        System.out.println(a);
        String output = getBossHttpInfOutput(req.getBizorderid(), BizConstant.BossInterfaceService.GW_PAYMENT, bossreq, loginInfo);
        BScanCPayResp bScanCPayResp = JSON.parseObject(output, BScanCPayResp.class);

        return bScanCPayResp;
    }

    /**
     * 二维码支付查询
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public JSONObject queryOrderResultBoss(String bizorderid,String orderNo, LoginInfo loginInfo) throws Exception {
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("orderNo", orderNo);
        String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.GW_QUERY_ORDER_RESULT, bossreq, loginInfo);
        JSONObject resultResp = JSONObject.parseObject(output);
        return resultResp;
    }

    /**
     * 二维码支付撤销
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public JSONObject gWCancelOrder(String bizorderid,String orderNo, LoginInfo loginInfo) throws Exception {
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("orderNo", orderNo);
        String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.GW_CANCELORDER, bossreq, loginInfo);
        JSONObject resultResp = JSONObject.parseObject(output);
        return resultResp;
    }


    /**
     * 二维码支付退款
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public JSONObject gwOrderRefund(String bizorderid,String orderNo,String refundReason,String refundAmount, LoginInfo loginInfo) throws Exception {
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("orderNo", orderNo);
        bossreq.put("refundReason", refundReason);
        bossreq.put("refundAmount", refundAmount);
        String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.GW_ORDER_REFUND, bossreq, loginInfo);
        JSONObject resultResp = JSONObject.parseObject(output);
        return resultResp;
    }


    public ReverseResp reverse(String orderNo, LoginInfo loginInfo, String bizorderid) throws Exception {
        CheckUtils.checkEmpty(orderNo, "CMMS:BOSS支付订单号为空");

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("orderNo", orderNo);
        String output = getBossHttpInfOutput(bizorderid, BizConstant.BossInterfaceService.REVERSE, bossreq, loginInfo);
        ReverseResp resp = JSON.parseObject(output, ReverseResp.class);

        return resp;
    }

}
