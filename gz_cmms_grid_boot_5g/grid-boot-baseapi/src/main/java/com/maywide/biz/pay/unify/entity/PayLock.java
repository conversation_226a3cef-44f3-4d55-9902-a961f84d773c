package com.maywide.biz.pay.unify.entity;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 *
 * 支付回调锁，防止重复的回调
 *
 * <AUTHOR>
 */
public class PayLock {

    private static Logger log = LoggerFactory.getLogger(PayLock.class);

    /**
     * 锁池，每个payid都是同一个锁，这样性能损失少
     */
    private static Map<String, LockBo> lockPool = new ConcurrentHashMap();
    /**
     * 锁失效时间 10分钟
     */
    private static long lockExpire = 10 * 60 * 1000L;
    /**
     * 清理任务状态
     */
    private static long lastClearTime = System.currentTimeMillis();


    /**
     * 获得锁
     * @param payid
     * @return
     */
    public static ReentrantLock getLock(String payid) {
        LockBo lock = lockPool.get(payid);
        if (lock == null) {
            lock = new LockBo();
            lock.setLock(new ReentrantLock());
            lock.setCreated(System.currentTimeMillis());
            lockPool.put(payid, lock);
        }

        // 执行清理
        clearThread();

        return lock.getLock();
    }

    private synchronized static void clearThread() {
        try {
            long now = System.currentTimeMillis();
            if (now - lastClearTime >= lockExpire) {
                clear();
                lastClearTime = now;
            }
        } catch (Exception e) {
            log.info("==> clear error. ", e);
        }
    }

    /**
     * 清理过期锁
     */
    public synchronized static void clear() {
        try {
            long now = System.currentTimeMillis();
            Thread.sleep(2000);
            Set<Map.Entry<String, LockBo>> entries = lockPool.entrySet();
            Iterator<Map.Entry<String, LockBo>> iterator = entries.iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, LockBo> next = iterator.next();
                String key = next.getKey();
                LockBo value = next.getValue();
                Long created = value.getCreated();
                if (now - created >= lockExpire) {
                    iterator.remove();
                    log.info("payid[{}]-lock[{}] clear.", key, value.toString());
                }
            }
        } catch (Exception e) {
            log.error("==> LockPool clear error. ", e);
        }
    }

    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    PayLock.clearThread();
                }
            }).start();
        }
    }

    static class LockBo {

        /**
         * 锁
         */
        private ReentrantLock lock;
        /**
         * 创建的时间戳
         */
        private Long created;

        public ReentrantLock getLock() {
            return lock;
        }

        public void setLock(ReentrantLock lock) {
            this.lock = lock;
        }

        public Long getCreated() {
            return created;
        }

        public void setCreated(Long created) {
            this.created = created;
        }

        @Override
        public String toString() {
            return "LockBo{" +
                    "lock=" + lock +
                    ", created=" + created +
                    '}';
        }
    }

}
