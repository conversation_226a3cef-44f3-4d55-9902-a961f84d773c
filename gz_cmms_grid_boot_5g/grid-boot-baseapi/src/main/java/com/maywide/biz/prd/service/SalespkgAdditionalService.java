package com.maywide.biz.prd.service;

import com.maywide.biz.prd.dao.SalespkgAdditionalDao;
import com.maywide.biz.prd.entity.SalespkgAdditional;
import com.maywide.core.dao.BaseDao;
import com.maywide.core.service.BaseService;
import com.maywide.core.service.PersistentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class SalespkgAdditionalService extends BaseService<SalespkgAdditional,Long>{

    @Autowired
    private SalespkgAdditionalDao salespkgAdditionalDao;

    @Autowired
    private PersistentService persistentService;

    @Override
    protected BaseDao<SalespkgAdditional, Long> getEntityDao() {
        return salespkgAdditionalDao;
    }

//    public void checkEntity(CatalogCondtion entity) {
//        try {
//            if (entity.getId() == null) {
//                Long counter = (Long) persistentService.findUnique(
//                        "SELECT COUNT(*) FROM CatalogCondtion WHERE catalogid = ? " +
//                                "AND contiontype = ? AND contionvalue = ?",
//                        entity.getCatalogid(), entity.getContiontype(), entity.getContionvalue());
//                if (counter > 0) {
//                    throw new Exception("该条件已存在");
//                }
//            } else {
//                Long counter = (Long) persistentService.findUnique(
//                        "SELECT COUNT(*) FROM CatalogCondtion WHERE catalogid = ? " +
//                                "AND contiontype = ? AND contionvalue = ? AND id != ?",
//                        entity.getCatalogid(), entity.getContiontype(),
//                        entity.getContionvalue(), entity.getId());
//                if (counter > 0) {
//                    throw new Exception("该条件已存在");
//                }
//            }
//        } catch (Exception e) {
//            throw new ServiceException(e.getMessage());
//        }
//    }
}
