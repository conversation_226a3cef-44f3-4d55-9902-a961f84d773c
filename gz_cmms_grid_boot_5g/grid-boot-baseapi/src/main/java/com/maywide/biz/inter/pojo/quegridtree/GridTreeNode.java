package com.maywide.biz.inter.pojo.quegridtree;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.maywide.biz.inter.pojo.dataReport.GridInfoResp;

import java.util.List;

@JsonIgnoreProperties(value = { "prev" })
public class GridTreeNode extends GridInfoResp {

	private GridTreeNode prev;

	private Long prevId;

	private List<GridTreeNode> childNodes;

	private int level;


	private Long gridid;
	private String gridcode;
	private String gridname;
	private Long gtype;
	private Long countyid;
	@Override
	public String getGridCode() {
		return this.gridcode;
	}

	@Override
	public String getGridName() {
		return this.gridname;
	}

	@Override
	public String getgType() {
		return this.gtype == null ? null : this.gtype.toString();
	}

	@Override
	public Long getGridId() {
		return this.gridid;
	}

	public Long getGridid() {
		return gridid;
	}

	public void setGridid(Long gridid) {
		this.gridid = gridid;
	}

	public String getGridcode() {
		return gridcode;
	}

	public void setGridcode(String gridcode) {
		this.gridcode = gridcode;
	}

	public String getGridname() {
		return gridname;
	}

	public void setGridname(String gridname) {
		this.gridname = gridname;
	}

	public Long getGtype() {
		return gtype;
	}

	public void setGtype(Long gtype) {
		this.gtype = gtype;
	}

	public GridTreeNode getPrev() {
		return prev;
	}

	public void setPrev(GridTreeNode prev) {
		this.prev = prev;
	}

	public Long getPrevId() {
		return prevId;
	}

	public void setPrevId(Long prevId) {
		this.prevId = prevId;
	}

	public List<GridTreeNode> getChildNodes() {
		return childNodes;
	}

	public void setChildNodes(List<GridTreeNode> childNodes) {
		this.childNodes = childNodes;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public Long getCountyid() {
		return countyid;
	}

	public void setCountyid(Long countyid) {
		this.countyid = countyid;
	}
}
