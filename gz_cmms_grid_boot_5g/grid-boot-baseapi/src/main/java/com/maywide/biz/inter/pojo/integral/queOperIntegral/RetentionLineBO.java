package com.maywide.biz.inter.pojo.integral.queOperIntegral;

public class RetentionLineBO {
    private String gridcode;

    private int baseLine;
    private int standardLine;
    private int challengeLine;

    public String getGridcode() {
        return gridcode;
    }

    public void setGridcode(String gridcode) {
        this.gridcode = gridcode;
    }

    public int getBaseLine() {
        return baseLine;
    }

    public void setBaseLine(int baseLine) {
        this.baseLine = baseLine;
    }

    public int getStandardLine() {
        return standardLine;
    }

    public void setStandardLine(int standardLine) {
        this.standardLine = standardLine;
    }

    public int getChallengeLine() {
        return challengeLine;
    }

    public void setChallengeLine(int challengeLine) {
        this.challengeLine = challengeLine;
    }
}
