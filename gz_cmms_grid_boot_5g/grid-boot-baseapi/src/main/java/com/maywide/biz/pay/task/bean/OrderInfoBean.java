package com.maywide.biz.pay.task.bean;

import com.maywide.biz.core.pojo.LoginInfo;
import lombok.*;

import java.io.Serializable;

/**
 * OrderInfoBean
 *
 * <AUTHOR>
 * @date 2022/11/24 16:13
 * @since 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class OrderInfoBean implements Serializable {

    // 创建时间
    private Long createTime;

    // 订单ID
    private String orderId;

    // 客户ID
    private String custId;

    // 支付方式
    private String payment;

    // 支付时间
    private Long payTime;

    // 支付状态
    private String payStatus;

    // 提交状态
    private String submitStatus;

    // 错误描述
    private String errorDesc;

    // 登录用户信息
    private LoginInfo loginInfo;

}
