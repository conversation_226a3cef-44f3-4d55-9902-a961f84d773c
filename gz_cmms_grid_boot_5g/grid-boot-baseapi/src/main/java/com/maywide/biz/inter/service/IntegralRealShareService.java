package com.maywide.biz.inter.service;

import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.TokenReturnInfo;
import com.maywide.biz.core.service.BillingHttpClientService;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.constant.QueConstant;
import com.maywide.biz.inter.pojo.integral.queOperIntegral.IntegralBO;
import com.maywide.biz.inter.pojo.integralRealShare.cancelshareRealIntegral.CancelShareRealIntegralReq;
import com.maywide.biz.inter.pojo.integralRealShare.cancelshareRealIntegral.CancelShareRealIntegralResp;
import com.maywide.biz.inter.pojo.integralRealShare.queryRealIntegralOperList.QueryRealIntegralOperListBO;
import com.maywide.biz.inter.pojo.integralRealShare.queryRealIntegralOperList.QueryRealIntegralOperListReq;
import com.maywide.biz.inter.pojo.integralRealShare.queryRealIntegralOperList.QueryRealIntegralOperListResp;
import com.maywide.biz.inter.pojo.integralRealShare.queryShareRealIntegral.QueryShareRealIntegralBo;
import com.maywide.biz.inter.pojo.integralRealShare.queryShareRealIntegral.QueryShareRealIntegralReq;
import com.maywide.biz.inter.pojo.integralRealShare.queryShareRealIntegral.QueryShareRealIntegralResp;
import com.maywide.biz.inter.pojo.integralRealShare.shareRealIntegral.ShareRealIntegralItem;
import com.maywide.biz.inter.pojo.integralRealShare.shareRealIntegral.ShareRealIntegralReq;
import com.maywide.biz.inter.pojo.integralRealShare.shareRealIntegral.ShareRealIntegralResp;
import com.maywide.biz.prv.entity.PrvArea;
import com.maywide.biz.prv.entity.PrvOperator;
import com.maywide.biz.salary.SalaryConstants;
import com.maywide.biz.salary.repbo.CentSumRep;
import com.maywide.biz.salary.reqbo.CentSumReq;
import com.maywide.biz.salary.service.BeforehandRealService;
import com.maywide.core.dao.support.Page;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.util.BeanUtil;
import com.maywide.core.util.CheckUtils;
import com.maywide.core.util.DateUtils;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class IntegralRealShareService extends CommonService {
    @Autowired
    private BillingHttpClientService billingHttpClientService;

    /**
     * 实积分分享-批量
     * @param req
     * @return
     * @throws Exception
     */
    @Transactional(readOnly = true)
    public TokenReturnInfo shareRealIntegral(ShareRealIntegralReq req) throws Exception {
        TokenReturnInfo returnInfo = new TokenReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);
        CheckUtils.checkNull(req.getReqinfo(),"[reqinfo]不能为空!");

        for (ShareRealIntegralItem item: req.getReqinfo()){
            CheckUtils.checkNull(item.getCtype(),"[ctype]不能为空!");
            CheckUtils.checkNull(item.getCycleid(),"[cycleid]不能为空!");
            CheckUtils.checkNull(item.getShare_cent_id(),"[share_cent_id]不能为空!");
            CheckUtils.checkNull(item.getRecv_cent_id(),"[recv_cent_id]不能为空!");
            CheckUtils.checkNull(item.getCent(),"[cent]不能为空!");
            CheckUtils.checkNull(item.getCtype(),"[ctype]不能为空!");

            item.setCycleid(item.getCycleid().replace("-",""));

            if (StringUtils.isEmpty(item.getOperdeptid())){
                item.setOperdeptid(loginInfo.getDeptid()+"");
            }
            if (StringUtils.isEmpty(item.getOperid())){
                item.setOperid(loginInfo.getOperid()+"");
            }
            if (StringUtils.isEmpty(item.getAreaid())){
                item.setAreaid(loginInfo.getAreaid()+"");
            }
            if (StringUtils.isEmpty(item.getCity())){
                item.setCity(loginInfo.getCity());
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.accumulate("reqinfo",req.getReqinfo());
        String response= billingHttpClientService.requestPost(loginInfo, SalaryConstants.API_URL.SHARE_REAL_CENT,jsonObject);
        ShareRealIntegralResp resp = (ShareRealIntegralResp) BeanUtil.jsonToObject(response, ShareRealIntegralResp.class);

        returnInfo.setCode(Long.valueOf(resp.getRtcode()));
        returnInfo.setMessage(resp.getMessage());
        returnInfo.setData("");
        return returnInfo;
    }

    /**
     * 取消指定的实积分分享
     * @param req
     * @return
     * @throws Exception
     */
    @Transactional(readOnly = true)
    public TokenReturnInfo cancelshareRealIntegral(CancelShareRealIntegralReq req) throws Exception {
        TokenReturnInfo returnInfo = new TokenReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);
        CheckUtils.checkEmpty(req.getRecid(),"[recid]不能为空!");


        if (StringUtils.isEmpty(req.getOperid())){
            req.setOperid(loginInfo.getOperid()+"");
        }

        Map map = BeanUtil.beanToMap(req);
        String response= billingHttpClientService.requestPost(loginInfo, SalaryConstants.API_URL.CANCEL_SHARE_REAL_CENT,map);//TODO
        CancelShareRealIntegralResp resp = (CancelShareRealIntegralResp) BeanUtil.jsonToObject(response, CancelShareRealIntegralResp.class);

        returnInfo.setCode(Long.valueOf(resp.getRtcode()));
        returnInfo.setMessage(resp.getMessage());
        returnInfo.setData("");
        return returnInfo;
    }

    /**
     * 查询的实积分分享明细
     * @param req
     * @return
     * @throws Exception
     */
    @Transactional(readOnly = true)
    public TokenReturnInfo queryShareRealIntegral(QueryShareRealIntegralReq req) throws Exception {
        TokenReturnInfo returnInfo = new TokenReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);

        if (StringUtils.isNotEmpty(req.getMonth_start())) {
            req.setMonth_start(req.getMonth_start().replace("-", ""));
        }
        if (StringUtils.isNotEmpty(req.getMonth_end())) {
            req.setMonth_end(req.getMonth_end().replace("-", ""));
        }
        if (StringUtils.isNotEmpty(req.getCtime_start())) {
            req.setCtime_start(req.getCtime_start().replace("-", ""));
        }
        if (StringUtils.isNotEmpty(req.getCtime_end())) {
        req.setCtime_end(req.getCtime_end().replace("-",""));
        }

        List<String> areaList = req.getAreaList();

        req.setAreaList(null);

        Map map = BeanUtil.beanToMap(req);
        String response= billingHttpClientService.requestPost(loginInfo, SalaryConstants.API_URL.QUERY_SHARE_REAL_CENT,map);//TODO
        QueryShareRealIntegralResp resp = (QueryShareRealIntegralResp) BeanUtil.jsonToObject(response, QueryShareRealIntegralResp.class);

        if (resp!=null&&resp.getCentlist()!=null) {
            Iterator<QueryShareRealIntegralBo> it = resp.getCentlist().iterator();
            while(it.hasNext()){
                QueryShareRealIntegralBo item = it.next();
                if("N".equals(item.getStatus())||(areaList!=null&&!areaList.contains(item.getAreaid()))){
                    it.remove();
                }else{
                    PrvOperator prvOperator = (PrvOperator) getDAO().findUniqueByProperty(PrvOperator.class, "id", item.getShare_cent_id());
                    item.setShare_cent_opername(prvOperator.getName());

                    prvOperator = (PrvOperator) getDAO().findUniqueByProperty(PrvOperator.class, "id", item.getRecv_cent_id());
                    item.setRecv_cent_opername(prvOperator.getName());

                    prvOperator = (PrvOperator) getDAO().findUniqueByProperty(PrvOperator.class, "id", item.getOperid());
                    item.setOperName(prvOperator.getName());

                    PrvArea prvArea = (PrvArea)getDAO().findUniqueByProperty(PrvArea.class, "id", Long.valueOf(item.getAreaid()));
                    item.setAreaName(prvArea.getName());

                    item.setCtypeName(integralTypeMap.get(item.getCtype()));

                    if (StringUtils.isNotEmpty(req.getSearchShareRelateOper())) {
                        //自己是分享人，模糊搜索被分享人
                        if (StringUtils.isNotEmpty(req.getShare_cent_id()) && StringUtils.isEmpty(req.getRecv_cent_id())) {
                            if (item.getRecv_cent_opername().contains(req.getSearchShareRelateOper())){

                            }else {
                                it.remove();
                            }
                        }
                        //自己是被分享人，模糊搜索分享人
                        if (StringUtils.isNotEmpty(req.getRecv_cent_id()) && StringUtils.isEmpty(req.getShare_cent_id())) {
                            if (item.getShare_cent_opername().contains(req.getSearchShareRelateOper())){

                            }else {
                                it.remove();
                            }
                        }
                    }



                }
                item.setCreatetime(DateUtils.dateFormat(item.getCreatetime(),"yyyyMMddHHmmss","yyyy-MM-dd HH:mm:ss"));
            }
//            for (QueryShareRealIntegralBo item : resp.getCentlist()) {
//                PrvOperator prvOperator = (PrvOperator) getDAO().findUniqueByProperty(PrvOperator.class, "id", item.getShare_cent_id());
//                item.setShare_cent_opername(prvOperator.getName());
//
//                prvOperator = (PrvOperator) getDAO().findUniqueByProperty(PrvOperator.class, "id", item.getRecv_cent_id());
//                item.setRecv_cent_opername(prvOperator.getName());
//            }
        }

        returnInfo.setCode(Long.valueOf(resp.getRtcode()));
        returnInfo.setMessage(resp.getMessage());
        returnInfo.setData(resp.getCentlist());
        return returnInfo;
    }

    //TODO PC搜索网格人员
    //TODO PC返回可选业务区
    //TODO PC返回所有人的实积分

    //TODO App搜索网格人员

    /**
     * PC--实积分分享--查询符合查询条件的操作员信息
     * @param req
     * @return
     * @throws Exception
     */
    @Transactional(readOnly = true)
    public TokenReturnInfo queryRealIntegralOperList(QueryRealIntegralOperListReq req) throws Exception {
        QueryRealIntegralOperListResp resp = new QueryRealIntegralOperListResp();
        TokenReturnInfo returnInfo = new TokenReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);

        if (req.getAreaList()==null||req.getAreaList().size()==0) {
            throw new BusinessException("业务区不能为空");
        }
        CheckUtils.checkEmpty(req.getIntegralType(), "积分类型不能为空");

        List<QueryRealIntegralOperListBO> queryRealIntegralOperListBOList = queTargetOperList(req,resp);
        fillDataToOperList(queryRealIntegralOperListBOList,req);

        resp.setRealIntegralOperList(queryRealIntegralOperListBOList);

        returnInfo.setData(resp);

        return returnInfo;
    }

    //从网格数据库查询符合条件的操作员
    private List<QueryRealIntegralOperListBO> queTargetOperList(QueryRealIntegralOperListReq req, QueryRealIntegralOperListResp resp) throws Exception{
//        select * from prv_operator a ,prv_department b,prv_operdept c,prv_area d
//        where a.operid = c.operid and b.deptid = c.depid and b.areaid = d.areaid
//        and a.status='0'
//        and d.areaid in ('715')
        List params = new ArrayList();

        StringBuffer sb = new StringBuffer();
        sb.append(" select d.name areaName, e.gridname gridName, a.operid operId,a.name operName ");
        sb.append(" from prv_operator a ,prv_department b,prv_operdept c,prv_area d , biz_grid_info e ,biz_grid_manager f ");
        sb.append(" where a.operid = c.operid and b.deptid = c.depid and b.areaid = d.areaid ");
        sb.append("  and e.areaid = d.areaid and e.gridid = f.gridid and a.operid = f.operid");
        sb.append(" and a.status='0' ");
        sb.append(" and d.areaid in ( ");

        List<String> areaList = req.getAreaList();
        for (int i = 0; i < areaList.size(); i++) {
            params.add(areaList.get(i));
            sb.append("?");
            if (i != areaList.size() - 1) {
                sb.append(",");
            }
        }
        sb.append("	)");

        Page page = new Page();
        page.setPageSize(10);
        if (StringUtils.isNotBlank(req.getPagesize())) {
            page.setPageSize(Integer.valueOf(req.getPagesize()));
        }
        if (StringUtils.isNotBlank(req.getCurrentPage())) {
            page.setPageNo(Integer.valueOf(req.getCurrentPage()));
        }

        page = getDAO().find(page, sb.toString(), QueryRealIntegralOperListBO.class, params.toArray());

        List<QueryRealIntegralOperListBO> realIntegralOperList = page.getResult();

        resp.setPagesize(BeanUtil.object2String(page.getPageSize()));
        resp.setCurrentPage(BeanUtil.object2String(page.getCntPageNo()));
        resp.setTotalRecords(BeanUtil.object2String(page.getTotalCount()));

        return realIntegralOperList;
    }
    private static HashMap<String,String> integralTypeMap = new HashMap<>();
    static {
        integralTypeMap.put("1","安装积分");
        integralTypeMap.put("2","销售积分");
    }

    //给每个操作员填充实积分的数据
    private void fillDataToOperList(List<QueryRealIntegralOperListBO> realIntegralOperList,QueryRealIntegralOperListReq req) throws Exception{
        if (realIntegralOperList==null){
            return;
        }
        for (QueryRealIntegralOperListBO item : realIntegralOperList) {
            item.setIntegralDate(req.getShareSDate());
            item.setIntegralType(req.getIntegralType());
            item.setIntegralTypeName(integralTypeMap.get(req.getIntegralType()));

            double preShareIntegral[] = fillRealIntegralToOper(item, req.getShareSDate(), req.getIntegralType());
            fillRealIntegralSharedToOper(item, req,preShareIntegral[1]);
            fillRealIntegralBeShareToOper(item, req,preShareIntegral[2]);
            item.setRealIntegralNow(item.getRealIntegral() + item.getRealIntegralShared() + item.getRealIntegralBeShare());
        }
    }

    //给操作员填充原实积分
    private double[] fillRealIntegralToOper(QueryRealIntegralOperListBO item,String shareDate,String integralType) throws Exception{
        if ("1".equals(integralType)) {
            double[] realIntegral = getOperRealIntegralType1(shareDate, item.getOperId()+"");
            item.setRealIntegral(realIntegral[0]);
            return realIntegral;
        } else {
            double[] realIntegral = getOperRealIntegralType2(shareDate, item.getOperId()+"");
            item.setRealIntegral(realIntegral[0]);
            return realIntegral;
        }
    }
    //给操作员填充分享出去的积分
    private void fillRealIntegralSharedToOper(QueryRealIntegralOperListBO item,QueryRealIntegralOperListReq req,double preShareIntegral) throws Exception{
            QueryShareRealIntegralReq queryShareRealIntegralReq = new QueryShareRealIntegralReq();
            queryShareRealIntegralReq.setMonth_start(req.getShareSDate());
            queryShareRealIntegralReq.setMonth_end(req.getShareEDate());
            queryShareRealIntegralReq.setShare_cent_id(item.getOperId());
            queryShareRealIntegralReq.setCtype(req.getIntegralType());

            //查询分享人是这个人的明细，将分数相加即是它分享出去的分
            TokenReturnInfo queryShareRealIntegralInfo = queryShareRealIntegral(queryShareRealIntegralReq);
            List<QueryShareRealIntegralBo> centlist = ((List<QueryShareRealIntegralBo> )queryShareRealIntegralInfo.getData());
            double realIntegralShared = 0d;
            if (centlist!=null) {
                for (QueryShareRealIntegralBo integralItem : centlist) {
                    realIntegralShared += Double.valueOf(integralItem.getCent());
                }
            }
            item.setRealIntegralShared(-realIntegralShared+preShareIntegral);

    }
    //给操作员填充被分享的积分
    private void fillRealIntegralBeShareToOper(QueryRealIntegralOperListBO item,QueryRealIntegralOperListReq req,double preShareIntegral) throws Exception{
        QueryShareRealIntegralReq queryShareRealIntegralReq = new QueryShareRealIntegralReq();
        queryShareRealIntegralReq.setMonth_start(req.getShareSDate());
        queryShareRealIntegralReq.setMonth_end(req.getShareEDate());
        queryShareRealIntegralReq.setRecv_cent_id(item.getOperId());
        queryShareRealIntegralReq.setCtype(req.getIntegralType());

        //查询被分享人是这个人的明细，将分数相加即是别人分享给他的分
        TokenReturnInfo queryShareRealIntegralInfo = queryShareRealIntegral(queryShareRealIntegralReq);
        List<QueryShareRealIntegralBo> centlist = ((List<QueryShareRealIntegralBo> )queryShareRealIntegralInfo.getData());
        double realIntegralShared = 0d;
        if (centlist!=null) {
            for (QueryShareRealIntegralBo integralItem : centlist) {
                realIntegralShared += Double.valueOf(integralItem.getCent());
            }
        }
        item.setRealIntegralBeShare(realIntegralShared+preShareIntegral);

    }

    @Autowired
    private BeforehandRealService beforehandRealService;

    //根据操作员id和月份查实积分-销售积分
    private double[] getOperRealIntegralType2(String date,String operid) throws Exception{
        date = date.replace("-","");
        IntegralBO integral = new IntegralBO();
        //--------------销售积分---------------
        CentSumReq csreq = new CentSumReq();
        csreq.setCycleid(date);
        csreq.setOperator(String.valueOf(operid));
        csreq.setGroupcode(SalaryConstants.OthersKpi.ZSBUSI);
//        csreq.setWhgridcode(grid.getGridcode());
        csreq.setStatus("1"); //审核通过
        csreq.setPagesize(Integer.MAX_VALUE + "");
        csreq.setCurrentPage("1");
        CentSumRep zsbusiRep = beforehandRealService.queryCentSum(csreq);
        Double zsbusiSrccents = 0D;
        Double zsbusiSharecents = 0D;

        double integralShared = 0d;
        double integralBeShared = 0d;

        if (zsbusiRep != null && zsbusiRep.getCentlist() != null && zsbusiRep.getCentlist().size() > 0) {
            for (CentSumRep.Detail detail : zsbusiRep.getCentlist()) {
                String srccents = StringUtils.isNotEmpty(detail.getSrccents()) ?
                        detail.getSrccents() : "0";
                zsbusiSrccents = zsbusiSrccents + Double.valueOf(srccents);
                //是否有剔除积分，如果有则合并
                String adjust = detail.getAdjustcents();
                if (StringUtils.isNotBlank(adjust)) {
                    zsbusiSrccents = zsbusiSrccents + Double.valueOf(adjust);
                }
                String share = detail.getSharecents();
                if (StringUtils.isNotEmpty(share)) {
                    zsbusiSharecents = zsbusiSharecents + Double.valueOf(share);
                    if (Double.valueOf(share)<0){
                        integralShared = integralShared+ Double.valueOf(share);
                    }else{
                        integralBeShared = integralBeShared+ Double.valueOf(share);
                    }
                }
            }
        }

//        return   zsbusiSharecents;
        return new double[] {zsbusiSharecents,integralShared,integralBeShared};
    }

    //根据操作员id和月份查实积分-安装积分
    private double[] getOperRealIntegralType1(String date,String operid) throws Exception{
        date = date.replace("-","");
        CentSumReq csreq = new CentSumReq();
        csreq.setCycleid(date);
        csreq.setOperator(String.valueOf(operid));
        csreq.setGroupcode(SalaryConstants.OthersKpi.ZSSETUP);
//        csreq.setWhgridcode(grid.getGridcode());
        csreq.setStatus("1"); //审核通过
        csreq.setPagesize(Integer.MAX_VALUE + "");
        csreq.setCurrentPage("1");
        CentSumRep zssetupRep = beforehandRealService.queryCentSum(csreq);
        Double zssetupSrccents = 0D;
        Double zssetupSharecents = 0D;

        double integralShared = 0d;
        double integralBeShared = 0d;

        if (zssetupRep != null && zssetupRep.getCentlist() != null && zssetupRep.getCentlist().size() > 0) {
            for (CentSumRep.Detail detail : zssetupRep.getCentlist()) {
                String srccents = StringUtils.isNotEmpty(detail.getSrccents()) ?
                        detail.getSrccents() : "0";
                zssetupSrccents = zssetupSrccents + Double.valueOf(srccents);
                //是否有剔除积分，如果有则合并
                String adjust = detail.getAdjustcents();
                if (StringUtils.isNotBlank(adjust)) {
                    zssetupSrccents = zssetupSrccents + Double.valueOf(adjust);
                }
                String share = detail.getSharecents();
                if (StringUtils.isNotBlank(share)) {
                    zssetupSharecents = zssetupSharecents + Double.valueOf(share);
                    if (Double.valueOf(share)<0){
                        integralShared = integralShared+ Double.valueOf(share);
                    }else{
                        integralBeShared = integralBeShared+ Double.valueOf(share);
                    }
                }
            }
        }
        return new double[] {zssetupSrccents,integralShared,integralBeShared};
    }

}
