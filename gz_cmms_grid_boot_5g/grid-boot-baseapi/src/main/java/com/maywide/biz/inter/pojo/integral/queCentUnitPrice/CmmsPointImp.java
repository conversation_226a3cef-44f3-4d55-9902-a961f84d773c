package com.maywide.biz.inter.pojo.integral.queCentUnitPrice;

/**
 * <AUTHOR>
 * @date 2020/8/27 0027
 */
public class CmmsPointImp {

    private String 统计日期;
    private String 网格;
    private String 主机数上月末;
    private String 当前主机数;
    private String 主机基数;
    private String 净增率;
    private String 保有积分;
    private String 积分单价;
    private String 基础线;
    private String 标准线;
    private String 挑战线;

    public String get统计日期() {
        return 统计日期;
    }

    public void set统计日期(String 统计日期) {
        this.统计日期 = 统计日期;
    }

    public String get网格() {
        return 网格;
    }

    public void set网格(String 网格) {
        this.网格 = 网格;
    }

    public String get主机数上月末() {
        return 主机数上月末;
    }

    public void set主机数上月末(String 主机数上月末) {
        this.主机数上月末 = 主机数上月末;
    }

    public String get当前主机数() {
        return 当前主机数;
    }

    public void set当前主机数(String 当前主机数) {
        this.当前主机数 = 当前主机数;
    }

    public String get主机基数() {
        return 主机基数;
    }

    public void set主机基数(String 主机基数) {
        this.主机基数 = 主机基数;
    }

    public String get净增率() {
        return 净增率;
    }

    public void set净增率(String 净增率) {
        this.净增率 = 净增率;
    }

    public String get保有积分() {
        return 保有积分;
    }

    public void set保有积分(String 保有积分) {
        this.保有积分 = 保有积分;
    }

    public String get积分单价() {
        return 积分单价;
    }

    public void set积分单价(String 积分单价) {
        this.积分单价 = 积分单价;
    }

    public String get基础线() {
        return 基础线;
    }

    public void set基础线(String 基础线) {
        this.基础线 = 基础线;
    }

    public String get标准线() {
        return 标准线;
    }

    public void set标准线(String 标准线) {
        this.标准线 = 标准线;
    }

    public String get挑战线() {
        return 挑战线;
    }

    public void set挑战线(String 挑战线) {
        this.挑战线 = 挑战线;
    }
}
