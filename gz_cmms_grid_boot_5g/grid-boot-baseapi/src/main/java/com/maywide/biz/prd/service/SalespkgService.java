package com.maywide.biz.prd.service;

import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.prd.dao.SalespkgDao;
import com.maywide.biz.prd.entity.Salespkg;
import com.maywide.core.dao.BaseDao;
import com.maywide.core.dao.support.Page;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.service.BaseService;
import com.maywide.core.service.PersistentService;
import com.maywide.core.util.SimpleSqlCreator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Transactional
public class SalespkgService extends BaseService<Salespkg,Long>{
    
    @Autowired
    private SalespkgDao salespkgDao;
    
    @Autowired
    private PersistentService persistentService;

    @Override
    protected BaseDao<Salespkg, Long> getEntityDao() {
        return salespkgDao;
    }

    public Object findByPageForJump(String codeOrNameField,String objtype, String searchCity, String areaid, Pageable pageable,
            String orderField, String sortType) throws Exception {
        PageImpl<Salespkg> pageResult = null;
        Page<Salespkg> page = new Page<Salespkg>();
        page.setPageNo(pageable.getPageNumber() + 1);
        page.setPageSize(pageable.getPageSize());

        StringBuffer sql = SimpleSqlCreator.createSelectAllFieldSql(entityClass).append(" WHERE 1=1");
        List<Object> paramList = new ArrayList<Object>();

		LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
		sql.append(" AND o.SALESPKGID   IN (SELECT  OBJID FROM prd_salespkg_know WHERE city=? ) ");
		paramList.add(loginInfo.getCity());
        
		if (StringUtils.isNotBlank(codeOrNameField)) {
			String searchValue = "%" + codeOrNameField + "%";
			sql.append(" AND (o.SALESCODE LIKE ?");
			sql.append(" OR o.SALESNAME LIKE ?) ");
			paramList.add(searchValue);
			paramList.add(searchValue);
		}

        if (StringUtils.isNotBlank(orderField) && !"createdDate".equals(orderField)) {
            sql.append(" ORDER BY o.").append(orderField)
                    .append(StringUtils.isNotBlank(sortType) ? (" " + sortType) : "");
        } else {
            sql.append(" ORDER BY o.CREATETIME DESC");
        }

        persistentService.clear();
        page = persistentService.find(page, sql.toString(), entityClass, paramList.toArray());

        List<Salespkg> resultList = page.getResult();
        if (null != page && null != resultList) {
            int total = page.getTotalCount();
            pageResult = new PageImpl<Salespkg>(resultList, pageable, total);
        } else {
            pageResult = new PageImpl<Salespkg>(new ArrayList<Salespkg>(), pageable, 0);
        }
        return pageResult;
    }



    public Object saleskgBatchQuery(Pageable pageable,String time,String codeOrNameField)throws Exception{
        PageImpl<Salespkg> pageResult = null;
        Page<Salespkg> page = new Page<Salespkg>();
        page.setPageNo(pageable.getPageNumber() + 1);
        page.setPageSize(pageable.getPageSize());
        StringBuffer sql = SimpleSqlCreator.createSelectAllFieldSql(entityClass).append(" WHERE 1=1");
        List<Object> paramList = Collections.synchronizedList( new ArrayList<Object>());

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();

        sql.append(" AND o.CREATETIME >= ? AND o.CREATETIME <= ?");

        if(StringUtils.isBlank(time)){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            Date date =  cal.getTime();
            cal.add(Calendar.DAY_OF_MONTH, -90);
            Date date2 = cal.getTime();
            String startDate = simpleDateFormat.format(date2);
            String endDate = simpleDateFormat.format(date);
            paramList.add(startDate+" 00:00:00");
            paramList.add(endDate+" 23:59:59");
        }else{
            String[] timeRangeArray = time.split("～");
            String startDate = timeRangeArray[0].trim() + " 00:00:00";
            String endDate = timeRangeArray[1].trim() + " 23:59:59";
            paramList.add(startDate);
            paramList.add(endDate);
        }

        sql.append(" and  NOT EXISTS (SELECT k.`OBJID` SALESPKGID  FROM PRD_SALESPKG_KNOW k WHERE k.`OBJTYPE` = '1' "
                  +" AND (k.city = ? OR k.city = '*')	 AND k.`OBJID` = o.SALESPKGID )");
        paramList.add(loginInfo.getCity());


        if (StringUtils.isNotBlank(codeOrNameField)) {
            String searchValue = "%" + codeOrNameField + "%";
            sql.append(" AND (o.SALESPKGCODE LIKE ?");
            sql.append(" OR o.SALESPKGNAME LIKE ?) ");
            paramList.add(searchValue);
            paramList.add(searchValue);

        }

        sql.append(" ORDER BY o.CREATETIME DESC");

        persistentService.clear();
        page = persistentService.find(page, sql.toString(), entityClass, paramList.toArray());

        List<Salespkg> resultList = page.getResult();
        if (null != page && null != resultList) {
            int total = page.getTotalCount();
            pageResult = new PageImpl<Salespkg>(resultList, pageable, total);
        } else {
            pageResult = new PageImpl<Salespkg>(new ArrayList<Salespkg
                    >(), pageable, 0);
        }
        return pageResult;
    }









}
