package com.maywide.biz.inter.pojo.common;

import com.maywide.biz.core.pojo.api.BaseApiRequest;

public class ChangePasswordReq extends BaseApiRequest implements java.io.Serializable {
    private String newpwd;//新密码
    private String pwd;//旧密码
    private String loginname;//操作员工号
    private String deptid;//部门

    public String getNewpwd() {
        return newpwd;
    }

    public void setNewpwd(String newpwd) {
        this.newpwd = newpwd;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getLoginname() {
        return loginname;
    }

    public void setLoginname(String loginname) {
        this.loginname = loginname;
    }

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
}
