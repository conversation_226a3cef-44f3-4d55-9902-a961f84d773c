package com.maywide.biz.pay.unify.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.pojo.bizConfPay.BizConfirmaPayReq;
import com.maywide.biz.inter.pojo.bizConfPay.BizConfirmaPayResp;
import com.maywide.biz.inter.pojo.bizGroupActivate.BizGroupActiveReq;
import com.maywide.biz.inter.pojo.bizGroupActivate.BizGroupActiveResp;
import com.maywide.biz.inter.pojo.bizInstallCommit.BizInstallCommitReq;
import com.maywide.biz.inter.pojo.bizInstallCommit.BizInstallCommitResp;
import com.maywide.biz.inter.pojo.bizSaleCmit.BizSaleCommitReq;
import com.maywide.biz.inter.pojo.chargeFeeBook.QuePayOrderResp;
import com.maywide.biz.inter.pojo.chgdev.ChgDevWGReq;
import com.maywide.biz.inter.pojo.equipInfoSubmit.EquipInfoSubmitReq;
import com.maywide.biz.inter.pojo.equipInfoSubmit.EquipInfoSubmitResp;
import com.maywide.biz.inter.pojo.gridAddrMove.BizCustAddrCommitReq;
import com.maywide.biz.inter.pojo.gridAddrMove.BizCustAddrCommitResp;
import com.maywide.biz.inter.pojo.handlerSupplementGather.HandlerSupplementGatherReq;
import com.maywide.biz.inter.pojo.handlerSupplementGather.HandlerSupplementGatherResp;
import com.maywide.biz.inter.pojo.ordercommit.OrderCommitInterReq;
import com.maywide.biz.inter.pojo.ordercommit.OrderCommitInterResp;
import com.maywide.biz.inter.service.*;
import com.maywide.biz.market.entity.BizOrderSource;
import com.maywide.biz.market.entity.BizPortalOrder;
import com.maywide.biz.pay.unify.entity.PayRecordLog;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.util.BeanUtils;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class PayNoticeService extends CommonService {

	@Autowired
	private InterPrdService interPrdService;
	@Autowired
	private ChgDevService chgDevService;
	@Autowired
	private InterConfirmaPayService interConfirmPayService;
	@Autowired
	private InterSyncApplyService interSyncApplyService;
	@Autowired
	private InterResSaleService resSaleService;
	@Autowired
	private GridAddrMoveService gridAddrMoveService;
	
	@Autowired
	private CustBizOrderPoolService custBizOrderPoolService;
	
	@Autowired
	private SupplementGatheringService supplementGatheringService;

	public void bizSaleCommit(String orderid,QuePayOrderResp order, String extra,String multipaywayflag,String cashe) throws Exception {
		BizSaleCommitReq req = new BizSaleCommitReq();
		req.setOrderid(orderid);
		req.setPayway(order.getPayway());
		req.setWgpayway(BizConstant.SysPayway.SYS_PAYWAY_UNIFY);
		req.setPaycode(order.getBossPaycode());
		req.setPayreqid(order.getPaySerialNo());
		req.setBizorderid(getNewBizOrderid());
		req.setMultipaywayflag(multipaywayflag);
		req.setCashe(cashe);

		StringBuffer sql = new StringBuffer("");
		List<Object> param = new ArrayList<>();
		sql.append("select  recid,orderid,code,content from biz_order_source where orderid = ? and code = 'BIZ_PARTS_SALES'");
		param.add(orderid);
		List<BizOrderSource> list = getDAO().find(sql.toString(), BizOrderSource.class,param.toArray());
		if(list!=null && list.size()>=0){
			JSONObject json = new JSONObject(list.get(0).getContent());
			String type = (String) json.get("installtype");
			String time = (String) json.get("installtime");
			String houseid = (String) json.get("houseid");
			if(StringUtils.isNotBlank(time)){
				req.setInstalltime(time);
			}
			if(StringUtils.isNotBlank(type)){
				req.setInstalltype(type);
			}
			if(StringUtils.isNotBlank(houseid)&& type.equals("1")){
				req.setHouseid(houseid);
			}
		}
		ReturnInfo returnInfo = resSaleService.bizSaleCommit(req);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("订单确认失败：" + returnInfo.getMessage());
		}
	}

	public void bizCustAddrCommit(String orderid,QuePayOrderResp order,String multipaywayflag,String cashe) throws Exception {
		BizCustAddrCommitReq req = new BizCustAddrCommitReq();
		BizCustAddrCommitResp resp = new BizCustAddrCommitResp();
		req.setCustOrderid(orderid);
		req.setPayway(order.getPayway());
		req.setWgpayway(BizConstant.SysPayway.SYS_PAYWAY_UNIFY);
		req.setPaycode(order.getBossPaycode());
		req.setPayreqid(order.getPaySerialNo());
		req.setBizorderid(getNewBizOrderid());
		req.setMultipaywayflag(multipaywayflag);
		req.setCashe(cashe);
		ReturnInfo returnInfo = gridAddrMoveService.bizCustAddrCommit(req,resp);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("订单确认失败：" + returnInfo.getMessage());
		}
	}

	public void orderCommit(QuePayOrderResp order, String extra, String multipaywayflag, String cashe, com.alibaba.fastjson.JSONObject bizCustomInfo) throws Exception {
		OrderCommitInterReq req = new OrderCommitInterReq();
		OrderCommitInterResp resp = new OrderCommitInterResp();
		req.setWgpayway(BizConstant.SysPayway.SYS_PAYWAY_UNIFY);
		req.setBizorderid(getNewBizOrderid());
		req.setCustorderid(order.getOrderNo());
		req.setPayway(order.getPayway());
		req.setPaycode(order.getBossPaycode());
		req.setPayreqid(order.getPaySerialNo());
		req.setMultipaywayflag(multipaywayflag);
		req.setCashe(cashe);
		req.setGwPayOrderInfo(bizCustomInfo);
		if (StringUtils.isNotBlank(extra)) {
			ObjectMapper jsonMapper = new ObjectMapper();
			jsonMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			String extra2 = extra.replace("\\\"", "\"");
			JSONObject extraObj = new JSONObject(extra2);
			req.setMobile(extraObj.optString("mobile"));
			req.setSmartCardNo(extraObj.optString("smartCardNo"));
		}

		ReturnInfo returnInfo = interPrdService.doOrderCommit(req, resp);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("订单确认失败：" + returnInfo.getMessage());
		}
	}

	public void changeDevice(QuePayOrderResp order, String extra,String multipaywayflag,String cashe) throws Exception {
		ChgDevWGReq req = new ChgDevWGReq();
		req.setWgpayway(BizConstant.SysPayway.SYS_PAYWAY_UNIFY);
		req.setBizorderid(getNewBizOrderid());
		req.setCustorderid(order.getOrderNo());
		req.setPayway(order.getPayway());
		req.setPaycode(order.getBossPaycode());
		req.setPayreqid(order.getPaySerialNo());
		req.setMultipaywayflag(multipaywayflag);
		req.setCashe(cashe);
		String installtype = "0";
		req.setInstalltype(installtype);
		if (StringUtils.isNotBlank(extra)) {
			String extra2 = extra.replace("\\\"", "\"");
			JSONObject extraObj = new JSONObject(extra2);
			req.setCustid(extraObj.optString("custid"));
			req.setKeyno(extraObj.optString("keyno"));
		}

		ReturnInfo returnInfo = chgDevService.save(req);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("设备更换确认失败：" + returnInfo.getMessage());
		}
	}

	public void bizConfirmaPay(QuePayOrderResp order, String extra) throws Exception {
		BizConfirmaPayReq req = new BizConfirmaPayReq();
		BizConfirmaPayResp resp = new BizConfirmaPayResp();
		req.setWgpayway(BizConstant.SysPayway.SYS_PAYWAY_UNIFY);
		req.setBizorderid(getNewBizOrderid());
		req.setOrderid(Long.parseLong(order.getOrderNo()));
		req.setPayway(order.getPayway());
		req.setPaycode(order.getBossPaycode());
		req.setPayreqid(order.getPaySerialNo());
		req.setFees(String.valueOf(order.getTotalFee()));
		req.setRpay(String.valueOf(order.getTotalFee()));

		ReturnInfo returnInfo = interConfirmPayService.bizConfirmaPay(req, resp);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("未收款确认失败：" + returnInfo.getMessage());
		}
	}

	public void bizInstallCommit(QuePayOrderResp order, String extra, String multipaywayflag, String cashe, com.alibaba.fastjson.JSONObject bizCustomInfo) throws Exception {
		BizInstallCommitReq req = new BizInstallCommitReq();
		BizInstallCommitResp resp = new BizInstallCommitResp();
		req.setWgpayway(BizConstant.SysPayway.SYS_PAYWAY_UNIFY);
		req.setBizorderid(getNewBizOrderid());
		req.setCustOrderid(Long.parseLong(order.getOrderNo()));
		req.setPayway(order.getPayway());
		req.setPaycode(order.getBossPaycode());
		req.setPayreqid(order.getPaySerialNo());
		req.setMultipaywayflag(multipaywayflag);
		req.setCashe(cashe);

		// 国网信息
		req.setGwPayOrderInfo(bizCustomInfo);

		ReturnInfo returnInfo = interSyncApplyService.bizInstallCommit(req, resp);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("开户失败：" + returnInfo.getMessage());
		}
	}

	private String getNewBizOrderid() throws Exception {
//		return getDAO().getSequence("SEQ_BIZ_CUSTORDER_ID").toString();
		return getBizorderid();
	}

	public void handlerSupplementGather(String bizorderId, String fees) throws Exception {
		HandlerSupplementGatherReq req = new HandlerSupplementGatherReq();
		req.setBizOrderId(Long.parseLong(bizorderId));
		req.setFees(fees);
		HandlerSupplementGatherResp resp = new HandlerSupplementGatherResp();
		ReturnInfo returnInfo = supplementGatheringService.handlerSupplementGather(req, resp);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("补缴失败：" + returnInfo.getMessage());
		}
	}

	public PayRecordLog recordPay(String orderid) {
		try {
			PayRecordLog recordLog = new PayRecordLog();
			recordLog.setOrderid(Long.valueOf(orderid));
			recordLog.setPayresult("0");
			recordLog.setPaydate(new Date());
			recordLog.setActionType("01");
			DAO.save(recordLog);
			return recordLog;
		} catch (Exception e) {
			log.error("=============create paylog fail ================:"+e.getMessage());
		}
		return null;
	}

	public PayRecordLog recordPay(String orderid, String custorderid) {
		try {
			PayRecordLog recordLog = new PayRecordLog();
			recordLog.setOrderid(Long.valueOf(orderid));
			recordLog.setPayresult("0");
			recordLog.setPaydate(new Date());
			recordLog.setActionType("01");
			// 新增 custorderid
			if (StringUtils.isNotBlank(custorderid)) {
				recordLog.setCustorderid(Long.valueOf(custorderid));
			}
			DAO.save(recordLog);
			return recordLog;
		} catch (Exception e) {
			log.error("=============create paylog fail ================:"+e.getMessage());
		}
		return null;
	}

	public void updatePayRecordStatus(PayRecordLog recordLog) {
		if(recordLog == null) return;
		try {
			DAO.saveOrUpdate(recordLog);
		} catch (Exception e) {
			log.error("=============update recordlog fail ================"+e.getMessage());
		}
		
	}
	/**
	 * 业务工单提交接口
	 * @param orderid
	 * @param rpay
	 * @param payway
	 * @param subpay
	 * @throws Exception
	 */
	public void submitBizCustOrder(String orderid,String rpay,String payway,String subpay) throws Exception {
		EquipInfoSubmitReq apiReq = new EquipInfoSubmitReq();
		apiReq.setBizorderid(getNewBizOrderid());
		apiReq.setServorderid(Long.parseLong(orderid));
		apiReq.setPayway(payway);
		apiReq.setRpay(rpay);
		apiReq.setSubpay(subpay);
		EquipInfoSubmitResp resp = new EquipInfoSubmitResp();
		ReturnInfo returnInfo = custBizOrderPoolService.equipInfoSubmit(apiReq,resp);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("业务工单设备分配失败：" + returnInfo.getMessage());
		}
	}

	/**
	 * 业务工单提交接口 20200628 新增payid
	 * @param orderid
	 * @param rpay
	 * @param payway
	 * @param subpay
	 * @throws Exception
	 */
	public void submitBizCustOrder(String orderid,String rpay,String payway,String subpay, String payid) throws Exception {
		EquipInfoSubmitReq apiReq = new EquipInfoSubmitReq();
		apiReq.setBizorderid(getNewBizOrderid());
		apiReq.setServorderid(Long.parseLong(orderid));
		apiReq.setPayway(payway);
		apiReq.setRpay(rpay);
		apiReq.setSubpay(subpay);
		apiReq.setPayid(Long.parseLong(payid));
		EquipInfoSubmitResp resp = new EquipInfoSubmitResp();
		ReturnInfo returnInfo = custBizOrderPoolService.equipInfoSubmit(apiReq,resp);
		if (returnInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
			throw new BusinessException("业务工单设备分配失败：" + returnInfo.getMessage());
		}
	}

	public void recordFaidOrder(PayRecordLog recordLog) throws Exception {
		PayRecordLog payRecordLog = new PayRecordLog();
		payRecordLog.setFees(recordLog.getFees());
		payRecordLog.setOrderid(recordLog.getOrderid());
		payRecordLog.setPaydate(new Date());
		payRecordLog.setPayresult("2");
		payRecordLog.setActionType("02");
		payRecordLog.setCustorderid(recordLog.getCustorderid());
		getDAO().save(payRecordLog);

	}

	public void recordFaidOrder(String orderid) throws Exception {
		BizPortalOrder portalOrder = getPortalOrder(Long.parseLong(orderid));
		if(portalOrder != null){
			PayRecordLog payRecordLog = new PayRecordLog();
			payRecordLog.setFees(portalOrder.getPayFees());
			payRecordLog.setOrderid(portalOrder.getId());
			payRecordLog.setPaydate(new Date());
			payRecordLog.setPayresult("2");
			payRecordLog.setActionType("02");
			getDAO().save(payRecordLog);
		}

	}

	public void updatePayId(Long orderid) {
		BizPortalOrder bizPortalOrder = getPortalOrder(orderid);
		if(bizPortalOrder != null){
			bizPortalOrder.setPayid(getSnowflakeOrderId());
			try {
				getDAO().update(bizPortalOrder);
			} catch (Exception e) {
				log.error("update bizportalorder ["+orderid+"] payid faild!!!");
			}
		}else{

		}
	}

	private BizPortalOrder getPortalOrder(Long orderid){
		try {
			BizPortalOrder portalOrder = (BizPortalOrder) getDAO().find(BizPortalOrder.class,orderid);
			return portalOrder;
		} catch (Exception e) {
			return null;
		}
	}

	public void bizGroupActivation(QuePayOrderResp order, com.alibaba.fastjson.JSONObject bizCustomInfo) throws Exception {
		BizGroupActiveReq req = new BizGroupActiveReq();
		BizGroupActiveResp resp = new BizGroupActiveResp();
		req.setWgpayway(order.getPayway());
		req.setBizorderid(getNewBizOrderid());
		req.setCustOrderid(Long.parseLong(order.getOrderNo()));
		req.setPayway(order.getPayway());
		req.setPaycode(order.getBossPaycode());
		req.setPayreqid(order.getPaySerialNo());

		// 国网信息
		req.setGwPayOrderInfo(bizCustomInfo);

		ReturnInfo returnInfo = interSyncApplyService.bizGroupActivation(req, resp);
	}

}
