package com.maywide.biz.pay.task.entity;

import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * GridBizExceptionRecord
 *
 * <AUTHOR>
 * @date 2022/11/25 15:52
 * @since 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Entity
@Table(name = "biz_exception_record")
public class BizExceptionRecord implements Serializable {

    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private Long id; // id
    private String bizCode; // 业务编码
    private String bizName; // 业务名称
    private Date createTime; // 创建时间
    private String custId; // 客户id
    private String custName; // 客户名称
    private String operid; // 操作员ID
    private String opername; // 操作员名称
    private String operdeptid; // 操作部门id
    private String operdeptname; // 操作部门名称
    private String errorDesc; // 错误描述
    private String bizParam; // 业务参数
    private String orderId; // 订单ID
    private String orderNo; // 订单编号
    private String fee; // 支付金额
    private String payStatus; // 支付状态
    private String payMethod; // 支付方式
    private String serialNo; // 流水号
    private Date payTime; // 支付时间
    private String submitStatus; // 订单提交状态
    private Date submitTime; // 提交时间
    private String readStatus; // 是否已读

}
