package com.maywide.biz.pay.gzboss.pojo;

import java.io.Serializable;
import java.util.List;

public class GwOrderTradeBo implements Serializable{
	private String peer_trade_id;	//子订单号,由调用方自定义，仅支持字母、数字、下划线且需保证在不重复
	private String trade_desc;	//子订单描述
	private List<GwOrderItemBo> item_list;	//订单费用明细列表, （比如一次下单支付多个商品的费用,则ITEM_LIST里面有多个对象）



	/******响应报文内容 开始******/
	private String detail_id;	//交易明细ID
	private String trade_amount;	//交易明细金额
	/******响应报文内容 结束******/



	public String getPeer_trade_id(){
		return peer_trade_id;
	}
	public String getDetail_id(){
		return detail_id;
	}
	public String getTrade_amount(){
		return trade_amount;
	}
	public void setDetail_id(String detail_id){
		this.detail_id = detail_id;
	}
	public void setTrade_amount(String trade_amount){
		this.trade_amount = trade_amount;
	}
	public String getTrade_desc(){
		return trade_desc;
	}
	public List<GwOrderItemBo> getItem_list(){
		return item_list;
	}
	public void setPeer_trade_id(String peer_trade_id){
		this.peer_trade_id = peer_trade_id;
	}
	public void setTrade_desc(String trade_desc){
		this.trade_desc = trade_desc;
	}
	public void setItem_list(List<GwOrderItemBo> item_list){
		this.item_list = item_list;
	}


}
