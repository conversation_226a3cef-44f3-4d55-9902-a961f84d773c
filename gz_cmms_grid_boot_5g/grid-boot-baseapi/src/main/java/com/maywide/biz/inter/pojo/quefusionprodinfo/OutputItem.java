package com.maywide.biz.inter.pojo.quefusionprodinfo;

import java.util.Date;

/**
 * OutputItem 是 output 列表中每个元素的 Java 对象。
 * 它包含了与 JSON 结构对应的字段及其注释说明。
 */
public class OutputItem {

    /**
     * contractperiod: 合同周期
     */
    private String contractperiod;

    /**
     * create_date: 创建日期，格式为 "yyyy-MM-dd HH:mm:ss"。
     */
    private Date create_date;

    /**
     * expire_date: 过期日期，格式为 "yyyy-MM-dd HH:mm:ss"。
     */
    private Date expire_date;

    /**
     * fee: 费用
     */
    private String fee;

    /**
     * is_main: 是否为主套餐的标志位。
     */
    private String is_main;

    /**
     * istemppay: 是否为临时支付的标志位
     */
    private String istemppay;

    /**
     * mpid: 消息处理 ID
     */
    private String mpid;

    /**
     * mservid: 消息服务 ID
     */
    private String mservid;

    /**
     * offer_code: 套餐代码
     */
    private String offer_code;

    /**
     * offer_ins_id: 套餐实例 ID
     */
    private String offer_ins_id;

    /**
     * offer_name: 套餐名称。
     */
    private String offer_name;

    /**
     * offertype: 套餐类型
     */
    private String offertype;

    /**
     * pid: 产品 ID
     */
    private String pid;

    /**
     * prestoremoney: 预存款金额
     */
    private String prestoremoney;

    /**
     * price: 套餐价格
     */
    private String price;

    /**
     * real_fee: 实际费用
     */
    private String real_fee;

    /**
     * rel_offer_code: 关联套餐代码
     */
    private String rel_offer_code;

    /**
     * rel_offer_code_name: 关联套餐代码名称
     */
    private String rel_offer_code_name;

    /**
     * salesid: 销售 ID
     */
    private String salesid;

    /**
     * swHasFlag: 开关标志位
     */
    private String swHasFlag;

    /**
     * valid_date: 生效日期，格式为 "yyyy-MM-dd HH:mm:ss"。
     */
    private Date valid_date;

    // Getter 和 Setter 方法

    public String getContractperiod() {
        return contractperiod;
    }

    public void setContractperiod(String contractperiod) {
        this.contractperiod = contractperiod;
    }

    public Date getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Date create_date) {
        this.create_date = create_date;
    }

    public Date getExpire_date() {
        return expire_date;
    }

    public void setExpire_date(Date expire_date) {
        this.expire_date = expire_date;
    }

    public String getFee() {
        return fee;
    }

    public void setFee(String fee) {
        this.fee = fee;
    }

    public String getIs_main() {
        return is_main;
    }

    public void setIs_main(String is_main) {
        this.is_main = is_main;
    }

    public String getIstemppay() {
        return istemppay;
    }

    public void setIstemppay(String istemppay) {
        this.istemppay = istemppay;
    }

    public String getMpid() {
        return mpid;
    }

    public void setMpid(String mpid) {
        this.mpid = mpid;
    }

    public String getMservid() {
        return mservid;
    }

    public void setMservid(String mservid) {
        this.mservid = mservid;
    }

    public String getOffer_code() {
        return offer_code;
    }

    public void setOffer_code(String offer_code) {
        this.offer_code = offer_code;
    }

    public String getOffer_ins_id() {
        return offer_ins_id;
    }

    public void setOffer_ins_id(String offer_ins_id) {
        this.offer_ins_id = offer_ins_id;
    }

    public String getOffer_name() {
        return offer_name;
    }

    public void setOffer_name(String offer_name) {
        this.offer_name = offer_name;
    }

    public String getOffertype() {
        return offertype;
    }

    public void setOffertype(String offertype) {
        this.offertype = offertype;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getPrestoremoney() {
        return prestoremoney;
    }

    public void setPrestoremoney(String prestoremoney) {
        this.prestoremoney = prestoremoney;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getReal_fee() {
        return real_fee;
    }

    public void setReal_fee(String real_fee) {
        this.real_fee = real_fee;
    }

    public String getRel_offer_code() {
        return rel_offer_code;
    }

    public void setRel_offer_code(String rel_offer_code) {
        this.rel_offer_code = rel_offer_code;
    }

    public String getRel_offer_code_name() {
        return rel_offer_code_name;
    }

    public void setRel_offer_code_name(String rel_offer_code_name) {
        this.rel_offer_code_name = rel_offer_code_name;
    }

    public String getSalesid() {
        return salesid;
    }

    public void setSalesid(String salesid) {
        this.salesid = salesid;
    }

    public String getSwHasFlag() {
        return swHasFlag;
    }

    public void setSwHasFlag(String swHasFlag) {
        this.swHasFlag = swHasFlag;
    }

    public Date getValid_date() {
        return valid_date;
    }

    public void setValid_date(Date valid_date) {
        this.valid_date = valid_date;
    }

    @Override
    public String toString() {
        return "OutputItem{" +
                "contractperiod='" + contractperiod + '\'' +
                ", create_date=" + create_date +
                ", expire_date=" + expire_date +
                ", fee='" + fee + '\'' +
                ", is_main='" + is_main + '\'' +
                ", istemppay='" + istemppay + '\'' +
                ", mpid='" + mpid + '\'' +
                ", mservid='" + mservid + '\'' +
                ", offer_code='" + offer_code + '\'' +
                ", offer_ins_id='" + offer_ins_id + '\'' +
                ", offer_name='" + offer_name + '\'' +
                ", offertype='" + offertype + '\'' +
                ", pid='" + pid + '\'' +
                ", prestoremoney='" + prestoremoney + '\'' +
                ", price='" + price + '\'' +
                ", real_fee='" + real_fee + '\'' +
                ", rel_offer_code='" + rel_offer_code + '\'' +
                ", rel_offer_code_name='" + rel_offer_code_name + '\'' +
                ", salesid='" + salesid + '\'' +
                ", swHasFlag='" + swHasFlag + '\'' +
                ", valid_date=" + valid_date +
                '}';
    }
}