package com.maywide.biz.prd.service;

import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.entity.Rule;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.service.ParamService;
import com.maywide.biz.core.service.RuleService;
import com.maywide.biz.prd.dao.CatalogDao;
import com.maywide.biz.prd.entity.Catalog;
import com.maywide.biz.prd.entity.CatalogCondtion;
import com.maywide.biz.prd.entity.CatalogItem;
import com.maywide.biz.prd.entity.SalespkgKnow;
import com.maywide.biz.prv.entity.PrvDepartment;
import com.maywide.biz.prv.entity.PrvOperator;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.dao.BaseDao;
import com.maywide.core.exception.ServiceException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.service.BaseService;
import com.maywide.core.service.PersistentService;
import com.maywide.core.util.CheckUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional(rollbackFor = Exception.class)
public class CatalogService extends BaseService<Catalog, Long> {
	public final static String CONDTION_TYPE_OPER = "0";
	public final static String CONDTION_TYPE_OPCODE="2";
	
	@Autowired
	private CatalogDao catalogDao;
	
	@Autowired
	private PersistentService persistentService;
	
	@Autowired
	private RuleService ruleService;
	
	@Autowired
	private ParamService paramService;
	
	@Override
    protected BaseDao<Catalog, Long> getEntityDao() {
        return catalogDao;
    }
	
	@Override
	public Catalog save(Catalog entity){
		LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
		if(entity.isNew()){
			entity.setCreateoper(loginInfo.getOperid());
			entity.setCreatetime(new Date());
		} else {
			entity.setUpdateoper(loginInfo.getOperid());
			entity.setUpdatetime(new Date());
		}
		
		super.save(entity);
		
		return entity;
	}
	
	public void transCondtionList(List<CatalogCondtion> list) throws Exception {
		for (CatalogCondtion condtion : list) {
			transCondtion(condtion);
		}
	}
	
	public void transCondtion(CatalogCondtion condtion) throws Exception {
		condtion.addExtraAttribute("condtionvalue", getCondtionname(condtion.getContiontype(), condtion.getContionvalue()));
	}
	
	public void transKnowList(List<CatalogItem> list) throws Exception {
		for (CatalogItem condtion : list) {
			transKnow(condtion);
		}
	}
	
	public void transKnow(CatalogItem item) throws Exception {
		SalespkgKnow know = (SalespkgKnow) persistentService.find(SalespkgKnow.class, new Long(item.getKnowid().toString()));
		if (know != null) {
			item.addExtraAttribute("knowname", know.getKnowname());
			//查询营销知识库编码
			StringBuffer sql=new StringBuffer();
			List<Object> paramList = new ArrayList<Object>();
			sql.append(" SELECT id,knowname,objcode,objname FROM ( SELECT  o.knowid AS id, o.KNOWNAME AS knowname, ");
			sql.append(" p.PCODE AS objcode,p.PNAME AS objname FROM PRD_SALESPKG_KNOW o, PRD_PCODE p WHERE o.objid = p.PID");
			sql.append(" AND o.OBJTYPE IN ( '0' ) UNION ALL SELECT o.knowid AS id,o.KNOWNAME AS knowname,p.SALESCODE AS objcode, ");
			sql.append(" p.SALESNAME AS objname FROM PRD_SALESPKG_KNOW o, PRD_SALES p WHERE o.objid = p.salesid AND o.OBJTYPE IN ( '3', '4' ) UNION ALL");
			sql.append(" SELECT o.knowid AS id, o.KNOWNAME AS knowname, p.SALESPKGCODE AS objcode, p.SALESPKGNAME AS objname");
			sql.append(" FROM PRD_SALESPKG_KNOW o, PRD_SALESPKG p WHERE o.objid = p.SALESPKGID AND o.OBJTYPE IN ( '1' ) ) tb ");
			sql.append(" WHERE 1 = 1  AND tb.id = ? ORDER BY tb.objcode  ");
			paramList.add(know.getKnowid());

			List<SalespkgKnow> salespkgKnowList = persistentService.find(sql.toString(), SalespkgKnow.class, paramList.toArray());

			item.addExtraAttribute("objcode", salespkgKnowList.get(0).getObjcode());
		}
	}
	
	private String getCondtionname(String condtiontype, String condtionvalue) {
		try {
			if (CONDTION_TYPE_OPER.equals(condtiontype)) {
				PrvOperator oper = (PrvOperator) persistentService.find(PrvOperator.class, Long.parseLong(condtionvalue));
				if (oper != null) {
					return oper.getLoginname();
				}
			}//增加业务操作码限制
			else if(CONDTION_TYPE_OPCODE.equals(condtiontype)){
				PrvSysparam param = paramService.getData(BizConstant.SysparamGcode.BIZ_OPCODE,condtionvalue);
				if (param != null) {
					return param.getMname();
				}
			}else {
				PrvDepartment department = (PrvDepartment) persistentService.find(PrvDepartment.class, Long.parseLong(condtionvalue));
				if (department != null) {
					return department.getName();
				}
			}
		} catch (Exception e) {
			throw new ServiceException(e.getMessage());
		}
		return "";
	}

	public Map<String, String> findBizopcodesMap() throws Exception {
		Map<String, String> bizopcodeMap = new LinkedHashMap<String, String>();
		LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
		
		//1.先获取地市的规则
		Rule rule = ruleService.getRule(BizConstant.BizRuleParams.GOODS_BIZCODES,loginInfo.getCity(), BizConstant.BizRuleParams.PERMISSION_TYPE_Y);
		//如果为空，获取默认所有地市的规则
		if(null==rule){
			rule =  ruleService.getRule(BizConstant.BizRuleParams.GOODS_BIZCODES, BizConstant.BizRuleParams.DEFAULTCITY, BizConstant.BizRuleParams.PERMISSION_TYPE_Y);
		}
		CheckUtils.checkNull(rule, "目录关联业务操作规则未配置，请联系管理员先配置");
		
		String opcodeStr = rule.getValue();
		String[] opcodeMcodes = opcodeStr.split(",");
		
		List<PrvSysparam> params = paramService.getData(BizConstant.SysparamGcode.BIZ_OPCODE);
		//bizopcodeMap.put("*", "全部");
		for(String mcode:opcodeMcodes){
			for(PrvSysparam param : params){
				if(param.getMcode().equals(mcode)){
					bizopcodeMap.put(mcode, param.getMname());
					break;
				}
			}
		}
		
		return bizopcodeMap;
	}
	
}
