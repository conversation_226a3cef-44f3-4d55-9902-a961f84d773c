package com.maywide.biz.inter.pojo.integral.queOperIntegral;

public class IntegralBO {
    private double retentionIntegral;
    private double saleIntegral;
    private double installIntegral;
    private double otherIntegral;
    private double sumIntegral;

    public double getRetentionIntegral() {
        return retentionIntegral;
    }

    public void setRetentionIntegral(double retentionIntegral) {
        this.retentionIntegral = retentionIntegral;
    }

    public double getSaleIntegral() {
        return saleIntegral;
    }

    public void setSaleIntegral(double saleIntegral) {
        this.saleIntegral = saleIntegral;
    }

    public double getInstallIntegral() {
        return installIntegral;
    }

    public void setInstallIntegral(double installIntegral) {
        this.installIntegral = installIntegral;
    }

    public double getOtherIntegral() {
        return otherIntegral;
    }

    public void setOtherIntegral(double otherIntegral) {
        this.otherIntegral = otherIntegral;
    }

    public double getSumIntegral() {
        return sumIntegral;
    }

    public void setSumIntegral(double sumIntegral) {
        this.sumIntegral = sumIntegral;
    }
}
