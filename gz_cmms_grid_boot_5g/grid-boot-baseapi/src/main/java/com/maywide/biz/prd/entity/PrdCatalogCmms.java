package com.maywide.biz.prd.entity;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "prd_catalog_cmms")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class PrdCatalogCmms implements Serializable {

    private Long recid; //bigint(20) NOT NULL AUTO_INCREMENT COMMENT '目录主键',
    private String name; //varchar(128) NOT NULL COMMENT '目录名称',
    private Integer status; //int(11) NOT NULL COMMENT '目录状态',
    private String areas; //varchar(1024) NOT NULL COMMENT '所属业务区',
    private String city; //varchar(2) DEFAULT NULL COMMENT '所属地市',
    private Integer type; //int(11) DEFAULT NULL COMMENT '目录类型',
    private Integer sort; //int(11) DEFAULT NULL COMMENT '排序/优先级',
    private String category; //varchar(32) DEFAULT NULL COMMENT '分类',
    private Date createtime; //datetime DEFAULT NULL COMMENT '创建时间',
    private Long creator; //bigint(20) DEFAULT NULL COMMENT '创建人',
    private Date updatetime; //datetime DEFAULT NULL COMMENT '最后修改时间',
    private Long updator; //bigint(20) DEFAULT NULL COMMENT '最后修复人',
    private String desc; //varchar(2048) DEFAULT NULL COMMENT '描述/备注',
    private String condition; //varchar(2048) DEFAULT NULL COMMENT '目录条件',
    private String extra; //varchar(1024) DEFAULT NULL COMMENT '预留扩展字段',

    private String purview; //varchar(80) DEFAULT NULL COMMENT '权限范围',

    private String jobnums; // text DEFAULT NULL COMMENT '工号',

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "recid")
    public Long getRecid() {
        return recid;
    }

    public void setRecid(Long recid) {
        this.recid = recid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAreas() {
        return areas;
    }

    public void setAreas(String areas) {
        this.areas = areas;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name = "`type`",nullable = false)
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public Long getUpdator() {
        return updator;
    }

    public void setUpdator(Long updator) {
        this.updator = updator;
    }

    @Column(name = "`desc`",nullable = false)
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Column(name = "`condition`",nullable = false)
    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getPurview() {
        return purview;
    }

    public void setPurview(String purview) {
        this.purview = purview;
    }

    public String getJobnums() {
        return jobnums;
    }

    public void setJobnums(String jobnums) {
        this.jobnums = jobnums;
    }
}
