package com.maywide.biz.inter.service;

import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.pojo.searchRecord.AddSearchRecordReq;
import com.maywide.biz.inter.pojo.searchRecord.DelSearchRecordReq;
import com.maywide.biz.inter.pojo.searchRecord.QuerySearchResp;
import com.maywide.biz.inter.pojo.searchRecord.SearchRecordResp;
import com.maywide.biz.market.entity.BizRecord;
import com.maywide.biz.market.entity.BizSearchRecord;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class SearchRecordService  extends CommonService
{
    public ReturnInfo querySearchRecord(QuerySearchResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        StringBuffer sql = new StringBuffer();
        List param = new ArrayList();
        sql.append(" SELECT * FROM biz_search_record WHERE operid = ?  ORDER BY updatetime DESC LIMIT 10 ");
        param.add(loginInfo.getOperid());
        List<BizRecord> bizSearchRecordList = getDAO().find(sql.toString(), BizRecord.class, param.toArray());
        resp.setBizSearchRecordList(bizSearchRecordList);
        return returnInfo;
    }


    public ReturnInfo addSearchRecord(AddSearchRecordReq req, SearchRecordResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        try {
            StringBuffer sql = new StringBuffer();
            List param = new ArrayList();
            sql.append(" SELECT * FROM biz_search_record WHERE operid = ? AND quekeyword = ? ");
            param.add(loginInfo.getOperid());
            param.add(req.getQuekeyword());
            if (StringUtils.isNotBlank(req.getAreaids())){
                sql.append(" and areaids = ? ");
                param.add(req.getAreaids());
            }else {
                sql.append(" and areaids is null ");
            }

            List<BizSearchRecord> bizSearchRecords = getDAO().find(sql.toString(), BizSearchRecord.class, param.toArray());
            if (bizSearchRecords.size() > 0){
                StringBuffer sqlupdate = new StringBuffer();
                List paramupdate = new ArrayList();
                sqlupdate.append(" UPDATE biz_search_record SET updatetime = ?  WHERE operid = ?  AND quekeyword = ? ");
                paramupdate.add(new Date());
                paramupdate.add(loginInfo.getOperid());
                paramupdate.add(req.getQuekeyword());
                if (StringUtils.isNotBlank(req.getAreaids())){
                sqlupdate.append(" and areaids = ? ");
                paramupdate.add(req.getAreaids());
                }else {
                    sqlupdate.append(" and areaids is null ");
                }
                getDAO().executeSql(sqlupdate.toString(),paramupdate.toArray());
            }else {
                BizSearchRecord bizSearchRecord = new BizSearchRecord();
                bizSearchRecord.setOperid(loginInfo.getOperid());
                bizSearchRecord.setQuekeyword(req.getQuekeyword());
                bizSearchRecord.setKeywordtype(req.getKeywordtype());
                bizSearchRecord.setCreatetime(new Date());
                bizSearchRecord.setUpdatetime(new Date());
                bizSearchRecord.setAreaids(req.getAreaids());
                getDAO().save(bizSearchRecord);
            }
            //保留操作员查询最新的十条数据，早期数据删除
            StringBuffer sqldel = new StringBuffer();
            List paramdel = new ArrayList();
            sqldel.append(" SELECT * FROM biz_search_record WHERE operid = ? ORDER BY updatetime ASC ");
            paramdel.add(loginInfo.getOperid());
            List<BizRecord> bizSearchRecordList = getDAO().find(sqldel.toString(), BizRecord.class, paramdel.toArray());
            if (bizSearchRecordList.size() >10 ){
                Long id = bizSearchRecordList.get(0).getRecid();
                StringBuffer sqldelid = new StringBuffer();
                List paramdelID = new ArrayList();
                sqldelid.append(" delete from biz_search_record where recid = ? ");
                paramdelID.add(id);
                getDAO().executeSql(sqldelid.toString(),paramdelID.toArray());
            }
            resp.setStatus("Y");
        } catch (Exception e) {
            resp.setStatus("N");
            resp.setErrMessage(e.getMessage());
        }
        return returnInfo;
    }


    public ReturnInfo delSearchRecord(DelSearchRecordReq req , SearchRecordResp resp) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        StringBuffer sql = new StringBuffer();
        List param = new ArrayList();
        sql.append(" DELETE  FROM biz_search_record WHERE recid in ( ");
        Long[] ids = req.getIds();
        if (ids.length == 0){
            throw new BusinessException("id不能为空");
        }
        for (int i = 0; i < ids.length; i++) {
            sql.append(ids[i]);
            if (i < ids.length-1){
                sql.append(",");
            }
        }
        sql.append(")");
        try {
            getDAO().executeSql(sql.toString(),param.toArray());
            resp.setStatus("Y");
        } catch (Exception e) {
            resp.setStatus("N");
            resp.setErrMessage(e.getMessage());
        }
        return returnInfo;
    }
}
