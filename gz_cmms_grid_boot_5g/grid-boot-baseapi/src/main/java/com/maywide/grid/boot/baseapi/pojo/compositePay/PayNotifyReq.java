package com.maywide.grid.boot.baseapi.pojo.compositePay;

/**
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date:2023/8/7 17:31
 */
public class PayNotifyReq {
    /**
     * 成功标志
     * SUCCESS/FAIL
     */
    private String successful;

    /**
     * 综合支付系统订单号
     */
    private String tradeNo;

    /**
     * 商户订单号
     * 最大长度为 16位的商户订单号
     */
    private String busiSeqNo;


    /**
     * 用户标识
     */
    private String userId;

    /**
     * 交易金额
     * 交易金额（分）
     */
    private String tradeMoney;


    /**
     * 签名方式
     */
    private String signType;

    /**
     * 签名
     */
    private String signValue;

    /**
     * 支付渠道
     * wechat : 原生微信
     * alipay: 原生支付宝
     * spdbank-alipay:浦发支付宝
     * spdbank-wechat:浦发微信
     * allinpaygx-alipay:浦发支付宝
     * allinpaygx-wechat:浦发微信
     */
    private String channelId;

    /**
     * 错误编码
     * 异常编码
     */
    private String errorCode;

    /**
     * 错误描述
     * 渠道支付错误描述
     */
    private String errorMsg;

    /**
     * 支付成功时间
     * 格式: **************
     */
    private String paySuccTime;

    /**
     * 业务信息
     */
    private String busiBody;

    /**
     * 退款金额
     */
    private String refundAmount;

    /**
     * 退款时间
     */
    private String refundSuccTime;

    private String backType;
    private String channelBody;
    private String channelTradeNo;


    public String getSuccessful() {
        return successful;
    }

    public void setSuccessful(String successful) {
        this.successful = successful;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getBusiSeqNo() {
        return busiSeqNo;
    }

    public void setBusiSeqNo(String busiSeqNo) {
        this.busiSeqNo = busiSeqNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTradeMoney() {
        return tradeMoney;
    }

    public void setTradeMoney(String tradeMoney) {
        this.tradeMoney = tradeMoney;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSignValue() {
        return signValue;
    }

    public void setSignValue(String signValue) {
        this.signValue = signValue;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getPaySuccTime() {
        return paySuccTime;
    }

    public void setPaySuccTime(String paySuccTime) {
        this.paySuccTime = paySuccTime;
    }

    public String getBusiBody() {
        return busiBody;
    }

    public void setBusiBody(String busiBody) {
        this.busiBody = busiBody;
    }

    public String getBackType() {
        return backType;
    }

    public void setBackType(String backType) {
        this.backType = backType;
    }

    public String getChannelBody() {
        return channelBody;
    }

    public void setChannelBody(String channelBody) {
        this.channelBody = channelBody;
    }

    public String getChannelTradeNo() {
        return channelTradeNo;
    }

    public void setChannelTradeNo(String channelTradeNo) {
        this.channelTradeNo = channelTradeNo;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRefundSuccTime() {
        return refundSuccTime;
    }

    public void setRefundSuccTime(String refundSuccTime) {
        this.refundSuccTime = refundSuccTime;
    }
}
