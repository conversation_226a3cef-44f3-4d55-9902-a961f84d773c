package com.maywide.grid.boot.baseapi.pojo.compositePay;


/**
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date:2023/8/7 14:33
 */
public class CompositePayHttpParam{
    /**
     * 商户号
     */
    private String merchantId;

    /**
     * WEB支付同步返回URL
     */
    private String notifyUrl;

    /**
     * 接口版本
     * 调用的接口版本，固定为：1.0
     */
    private String version;

    /**
     * 字符集
     * 目前只支持 UTF-8
     */
    private String _inputCharset;

    /**
     * 收银台类型
     * 微信、支付宝统一支付收银台标志固定13
     */
    private String payDeskMode;


    /**
     * 高标清
     * 固定为 0，高清
     */
    private String hdFlag;

    /**
     * 业务渠道号
     * 目前固定 1000
     */
    private String terminalChannel;


    /**
     * 账单前缀
     * 综合支付平台平台提供的固定值 GXTV
     */
    private String billPrefix;

    /**
     * 综合支付自带的统一支付页类型
     * 1：PC；2:机顶盒；3：手机；0：非统一支付页；默认为0
     */
    private String unifyPageType;

    /**
     * 交易类型
     * 0：缴费 1:购买增值产品
     */
    private String billType;

    /**
     * 业务类型
     * 默认: 商城
     */
    private String busiType;

    /**
     * 业务APPID
     */
    private String busiAppId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 业务信息
     */
    private String busiBody;

    /**
     * 用户标识
     */
    private String userId;

    /**
     * 交易金额
     * 交易金额（分）
     */
    private String tradeMoney;

    /**
     * 商户订单号
     * 最大长度为 16位的商户订单号
     */
    private String busiSeqNo;


    /**
     * 签名方式
     * 目前只支持 MD5 方式
     */
    private String signType;

    /**
     * 签名
     * 请求信息签名，生成方式见签名生成部分
     */
    private String signValue;


    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String get_inputCharset() {
        return _inputCharset;
    }

    public void set_inputCharset(String _inputCharset) {
        this._inputCharset = _inputCharset;
    }

    public String getPayDeskMode() {
        return payDeskMode;
    }

    public void setPayDeskMode(String payDeskMode) {
        this.payDeskMode = payDeskMode;
    }

    public String getHdFlag() {
        return hdFlag;
    }

    public void setHdFlag(String hdFlag) {
        this.hdFlag = hdFlag;
    }

    public String getTerminalChannel() {
        return terminalChannel;
    }

    public void setTerminalChannel(String terminalChannel) {
        this.terminalChannel = terminalChannel;
    }

    public String getBillPrefix() {
        return billPrefix;
    }

    public void setBillPrefix(String billPrefix) {
        this.billPrefix = billPrefix;
    }

    public String getUnifyPageType() {
        return unifyPageType;
    }

    public void setUnifyPageType(String unifyPageType) {
        this.unifyPageType = unifyPageType;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getBusiAppId() {
        return busiAppId;
    }

    public void setBusiAppId(String busiAppId) {
        this.busiAppId = busiAppId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getBusiBody() {
        return busiBody;
    }

    public void setBusiBody(String busiBody) {
        this.busiBody = busiBody;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTradeMoney() {
        return tradeMoney;
    }

    public void setTradeMoney(String tradeMoney) {
        this.tradeMoney = tradeMoney;
    }

    public String getBusiSeqNo() {
        return busiSeqNo;
    }

    public void setBusiSeqNo(String busiSeqNo) {
        this.busiSeqNo = busiSeqNo;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSignValue() {
        return signValue;
    }

    public void setSignValue(String signValue) {
        this.signValue = signValue;
    }


    @Override
    public String toString() {
        return "CompositePayHttpParam{" +
                "merchantId='" + merchantId + '\'' +
                ", notifyUrl='" + notifyUrl + '\'' +
                ", version='" + version + '\'' +
                ", _inputCharset='" + _inputCharset + '\'' +
                ", payDeskMode='" + payDeskMode + '\'' +
                ", hdFlag='" + hdFlag + '\'' +
                ", terminalChannel='" + terminalChannel + '\'' +
                ", billPrefix='" + billPrefix + '\'' +
                ", unifyPageType='" + unifyPageType + '\'' +
                ", billType='" + billType + '\'' +
                ", busiType='" + busiType + '\'' +
                ", busiAppId='" + busiAppId + '\'' +
                ", productName='" + productName + '\'' +
                ", busiBody='" + busiBody + '\'' +
                ", userId='" + userId + '\'' +
                ", tradeMoney='" + tradeMoney + '\'' +
                ", busiSeqNo='" + busiSeqNo + '\'' +
                ", signType='" + signType + '\'' +
                ", signValue='" + signValue + '\'' +
                '}';
    }
}
