package com.maywide.grid.boot.baseapi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.entity.AccessLog;
import com.maywide.biz.core.entity.Rule;
import com.maywide.biz.core.entity.SysChannel;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.OperatorInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.pojo.TokenLoginInfo;
import com.maywide.biz.core.service.RuleService;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.pojo.common.ChangePasswordReq;
import com.maywide.biz.inter.pojo.login.LoginInterReq;
import com.maywide.biz.inter.pojo.login.LoginInterResp;
import com.maywide.biz.inter.pojo.querydepartment.DepartmentInterInfo;
import com.maywide.biz.inter.pojo.querydepartment.QueryDepartmentInterReq;
import com.maywide.biz.inter.pojo.verificationLogin.VerificationLoginReq;
import com.maywide.biz.inter.service.InterLoginService;
import com.maywide.biz.inter.service.PubService;
import com.maywide.biz.prv.entity.*;
import com.maywide.core.cons.SysConstant;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.service.PersistentService;
import com.maywide.core.util.*;
import com.maywide.grid.boot.baseapi.pojo.token.CodeReq;
import com.maywide.grid.boot.baseapi.pojo.token.TokenRefreshReq;
import com.maywide.grid.boot.baseapi.pojo.token.TokenReq;
import com.maywide.grid.boot.baseweb.bean.ResponseMvcWarpper;
import com.maywide.grid.boot.baseweb.cons.ApiConstant;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 获得token的
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/token")
public class TokenController {

    private Logger log = LoggerFactory.getLogger(TokenController.class);

    @Value("${grid.boot.token.expire:1800}")
    private int TOKEN_EXPIRE_TIME;
    private static final String DATE_FORMAT = "yyyyMMddHHmmss";
    @Value("${corpid}")
    private String corpid;
    @Value("${corpsecret}")
    private String corpsecret;
    @Autowired
    private InterLoginService loginService;

    @Autowired
    protected PersistentService DAO;
    @Autowired
    private UseridUtil useridUtil;
    @Autowired
    private RuleService ruleService;
    @Autowired
    private PubService pubService;

    public PersistentService getDAO() {
        return DAO;
    }

    @RequestMapping("/request")
    public ResponseMvcWarpper tokenRequset(@RequestBody TokenReq req, HttpServletRequest request) throws Exception {

        String loginname = req.getLoginname();
        CheckUtils.checkEmpty(loginname, "参数缺失: BOSS工号");
        String passwd = req.getPasswd();
        CheckUtils.checkEmpty(passwd, "参数缺失: BOSS工号密码");
        String deptid = req.getDeptid();
        CheckUtils.checkEmpty(deptid, "参数缺失: BOSS部门");


        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        AccessLog accessLog = new AccessLog();
        accessLog.setClientIP(ServletUtil.getClientIP(request));
        accessLog.setCallTime(new Date());
        accessLog.setCallMethod("request");
        JSONObject reqJson = new JSONObject();
        reqJson.put("req", req);


        Rule rule = ruleService.getRule("*", "SYS_LOGIN");
        if (rule.getValue().equals("Y")) {
            String userid = useridUtil.getUseridByCode(req.getCode(), corpid, corpsecret);

            StringBuffer sql = new StringBuffer();
            List param = new ArrayList();
            sql.append(" select * from prv_oper_userid p where p.loginname = ? ");
            param.add(loginname);
            List<PrvOperUserid> operUserid = getDAO().find(sql.toString(), PrvOperUserid.class, param.toArray());
            reqJson.put("userid", userid);
            reqJson.put("operUserid", operUserid);
            if (operUserid.size() > 0 && !StringUtils.equals(userid, operUserid.get(0).getUserid())){
                accessLog.setRequest(reqJson.toJSONString());
                accessLog.setResponse("一个工号只允许在一个企业微信上登录，请在另一台设备上退出再重新登录");
                accessLog.setReturnCode(returnInfo.getCode());
                accessLog.setReturnMsg(returnInfo.getMessage());
                accessLog.setEndTime(new Date());
                accessLog.setOrderid(loginService.getBizorderid());
                try {
                    getDAO().save(accessLog);
                } catch (Exception e) {
                    log.error("request========>", e);
                }
                throw new BusinessException("一个工号只允许在一个企业微信上登录，请在另一台设备上退出再重新登录");
            }
        }
        // 获得渠道系统
        String system = getSystemCode(request);

        // 登录
        ReturnInfo login = login(loginname, passwd, deptid, req.isMd5(),req.getModel(), request);
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        loginInfo.setSystem(system);

        // 获得Token超时默认
        String token = JwtUtil.genrateLoginInfoToken(loginInfo, new Date(), TOKEN_EXPIRE_TIME);
        Claims claims = JwtUtil.decryJwt(token);
        Date expiration = claims.getExpiration();
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        String format = sdf.format(expiration);

        JSONObject resp = new JSONObject();
        resp.put("token", token);
        resp.put("expire", format);
        resp.put("pwd", JSON.toJSON(loginService.checkPwdLastChangeTime(loginInfo.getCity(), loginInfo.getOperid())));


        accessLog.setRequest(reqJson.toJSONString());
        accessLog.setResponse(resp.toJSONString());
        accessLog.setReturnCode(returnInfo.getCode());
        accessLog.setReturnMsg(returnInfo.getMessage());
        accessLog.setEndTime(new Date());
        accessLog.setOrderid(loginService.getBizorderid());
        try {
            getDAO().save(accessLog);
        } catch (Exception e) {
            log.error("request========>", e);
        }
        return ResponseMvcWarpper.toResponse(login, resp);
    }
    @RequestMapping("/wxlogin")
    public ResponseMvcWarpper tokenwxlogin(@RequestBody CodeReq req, HttpServletRequest request) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        AccessLog accessLog = new AccessLog();
        accessLog.setClientIP(ServletUtil.getClientIP(request));
        accessLog.setCallTime(new Date());
        accessLog.setCallMethod("wxlogin");
        JSONObject reqJson = new JSONObject();
        reqJson.put("code", req.getCode());


        String token = request.getHeader(ApiConstant.TOKEN_HEADER);
        Claims claims = JwtUtil.parseJwtClaims(token);
        String loginInfoStr = claims.getSubject();
        if (StringUtils.isBlank(loginInfoStr)) {
            accessLog.setRequest(reqJson.toJSONString());
            accessLog.setResponse(IErrorDefConstant.ERROR_TOKEN_FAIL_MSG);
            accessLog.setReturnCode(returnInfo.getCode());
            accessLog.setReturnMsg(returnInfo.getMessage());
            accessLog.setEndTime(new Date());
            accessLog.setOrderid(loginService.getBizorderid());
            try {
                getDAO().save(accessLog);
            } catch (Exception e) {
                log.error("", e);
            }

            throw new RuntimeException(IErrorDefConstant.ERROR_TOKEN_FAIL_MSG);
        }
        LoginInfo loginInfoByToken = getLoginInfoByToken(loginInfoStr);
        JSONObject resp = new JSONObject();
        if (StringUtils.isNotBlank(req.getCode())) {
            String userid = useridUtil.getUseridByCode(req.getCode(),corpid,corpsecret);

            reqJson.put("userid", userid);

            StringBuffer sb = new StringBuffer();
            List params = new ArrayList();
            sb.append(" DELETE FROM prv_oper_userid WHERE operid = ? ");
            params.add(loginInfoByToken.getOperid());
            getDAO().executeSql(sb.toString(),params.toArray());

            StringBuffer sql = new StringBuffer();
            List param = new ArrayList();
            sql.append(" insert into prv_oper_userid (userid,operid,token,loginname,createtime,depid) values (?,?,?,?,?,?) ");
            param.add(userid);
            param.add(loginInfoByToken.getOperid());
            param.add(token);
            param.add(loginInfoByToken.getLoginname());
            // 获取当前时间
            LocalDateTime currentTime = LocalDateTime.now();

            // 转换为Date格式
            Date date = Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant());
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = sd.format(date);
            param.add(formattedDate);
            param.add(loginInfoByToken.getDeptid());
            getDAO().executeSql(sql.toString(),param.toArray());

            resp.put("userid", userid);
            resp.put("operid", loginInfoByToken.getOperid());
            resp.put("token", token);
            resp.put("deptid", loginInfoByToken.getDeptid());
        }

        accessLog.setRequest(reqJson.toJSONString());
        accessLog.setResponse(resp.toJSONString());
        accessLog.setReturnCode(returnInfo.getCode());
        accessLog.setReturnMsg(returnInfo.getMessage());
        accessLog.setEndTime(new Date());
        accessLog.setOrderid(loginService.getBizorderid());
        try {
            getDAO().save(accessLog);
        } catch (Exception e) {
            log.error("", e);
        }

        return ResponseMvcWarpper.toResponse(returnInfo, resp);
    }
    @RequestMapping("/check")
    public ResponseMvcWarpper tokenCheck(@RequestBody CodeReq req, HttpServletRequest request) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);
        String userid = useridUtil.getUseridByCode(req.getCode(),corpid,corpsecret);


        AccessLog accessLog = new AccessLog();
        accessLog.setClientIP(ServletUtil.getClientIP(request));
        accessLog.setCallTime(new Date());
        accessLog.setCallMethod("check");
        JSONObject reqJson = new JSONObject();
        reqJson.put("code", req.getCode());
        reqJson.put("userid", userid);
        accessLog.setRequest(reqJson.toJSONString());

        StringBuffer sql = new StringBuffer();
        List param = new ArrayList();
        sql.append(" select * from prv_oper_userid p where p.userid = ? ");
        param.add(userid);
        JSONObject resp = new JSONObject();
        List<PrvOperUserid> operUserid = getDAO().find(sql.toString(), PrvOperUserid.class, param.toArray());
        PrvOperUserid prvOperUserid =null;
        if (operUserid.size() > 0){
            prvOperUserid = operUserid.get(0);
            PrvOperator operator = new PrvOperator();
            operator.setLoginname(prvOperUserid.getLoginname());
            Boolean user = checkUser(operator, prvOperUserid);
            if (!user){
                StringBuffer sb = new StringBuffer();
                List params = new ArrayList();
                sb.append(" DELETE FROM prv_oper_userid WHERE loginname = ? ");
                params.add(prvOperUserid.getLoginname());
                getDAO().executeSql(sb.toString(),params.toArray());
                resp.put("isSignBack","N");

                accessLog.setResponse(resp.toJSONString());
                accessLog.setReturnCode(returnInfo.getCode());
                accessLog.setReturnMsg(returnInfo.getMessage());
                accessLog.setEndTime(new Date());
                accessLog.setOrderid(loginService.getBizorderid());
                try {
                    getDAO().save(accessLog);
                } catch (Exception e) {
                    log.error("", e);
                }

                return ResponseMvcWarpper.toResponse(returnInfo, resp);
            }
        }else {
            resp.put("isSignBack", "N");

            accessLog.setResponse(resp.toJSONString());
            accessLog.setReturnCode(returnInfo.getCode());
            accessLog.setReturnMsg(returnInfo.getMessage());
            accessLog.setEndTime(new Date());
            accessLog.setOrderid(loginService.getBizorderid());
            try {
                getDAO().save(accessLog);
            } catch (Exception e) {
                log.error("", e);
            }

            return ResponseMvcWarpper.toResponse(returnInfo, resp);
        }
        String token = prvOperUserid.getToken();
        Claims claims = JwtUtil.parseJwtClaims(token);
        long time = claims.getExpiration().getTime();
        long now = System.currentTimeMillis();
        String loginInfoStr = claims.getSubject();
        if (StringUtils.isBlank(loginInfoStr)) {

            accessLog.setResponse(IErrorDefConstant.ERROR_TOKEN_FAIL_MSG);
            accessLog.setReturnCode(returnInfo.getCode());
            accessLog.setReturnMsg(returnInfo.getMessage());
            accessLog.setEndTime(new Date());
            accessLog.setOrderid(loginService.getBizorderid());
            try {
                getDAO().save(accessLog);
            } catch (Exception e) {
                log.error("", e);
            }

            throw new RuntimeException(IErrorDefConstant.ERROR_TOKEN_FAIL_MSG);
        }
        LoginInfo loginInfoByToken = getLoginInfoByToken(loginInfoStr);

        if (time - now < 0) {
            String tokenRefresh = JwtUtil.genrateLoginInfoToken(loginInfoByToken, new Date(), TOKEN_EXPIRE_TIME);
            Claims claimsRefresh = JwtUtil.decryJwt(tokenRefresh);
            String expire = DateTimeUtil.formatDate(claimsRefresh.getExpiration(), "yyyyMMddHHmmss");
            // 请求头上加上token
            resp.put("token", tokenRefresh);
            resp.put("expire", expire);
            resp.put("isSignBack","Y");
            StringBuffer sb = new StringBuffer();
            List params = new ArrayList();
            sb.append(" update  prv_oper_userid set token = ? where userid = ? ");
            params.add(tokenRefresh);
            params.add(userid);
            getDAO().executeSql(sb.toString(),params.toArray());
        }else {
            resp.put("token", token);
            String expire = DateTimeUtil.formatDate(claims.getExpiration(), "yyyyMMddHHmmss");
            resp.put("expire", expire);
            resp.put("isSignBack","Y");
        }

        accessLog.setResponse(resp.toJSONString());
        accessLog.setReturnCode(returnInfo.getCode());
        accessLog.setReturnMsg(returnInfo.getMessage());
        accessLog.setEndTime(new Date());
        accessLog.setOrderid(loginService.getBizorderid());
        try {
            getDAO().save(accessLog);
        } catch (Exception e) {
            log.error("", e);
        }

        return ResponseMvcWarpper.toResponse(returnInfo, resp);
    }
    public Boolean checkUser(PrvOperator operator ,PrvOperUserid prvOperUserid)throws Exception {
        Boolean loginStatus = true;
        List list = DAO.find(operator);
        if (list == null || list.isEmpty()){
            loginStatus = false;
        }

        PrvOperator oper = (PrvOperator) list.get(0);



        Date nowtime = new Date();
        if (!SysConstant.Operator.PRV_USE_STATUS_ENABLED.equals(oper
                .getStatus())) {
            loginStatus = false;
        }

        // 判断是否要人脸验证和人脸认证状态
        if ("Y".equals(oper.getIsfacelogin()) && !"Y".equals(prvOperUserid.getFace_status())) {
           return false;
        }

        Long depid = prvOperUserid.getDepid();

        // 获取操作员部门
        String sql = "SELECT deptid id,name,kind,areaid,preid,deplevel,isagent,agentid FROM prv_department where deptid in(select deptid from prv_operrole where operid=?) order by deptid";
        List<PrvDepartment> opdepList = DAO.find(sql, PrvDepartment.class,
                oper.getId());
        if (opdepList.size() > 0){
            int index = 0 ;
            for (PrvDepartment prvDepartment : opdepList) {
                if (prvDepartment.getId().equals(depid)){
                    index++;
                }
            }
            if (index == 0){
                loginStatus = false;
            }
        }else {
            loginStatus = false;
        }
        return loginStatus;
    }
    public LoginInfo getLoginInfoByToken(String loginInfoStr) {
        LoginInfo loginInfo = new LoginInfo();
        try {
            ObjectMapper obj = new ObjectMapper();
            //由于与微服务那边的数据格式不一致，要转换一下
            TokenLoginInfo tokenLoginInfo = obj.readValue(loginInfoStr,TokenLoginInfo.class);
            loginInfo.setOperid(tokenLoginInfo.getOperid());
            loginInfo.setLoginname(tokenLoginInfo.getLoginName());
            log.info("==> loginOper={}[{}]", tokenLoginInfo.getLoginName(), tokenLoginInfo.getOperid());
            loginInfo.setName(tokenLoginInfo.getNickName());
            loginInfo.setDeptid(tokenLoginInfo.getDeptId());
            loginInfo.setDeptname(tokenLoginInfo.getDeptName());
            log.info("==> loginDept={}[{}]", tokenLoginInfo.getDeptName(), tokenLoginInfo.getDeptId());
            loginInfo.setCity(tokenLoginInfo.getCity());
            log.info("==> loginCity={}", tokenLoginInfo.getCity());
            loginInfo.setRoleid(tokenLoginInfo.getRoleId());
            log.info("==> loginRole={}", tokenLoginInfo.getRoleId());
            loginInfo.setAreaid(tokenLoginInfo.getAreaid());
            log.info("==> loginArea={}", tokenLoginInfo.getAreaid());
            loginInfo.setSystem(tokenLoginInfo.getSystem());
            log.info("==> loginSys={}", tokenLoginInfo.getSystem());
            loginInfo.setIsagent(tokenLoginInfo.getIsagent());
            loginInfo.setAgentid(tokenLoginInfo.getAgentid());
        } catch (Exception e) {
            log.error("token error!", e);
            throw new RuntimeException(IErrorDefConstant.ERROR_TOKEN_FAIL_MSG);
        }
        return loginInfo;
    }
    @RequestMapping("/refresh")
    public ResponseMvcWarpper tokenRefresh(@RequestBody TokenRefreshReq req, HttpServletRequest request) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        String token = req.getToken();
        CheckUtils.checkEmpty(token, "参数缺失: token");
        String expire = req.getExpire();
        CheckUtils.checkEmpty(expire, "参数缺失: expire");

        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);

        LoginInfo loginInfo = bulidLoginInfo(token, request);

        Claims claims = JwtUtil.decryJwt(token);
        Date expiration = claims.getExpiration();
        String format = sdf.format(expiration);
        if (!StringUtils.equals(format, expire)) {
            throw new BusinessException("过期时间匹配失败");
        }


        // 获得Token
        token = JwtUtil.genrateLoginInfoToken(loginInfo, new Date(), TOKEN_EXPIRE_TIME);
        Claims claimsN = JwtUtil.decryJwt(token);
        Date expirationN = claimsN.getExpiration();
        String formatN = sdf.format(expirationN);

        JSONObject resp = new JSONObject();
        resp.put("token", token);
        resp.put("expire", formatN);


        return ResponseMvcWarpper.toResponse(returnInfo, resp);
    }

    private String getSystemCode(HttpServletRequest request) throws Exception {
        String key = request.getHeader(ApiConstant.TOKEN_KEY_HEADER);
        if (StringUtils.isBlank(key)) {
            throw new BusinessException(String.format("%s is empty. ", ApiConstant.TOKEN_KEY_HEADER));
        }

        SysChannel channel = (SysChannel) loginService.getDAO().findUniqueByProperty(SysChannel.class, "accesskey", key);
        if (channel == null) {
            throw new BusinessException(String.format("渠道code不存在,请联系网格管理员新增渠道. "));
        }

        return channel.getCode();
    }

    /**
     * 登录
     *
     * @param loginname
     * @param passwd
     * @param deptid
     * @return
     * @throws Exception
     */
    private ReturnInfo login(String loginname, String passwd, String deptid, boolean md5, String model, HttpServletRequest request) throws Exception {
        LoginInterReq loginReq = new LoginInterReq();
        loginReq.setLoginname(loginname);
        loginReq.setPassword(passwd);
        loginReq.setDeptid(deptid);
        if (md5) {
            loginReq.setCryptPwd(passwd);
        }

        loginReq.setVerApp(model);
        loginReq.setDevStr(request.getHeader(ApiConstant.USER_AGENT));
        AuthContextHolder.setUserIpAddr(ServletUtil.getClientIP(request));

        LoginInterResp loginResp = new LoginInterResp();
        return loginService.login(loginReq, loginResp);
    }

    @RequestMapping("/queryDepartment")
    public ResponseMvcWarpper queryDepartment(@RequestBody QueryDepartmentInterReq req,
                                      HttpServletRequest request ) throws Exception {
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setCode(IErrorDefConstant.ERROR_SUCCESS_CODE);
        returnInfo.setMessage(IErrorDefConstant.ERROR_SUCCESS_MSG);

        // 先取操作员
        PrvOperator oper = null;
        if (StringUtils.isNotBlank(req.getLoginname())) {
            if(loginService.isPhoneNumber(req.getLoginname())){
                try {
                    PrvMdpStaff prvMdpStaff = loginService.findPrvMdpStaffByPhone(req.getLoginname());
                    req.setLoginname(prvMdpStaff.getLoginname());
                }catch (NullPointerException ex){
                    String loginname = loginService.getLoginNameWithPhoneNumber(req.getLoginname());
                    if (StringUtils.isNotBlank(loginname)) {
                        req.setLoginname(loginname);
                    }
                }
            }
            PrvOperator prvOperatorVO = new PrvOperator();
            prvOperatorVO.setLoginname(req.getLoginname());
            getDAO().clear();
            List<PrvOperator> list = getDAO().find(prvOperatorVO);
            if (list == null || list.size() < 1)
                throw new BusinessException("用户名不存在");

            oper = (PrvOperator) list.get(0);
        }

        StringBuffer sql = new StringBuffer();
        List paramList = new ArrayList();
        sql.append(" SELECT distinct * from ( ");
        sql.append(" SELECT d.deptid AS id, d.name, d.kind, d.areaid, d.preid ");
        sql.append("   FROM prv_department d ");
        sql.append("  WHERE d.deptid IN ");
        sql.append("        (SELECT o.deptid ");
        sql.append("           FROM prv_operrole o ");
        sql.append("          WHERE 1=1 ");
        if (oper != null) {
            sql.append("          and o.operid = ? ");
            paramList.add(oper.getId());
        }

        sql.append("            and exists (select 1 ");
        sql.append("                   from prv_roleprivs r, prv_menudef m ");
        sql.append("                  where r.menuid = m.menuid ");
        sql.append("                    and r.roleid = o.roleid)) ");

        if (req.getAreaid() != null && req.getAreaid().longValue() > 0) {
            sql.append("          and d.areaid = ? ");
            paramList.add(req.getAreaid());
        }

        sql.append(" or d.deptid in (select p.depid  from prv_operdept p " +
                "                   left join prv_department p1 on p.depid = p1.deptid " +
                "                 where p1.name like '%政企%' and p.operid = ?) ");
        paramList.add(oper.getId());
        sql.append("  ORDER BY deptid ");
        sql.append(" ) v ");

        getDAO().clear();
        List<DepartmentInterInfo> departmentList = getDAO().find(
                sql.toString(), DepartmentInterInfo.class, paramList.toArray());

        if(departmentList==null || departmentList.size()==0){
            returnInfo.setCode(-6L);
            returnInfo.setMessage("未配置部门或菜单权限，请联系网格管理员更改");
        }

        return ResponseMvcWarpper.toResponse(returnInfo, departmentList);
    }

    /**
     * 解析token --张耀其   10-24 跟微服务那边的数据签发和解析一致
     *
     * @param authorization
     * @param request
     * @throws Exception
     */
    public LoginInfo bulidLoginInfo(String authorization, HttpServletRequest request) throws Exception {
        try {
            Jws<Claims> claimsJws = JwtUtil.parseJWT(authorization);
            Claims claims = claimsJws.getBody();
            String subject = claims.getSubject();
            ObjectMapper obj = new ObjectMapper();

            //由于与微服务那边的数据格式不一致，要转换一下
            TokenLoginInfo tokenLoginInfo = obj.readValue(subject, TokenLoginInfo.class);

            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setOperid(tokenLoginInfo.getOperid());
            loginInfo.setLoginname(tokenLoginInfo.getLoginName());
            loginInfo.setName(tokenLoginInfo.getNickName());
            loginInfo.setDeptid(tokenLoginInfo.getDeptId());
            loginInfo.setDeptname(tokenLoginInfo.getDeptName());
            loginInfo.setCity(tokenLoginInfo.getCity());
            loginInfo.setRoleid(tokenLoginInfo.getRoleId());
            loginInfo.setAreaid(tokenLoginInfo.getAreaid());
            loginInfo.setSystem(tokenLoginInfo.getSystem());
            loginInfo.setIsagent(tokenLoginInfo.getIsagent());
            loginInfo.setAgentid(tokenLoginInfo.getAgentid());
            return loginInfo;

        } catch (Exception e) {
            log.error("token error!", e);
            throw new RuntimeException(IErrorDefConstant.ERROR_TOKEN_FAIL_MSG);
        }
    }



    @RequestMapping("/sendVerification")
    public ResponseMvcWarpper sendVerification(@RequestBody VerificationLoginReq req, HttpServletRequest request ) throws Exception {
        LoginInterResp loginInterResp = new LoginInterResp();
        return ResponseMvcWarpper.toResponse(loginService.getLoginPhoneVerification(req, loginInterResp), loginInterResp);
    }

    @RequestMapping("/verificationLogin")
    public ResponseMvcWarpper verificationLogin(@RequestBody VerificationLoginReq req, HttpServletRequest request ) throws Exception {
        LoginInterResp loginInterResp = new LoginInterResp();
        ReturnInfo login = loginService.verificationLogin(req, loginInterResp, request);
        // 获得渠道系统
        String system = getSystemCode(request);

        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        loginInfo.setSystem(system);
        // 获得Token超时默认
        String token = JwtUtil.genrateLoginInfoToken(loginInfo, new Date(), TOKEN_EXPIRE_TIME);
        Claims claims = JwtUtil.decryJwt(token);
        Date expiration = claims.getExpiration();
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        String format = sdf.format(expiration);

        JSONObject resp = new JSONObject();
        resp.put("token", token);
        resp.put("expire", format);

        return ResponseMvcWarpper.toResponse(login, resp);
    }


    @RequestMapping("/changePwd")
    public ResponseMvcWarpper changePwd(@RequestBody ChangePasswordReq req, HttpServletRequest request) throws Exception {
        CheckUtils.checkEmpty(req.getPwd(),"原密码不能为空");
        CheckUtils.checkEmpty(req.getDeptid(),"部门不能为空");
        CheckUtils.checkEmpty(req.getLoginname(),"工号不能为空");

        String loginname = req.getLoginname();
        String passwd = MD5.md5(req.getPwd());
        String deptid = req.getDeptid();

        // 登录
        ReturnInfo login = login(loginname, passwd, deptid, true,"未知", request);
        req.setBizorderid(pubService.getBizorderid());
        ReturnInfo returnInfo = pubService.changePassword(req);

        JSONObject resp = new JSONObject();
        resp.put("status", "Y");
        return ResponseMvcWarpper.toResponse(returnInfo, resp);
    }

}
