package com.maywide.grid.boot.baseapi.sysparam;

/**
 * <AUTHOR>
 */
public class SysparamGcodeCons {

    /**
     * mcode=@DYNAMIC_DETAIL 就是SQL参数，取data SQL 查询。
     */
    public static final String MCODE_DYNAMIC_DETAIL = "@DYNAMIC_DETAIL";

    /**
     * 业务类型
     */
    public static final String SYS_PERMARK = "SYS_PERMARK";
    /**
     * 5G 可选商品订购周期
     */
    public static final String GW_TERM_UNIT = "GW_TERM_UNIT";

    /**
     * 5G 身份认证 性别
     */
    public static final String FGB_GENDER = "FGB_GENDER";

    /**
     * 5G 身份认证 民族
     */
    public static final String FGB_NATIONAL_TYPE = "FGB_NATIONAL_TYPE";
    /**
     * 广电业务支付方式
     */
    public static final String SYS_CITY_PAYWAY = "SYS_CITY_PAYWAY";
    /**
     * 5G 业务支付方式
     */
    public static final String SYS_CITY_PAYWAY_5G = "SYS_CITY_PAYWAY_5G";

    /**
     * 5G二维码 业务支付方式
     */
    public static final String SYS_CITY_PAYWAY_EWM_5G = "SYS_CITY_PAYWAY_EWM_5G";

    /**
     * 5G二维码 + 抖音 业务支付方式
     */
    public static final String SYS_CITY_PAYWAY_EWM_DOUYIN_5G = "SYS_CITY_PAYWAY_EWM_DOUYIN_5G";

    /**
     * 代理商付款方式
     */
    public static final String SYS_AGENT_CITY_PAYWAY_5G = "SYS_AGENT_CITY_PAYWAY_5G";

    /**
     * 支付方式列表
     */
    public static final String SYS_PAYWAY = "SYS_PAYWAY";
    /**
     * 5G发票状态
     */
    public static final String FGB_NOTE_STATE = "FGB_NOTE_STATE";

}
