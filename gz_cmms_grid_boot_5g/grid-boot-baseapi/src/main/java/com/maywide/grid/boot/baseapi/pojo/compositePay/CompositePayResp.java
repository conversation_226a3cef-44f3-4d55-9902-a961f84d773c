package com.maywide.grid.boot.baseapi.pojo.compositePay;


/**
 * @author:Hu<PERSON>iangTao
 * @date:2023/8/7 16:17
 */
public class CompositePayResp {
    private String code;
    private String msg;
    private ResultData data;
    private String signType;
    private String signValue;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ResultData getData() {
        return data;
    }

    public void setData(ResultData data) {
        this.data = data;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSignValue() {
        return signValue;
    }

    public void setSignValue(String signValue) {
        this.signValue = signValue;
    }

    @Override
    public String toString() {
        return "CompositePayResp{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                ", signType='" + signType + '\'' +
                ", signValue='" + signValue + '\'' +
                '}';
    }

}
