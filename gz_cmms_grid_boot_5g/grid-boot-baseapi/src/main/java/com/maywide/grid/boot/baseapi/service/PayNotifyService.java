package com.maywide.grid.boot.baseapi.service;

import com.alibaba.fastjson.JSON;
import com.maywide.biz.inter.entity.CompositePayResult;
import com.maywide.biz.inter.entity.RefundResultNotify;
import com.maywide.core.service.PersistentService;
import com.maywide.core.util.CompositePayUtil;
import com.maywide.core.util.MD5;
import com.maywide.grid.boot.baseapi.pojo.compositePay.CompositePayResp;
import com.maywide.grid.boot.baseapi.pojo.compositePay.PayNotifyReq;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>ao
 * @date:2023/8/8 15:29
 */
@Service
public class PayNotifyService {
    @Value("${secret:}")
    private String secret;

    @Autowired
    private PersistentService DAO;

    @Autowired
    private CompositePayUtil compositePayUtil;

    private static final Logger logger = LoggerFactory.getLogger(PayNotifyService.class);

    public PersistentService getDAO() {
        return DAO;
    }

    public void setDAO(PersistentService dAO) {
        DAO = dAO;
    }

    /**
     * 综合支付结果通知接口
     *
     * @param req
     * @return
     * @throws Exception
     */
    public CompositePayResp payNotify(PayNotifyReq req) throws Exception {
        CompositePayResp resp = new CompositePayResp();
        if (req.getBusiSeqNo() == null) {
            logger.error("综合支付:支付结果通知接口请求失败====>请求参数为空");
            resp.setCode("FAIL");
            resp.setMsg("请求参数为空");
            setMd5(resp);
            return resp;
        }

        CompositePayResult result = new CompositePayResult();
        BeanUtils.copyProperties(req, result);
        String creatTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        result.setCreateTime(creatTime);

        try {
            getDAO().save(result);
        } catch (Exception e) {
            logger.error("综合支付:支付结果数据库添加失败====>", e);
            resp.setCode("FAIL");
            resp.setMsg("请求失败");
            setMd5(resp);
            return resp;
        }

        resp.setCode("SUCCESS");
        setMd5(resp);
        logger.info("综合支付:支付结果通知接口请求成功======>{}", JSON.toJSONString(resp));
        return resp;
    }

    /**
     * 退款结果通知接口
     * @param req
     * @return
     * @throws Exception
     */
    public CompositePayResp refundNotify(PayNotifyReq req) throws Exception {
        CompositePayResp resp = new CompositePayResp();
        if (req.getBusiSeqNo() == null) {
            logger.error("综合支付:退款结果通知接口请求失败====>请求参数为空");
            resp.setCode("FAIL");
            resp.setMsg("请求参数为空");
            setMd5(resp);
            return resp;
        }
        RefundResultNotify refund = new RefundResultNotify();
        BeanUtils.copyProperties(req, refund);
        String creatTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        refund.setCreateTime(creatTime);

        try {
            getDAO().save(refund);
        } catch (Exception e) {
            logger.error("综合支付:退款结果数据库添加失败====>", e);
            resp.setCode("FAIL");
            resp.setMsg("请求失败");
            setMd5(resp);
            return resp;
        }

        resp.setCode("SUCCESS");
        setMd5(resp);
        logger.info("综合支付:退款结果通知接口请求成功======>{}", JSON.toJSONString(resp));

        return resp;
    }


    public void setMd5(CompositePayResp resp) {
        StringBuffer result = new StringBuffer();
        result.append("code=" + resp.getCode());
        if (StringUtils.isNotBlank(resp.getMsg())) {
            result.append("&msg=" + resp.getMsg());
        }

        String before = result + "&key=" + secret;
        logger.error("Sign Before MD5====>{}", before);
        String after = MD5.md5(before);
        logger.error("Sign After====>{}", after);

        resp.setSignType("MD5");
        resp.setSignValue(after);
    }


}
