package com.maywide.grid.boot.baseapi;

import com.alibaba.fastjson.JSON;
import com.maywide.grid.boot.baseapi.pojo.compositePay.CompositePayResp;
import com.maywide.grid.boot.baseapi.pojo.compositePay.PayNotifyReq;
import com.maywide.grid.boot.baseapi.service.PayNotifyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author:Hu<PERSON>iangTao
 * @date:2023/8/8 15:27
 */
@RestController
@RequestMapping("/ComPlay")
public class PayNotifyController {
    private static final String pay = "pay";
    private static final String refund = "refund";
    @Autowired
    private PayNotifyService payNotifyService;
    private static final Logger logger = LoggerFactory.getLogger(PayNotifyController.class);

    @RequestMapping("/notity")
    public CompositePayResp payNotify(@RequestParam("action") String action, @RequestBody PayNotifyReq req) throws Exception {
        logger.info("综合支付通知入参======>action={}, request={}", action, JSON.toJSONString(req));
        if (pay.equals(action)) {
            return payNotifyService.payNotify(req);
        }
        if (refund.equals(action)) {
            return payNotifyService.refundNotify(req);
        }
        return null;
    }

}
