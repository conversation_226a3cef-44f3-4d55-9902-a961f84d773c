package com.maywide.grid.boot.baseapi.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * <AUTHOR>
 */
@Service
public class TempService {

    public String getData(String name) throws IOException {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        InputStream asStream = classLoader.getResourceAsStream("config/json/" + name + ".json");
        String s = IOUtils.toString(asStream, "utf-8");
        return s;
    }

}
