package com.maywide.grid.boot.baseapi.pojo.compositePay;

/**
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date:2023/8/9 11:43
 */
public class CompositePayReq {
    private String userId;
    private String tradeMoney;
    private String busiSeqNo;
    private String productName;
    private String busiBody;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getBusiBody() {
        return busiBody;
    }

    public void setBusiBody(String busiBody) {
        this.busiBody = busiBody;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTradeMoney() {
        return tradeMoney;
    }

    public void setTradeMoney(String tradeMoney) {
        this.tradeMoney = tradeMoney;
    }

    public String getBusiSeqNo() {
        return busiSeqNo;
    }

    public void setBusiSeqNo(String busiSeqNo) {
        this.busiSeqNo = busiSeqNo;
    }

    @Override
    public String toString() {
        return "CompositePayReq{" +
                "userId='" + userId + '\'' +
                ", tradeMoney='" + tradeMoney + '\'' +
                ", busiSeqNo='" + busiSeqNo + '\'' +
                ", productName='" + productName + '\'' +
                ", busiBody='" + busiBody + '\'' +
                '}';
    }
}
