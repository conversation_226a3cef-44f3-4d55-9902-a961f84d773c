package com.maywide.grid.boot.baseapi.sysparam;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.maywide.biz.core.service.ParamService;
import com.maywide.biz.system.entity.PrvSysparam;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class SysparamCache {

    private static Cache<String, List<PrvSysparam>> sysparamCache;

    private static ParamService paramService;

    private static final long EXPIRE_DURATION = 12L;
    private static final TimeUnit EXPIRE_TIME_UNIT = TimeUnit.HOURS;
    private static final long MAX_SISE = 100L;

    @PostConstruct
    public void init() {
        if (sysparamCache == null) {
            Cache<String, List<PrvSysparam>> cache = Caffeine.newBuilder()
                    .expireAfterWrite(EXPIRE_DURATION, EXPIRE_TIME_UNIT)
                    .maximumSize(MAX_SISE)
                    .build();

            sysparamCache = cache;
        }
    }

    @Resource
    public void setParamService(ParamService paramService) {
        SysparamCache.paramService = paramService;
    }

    // 菜单数据并没有多少，不需要每次从缓存中读取，直接从数据库中读取
    @Deprecated
    public static List<PrvSysparam> getSysparamListOld(String gcode) throws Exception {
        List<PrvSysparam> ifPresent = sysparamCache.getIfPresent(gcode);
        if (ifPresent != null && !ifPresent.isEmpty()) {
            return ifPresent;
        }

        List<PrvSysparam> data = paramService.getDataOrderBySort(gcode);
        if (data != null && !data.isEmpty()) {
            sysparamCache.put(gcode, data);
            return data;
        }
        return new ArrayList<>();
    }

    public static List<PrvSysparam> getSysparamList(String gcode) throws Exception {
        return paramService.getDataOrderBySort(gcode);
    }

    public static List<PrvSysparam> getSysparamListCache(String gcode) throws Exception {
        List<PrvSysparam> ifPresent = sysparamCache.getIfPresent(gcode);
        if (ifPresent != null && !ifPresent.isEmpty()) {
            return ifPresent;
        }

        List<PrvSysparam> data = paramService.getDataOrderBySort(gcode);
        if (data == null || data.isEmpty()) {
            return new ArrayList<>();
        }

        sysparamCache.put(gcode, data);
        return data;
    }

    public static void delParamCache(String gcode) {
        sysparamCache.invalidate(gcode);
    }

    public static void delParamCacheAll() {
        sysparamCache.invalidateAll();
    }

}
