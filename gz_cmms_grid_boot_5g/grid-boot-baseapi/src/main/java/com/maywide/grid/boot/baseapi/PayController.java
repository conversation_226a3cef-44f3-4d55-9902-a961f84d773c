package com.maywide.grid.boot.baseapi;

import com.alibaba.fastjson.JSON;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.servlet.IErrorDefConstant;
import com.maywide.biz.inter.constant.QueConstant;
import com.maywide.biz.inter.pojo.quecustorder.CustordersBO;
import com.maywide.biz.inter.pojo.quecustorder.QueCustorderInterReq;
import com.maywide.biz.inter.pojo.quecustorder.QueCustorderInterResp;
import com.maywide.biz.inter.service.PubService;
import com.maywide.biz.market.entity.BizPortalOrder;
import com.maywide.biz.pay.unify.manager.UnifyPayManager;
import com.maywide.biz.pay.unify.pojo.MultiPayBean;
import com.maywide.biz.pay.unify.pojo.QueUnifyPayInfoResp;
import com.maywide.biz.pay.unify.pojo.UnifyPayCondition;
import com.maywide.biz.pay.unify.pojo.UnifyPayWechatBingBean;
import com.maywide.biz.pay.unify.service.UnifyPayService;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.AuthContextHolder;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/pay")
public class PayController {

    @Autowired
    private PubService pubService;
    @Autowired
    private UnifyPayService payService;

    @RequestMapping("/payUnify")
    public ModelAndView payUnify(String custorderid, String condition, String unifyPayWechatBingBean,
                                 String multipayBean) throws Exception {
        LoginInfo loginInfo = AuthContextHolder.getLoginInfo();
        CheckUtils.checkNull(loginInfo, QueConstant.CommonNotice.LOGIN_OUT_NOTICE);

        QueCustorderInterResp queOrderResp = new QueCustorderInterResp();
        QueCustorderInterReq queOrderReq = new QueCustorderInterReq();
        queOrderReq.setDetail(false);
        queOrderReq.setCustorderid(custorderid);
        ReturnInfo queOrderInfo = pubService.queCustorder(queOrderReq, queOrderResp);
        if (queOrderInfo.getCode() != IErrorDefConstant.ERROR_SUCCESS_CODE) {
            throw new BusinessException(queOrderInfo.getMessage(), queOrderInfo.getCode());
        }
        if (queOrderResp.getCustorders() == null || queOrderResp.getCustorders().size() == 0) {
            throw new BusinessException("查询订单失败");
        }
        CustordersBO order = queOrderResp.getCustorders().get(0);
        order.setSplitInfoList(payService.getSplitInfoList(order.getCustorderid()));

        // 获得国网订单信息
        order.setGwPayOrderinfo(payService.getGwPayOrderInfo(order.getCustorderid()));

        BizPortalOrder bizPortalOrder = (BizPortalOrder) payService.getDAO().find(BizPortalOrder.class, Long.parseLong(order.getCustorderid()));
        CheckUtils.checkNull(bizPortalOrder, "缴费失败,查询不到对应的订单信息!");
        // 更改为不执行sql，改到回调执行
        Long payid = payService.getSnowflakeOrderId();

        UnifyPayCondition uc = null;
        if (StringUtils.isNotBlank(condition)) {
            uc = JSON.parseObject(condition, UnifyPayCondition.class);
        }

        UnifyPayWechatBingBean upwb = null;
        if (StringUtils.isNotBlank(unifyPayWechatBingBean)) {
            upwb = JSON.parseObject(unifyPayWechatBingBean, UnifyPayWechatBingBean.class);
        }

        MultiPayBean mb = null;
        if (StringUtils.isNotBlank(multipayBean)) {
            mb = JSON.parseObject(multipayBean, MultiPayBean.class);
        }

        QueUnifyPayInfoResp quResp = UnifyPayManager.generatePayReq(order, uc, upwb, mb, payid);


        return null;
    }

}
