package com.maywide.grid.boot.baseapi;

import cn.hutool.json.JSONObject;
import cn.hutool.json.XML;
import com.maywide.biz.ass.assdata.service.OrderAssService;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.service.PersistentService;
import com.maywide.core.util.WXBizMsgCrypt;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/suite")
public class SuiteTicketController {
    private static final Log log = LogFactory.getLog(OrderAssService.class);
    @Value("${token}")
    private String sToken ;
    @Value("${corpid}")
    private  String sCorpID ;
    @Value("${corpsecret}")
    private  String suiteID ;
    @Value("${EncodingAESKey}")
    private  String sEncodingAESKey ;

    @Autowired
    private PersistentService persistentService;

    @GetMapping("/receive")
    public String suiteReceive(@RequestParam(value = "msg_signature") String msgSignature,
                               @RequestParam(value = "timestamp") String timestamp, @RequestParam(value = "nonce") String nonce,
                               @RequestParam(value = "echostr") String data) {
        System.out.println("GET################################");
        System.out.println("msgSignature:" + msgSignature);
        System.out.println("timestamp:" + timestamp);
        System.out.println("nonce:" + nonce);
        System.out.println("data:" + data);
        try {
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(sToken, sEncodingAESKey, sCorpID);
            String sEchoStr = wxcpt.verifyAndGetData(msgSignature, timestamp, nonce, data);

            System.out.println("aesDecode:" + sEchoStr);

            // 将解密后获取的参数直接返回就行
            return sEchoStr;
        } catch (Exception e) {
            return e.getMessage();
        }

    }


    @PostMapping("/receive")
    public String suiteReceivePost(HttpServletRequest request,
                                   @RequestParam(value = "msg_signature") String msgSignature,
                                   @RequestParam(value = "timestamp") String timestamp, @RequestParam(value = "nonce") String nonce) {
        System.out.println("POST################################");
        System.out.println("msgSignature:" + msgSignature);
        System.out.println("timestamp:" + timestamp);
        System.out.println("nonce:" + nonce);
        String result = null;
        try {
            String xmlString = getXMLString(request);
            System.out.println("data:" + xmlString);
            String encryptData = XML.toJSONObject(xmlString).getJSONObject("xml").getStr("Encrypt");
            System.out.println("encryptData:" + encryptData);

            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(sToken, sEncodingAESKey, suiteID);
            String sEchoStr = wxcpt.verifyAndGetData(msgSignature, timestamp, nonce, encryptData);
            ;
            System.out.println("sEchoStr:" + sEchoStr);
            JSONObject jsonObject = XML.toJSONObject(sEchoStr).getJSONObject("xml");
            System.out.println("jsonObject:" + jsonObject.toString());
            String infoType = jsonObject.getStr("InfoType");
            System.out.println("infoType:" + infoType);

            // 获取到的suiteTicket
            String suiteTicket = jsonObject.getStr("SuiteTicket");
            System.out.println("suiteTicket:" + suiteTicket);

            StringBuffer sql = new StringBuffer();
            List paramList = new ArrayList();

            sql.append(" select * from prv_sysparam p  where p.gcode = ? ");
            paramList.add(BizConstant.SuiteTicket.SuiteTicket);
            List<PrvSysparam> prvSysparams = persistentService.find(sql.toString(), PrvSysparam.class, paramList.toArray());
            if (prvSysparams.size() > 0){
                StringBuffer sqlUpdate = new StringBuffer();
                List paramListUpdate = new ArrayList();
                sqlUpdate.append(" UPDATE prv_sysparam  set  mcode = ? where gcode =? ");
                paramListUpdate.add(suiteTicket);
                paramListUpdate.add(BizConstant.SuiteTicket.SuiteTicket);
                persistentService.executeSql(sqlUpdate.toString(),paramListUpdate.toArray());
            }else {
                StringBuffer sqlInsert = new StringBuffer();
                List paramListInsert  = new ArrayList();
                sqlInsert.append(" insert into prv_sysparam (gcode,mcode,mname)values(?,?,'微信Ticket') ");
                paramListInsert.add(BizConstant.SuiteTicket.SuiteTicket);
                paramListInsert.add(suiteTicket);
                persistentService.executeSql(sqlInsert.toString(),paramListInsert.toArray());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        // 该接口返回success
        return "success";
    }
    private String getXMLString(HttpServletRequest request) throws IOException {
        BufferedReader reader = request.getReader();
        StringBuilder stringBuilder = new StringBuilder();
        String line = null;
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line);
        }
        return stringBuilder.toString();
    }
}
