package com.maywide.grid.boot.baseapi.pojo.compositePay;

/**
 * @author:<PERSON><PERSON><PERSON>g<PERSON>ao
 * @date:2023/8/7 16:20
 */
public class ResultData {
    /**
     * 二维码地址
     * 扫码支付的二维码地址
     */
    private String qrCode;
    /**
     * 网页地址
     * 跳转收银台支付网页URL
     */
    private String qrUrl;
    /**
     * 二维码图片地址
     * 扫码支付的二维码图片生成地址
     */
    private String imgUrl;
    /**
     * 二维码支付金额
     * 需支付金额，单位为分
     */
    private String amt;

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getQrUrl() {
        return qrUrl;
    }

    public void setQrUrl(String qrUrl) {
        this.qrUrl = qrUrl;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }

    @Override
    public String toString() {
        return "ResultData{" +
                "qrCode='" + qrCode + '\'' +
                ", qrUrl='" + qrUrl + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", amt='" + amt + '\'' +
                '}';
    }
}
