package com.maywide.grid.boot.api5g.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.entity.AliLivenessAuthLog;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.inter.service.PubService;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.util.CheckUtils;
import com.maywide.grid.boot.api5g.bean.AliRealPersonAuthService.applyAlicert.ApplyAlicertReq;
import com.maywide.grid.boot.api5g.bean.AliRealPersonAuthService.applyAlicert.ApplyAlicertResp;
import com.maywide.grid.boot.api5g.bean.AliRealPersonAuthService.resultAlicert.ResultAlicertReq;
import com.maywide.grid.boot.api5g.bean.AliRealPersonAuthService.resultAlicert.ResultAlicertResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 *
 * 阿里云活体检测
 * @author:HuJiangTao
 * @date:2024/10/24 16:58
 */
@Service
public class AliRealPersonAuthService extends CommonService {
    /**
     * 阿里云活体方式
     * 取值：SDK/H5/WEB，当前仅支持SDK和H5。
     * 1、WEB类型是指PC页面。
     */
    private final static String LIVENESS_WAY_H5 = "H5";

    /**
     * 活体检测类型。取值：
     *
     * LIVENESS（默认）：眨眼动作活体检测。
     *
     * PHOTINUS_LIVENESS：眨眼动作活体+炫彩活体双重检测。
     *
     * MULTI_ACTION：多动作活体检测。当前为眨眼+任意摇头检测。
     *
     * MOVE_ACTION：远近动作+眨眼动作活体检测。
     */
    private final static String LIVENESS = "LIVENESS";

    /**
     * 阿里云返回certifyUrl类型
     * L：Https，原始长链。
     */
    private final static String CERTIFY_URL_STYLE_HTTPS = "L";

    /**
     * 认证结果
     * T：通过
     */
    private final static String T = "T";

    /**
     * 认证结果
     * F：不通过。
     */
    private final static String F = "F";

    /**
     * 成功
     */
    private final static String SUCCESS = "200";

    /**
     * 身份证
     */
    private final static String SFZ = "1";

    @Resource
    private PubService pubService;

    public ReturnInfo applyAlicert(ApplyAlicertReq req, ApplyAlicertResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkEmpty(req.getCustid(), "客户编号为空");
        CheckUtils.checkEmpty(req.getMeta_info(), "MetaInfo环境参数为空");
        CheckUtils.checkEmpty(req.getIden_type(), "证件类型为空");
        CheckUtils.checkEmpty(req.getIden_nr(), "证件号码为空");
        CheckUtils.checkEmpty(req.getReturn_url(), "回调地址为空");

        if (SFZ.equals(req.getIden_type()) && !pubService.isCardId(req.getIden_nr())) {
            throw new BusinessException("客户证件号格式不正确，请先读取证件，或使用【客户资料修改】更改客户证件号");
        }

        String bossSerialno = null;
        try {
            bossSerialno = getBossSerialno(loginInfo, req.getBizorderid());
        } catch (Exception e) {
            throw new BusinessException("【GET_BOSS_SERIALNO】获取操作员流水号失败");
        }
        JSONObject bossReq = new JSONObject();

        PrvSysparam data = paramService.getData("ALI_LIVENESS_TYPE", "TYPE");
        String liveness_type = data != null ? data.getData() : LIVENESS;

        bossReq.put("custid", req.getCustid());
        bossReq.put("meta_info", req.getMeta_info());
        bossReq.put("iden_type", req.getIden_type());
        bossReq.put("iden_nr", req.getIden_nr());
        bossReq.put("access_num", req.getAccess_num());
        bossReq.put("return_url", req.getReturn_url());
        bossReq.put("serialno", bossSerialno);
        bossReq.put("liveness_way", LIVENESS_WAY_H5);
        bossReq.put("liveness_type", liveness_type);
        bossReq.put("certify_url_style", CERTIFY_URL_STYLE_HTTPS);

        String output = getBossHttpInfOutput(req.getBizorderid(), BizConstant.M5gInterface.GW_ALICERT_APPLY, bossReq, loginInfo);

        if (StringUtils.isNotBlank(output) && !"null".equals(output)) {
            ApplyAlicertResp applyAlicertResp = JSONObject.parseObject(output, ApplyAlicertResp.class);
            BeanUtils.copyProperties(applyAlicertResp, resp);

            saveLivenessAuthLog(req, bossSerialno, liveness_type, applyAlicertResp);
        }

        return returnInfo;
    }

    private void saveLivenessAuthLog(ApplyAlicertReq req, String bossSerialno, String liveness_type, ApplyAlicertResp applyAlicertResp) throws Exception {
        AliLivenessAuthLog aliLivenessAuthLog = new AliLivenessAuthLog();
        aliLivenessAuthLog.setCustid(req.getCustid());
        aliLivenessAuthLog.setMeta_info(req.getMeta_info());
        aliLivenessAuthLog.setIden_type(req.getIden_type());
        aliLivenessAuthLog.setIden_nr(req.getIden_nr());
        aliLivenessAuthLog.setAccess_num(req.getAccess_num());
        aliLivenessAuthLog.setReturn_url(req.getReturn_url());
        aliLivenessAuthLog.setSerialno(bossSerialno);
        aliLivenessAuthLog.setLiveness_way(LIVENESS_WAY_H5);
        aliLivenessAuthLog.setLiveness_type(liveness_type);
        aliLivenessAuthLog.setCertify_url_style(CERTIFY_URL_STYLE_HTTPS);
        aliLivenessAuthLog.setCertify_id(applyAlicertResp.getCertify_id());
        aliLivenessAuthLog.setLiveness_biz_no(applyAlicertResp.getLiveness_biz_no());
        aliLivenessAuthLog.setCertify_url(applyAlicertResp.getCertify_url());
        aliLivenessAuthLog.setCreattime(new Date());
        aliLivenessAuthLog.setPoid(req.getPoid());
        aliLivenessAuthLog.setOrderId(req.getOrderId());
        getDAO().save(aliLivenessAuthLog);
        getDAO().flushSession();
    }


    public ReturnInfo resultAlicert(ResultAlicertReq req, ResultAlicertResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkEmpty(req.getCertify_id(), "【certify_id】认证标识为空");

        AliLivenessAuthLog aliLivenessAuthLog = new AliLivenessAuthLog();
        aliLivenessAuthLog.setCertify_id(req.getCertify_id());
        List<AliLivenessAuthLog> list = getDAO().find(aliLivenessAuthLog);
        if (CollectionUtil.isEmpty(list)) {
            throw new BusinessException(String.format("【%s】查询不到申请活体检测记录", req.getCertify_id()));
        }

        AliLivenessAuthLog authLog = list.get(0);
        JSONObject bossReq = new JSONObject();
        bossReq.put("iden_type", authLog.getIden_type());
        bossReq.put("iden_nr", authLog.getIden_nr());
        bossReq.put("certify_id", authLog.getCertify_id());
        bossReq.put("liveness_biz_no", authLog.getLiveness_biz_no());

        String output = getBossHttpInfOutput(req.getBizorderid(), BizConstant.M5gInterface.GW_ALICERT_RESULT, bossReq, loginInfo);

        if (StringUtils.isNotBlank(output) && !"null".equals(output)) {
            ResultAlicertResp resultAlicertResp = JSONObject.parseObject(output, ResultAlicertResp.class);

            if (!SUCCESS.equals(resultAlicertResp.getSub_code())) {
                throw new BusinessException(resultAlicertResp.getSub_code_desc());
            }

            if (F.equals(resultAlicertResp.getPassed())) {
                throw new BusinessException(String.format("【%s】认证不通过，请重新认证", req.getCertify_id()));
            }

            resultAlicertResp.setCertify_id(authLog.getCertify_id());
            resultAlicertResp.setLiveness_biz_no(authLog.getLiveness_biz_no());
            resultAlicertResp.setOrderId(authLog.getOrderId());
            BeanUtils.copyProperties(resultAlicertResp, resp);
        }

        return returnInfo;
    }
}
