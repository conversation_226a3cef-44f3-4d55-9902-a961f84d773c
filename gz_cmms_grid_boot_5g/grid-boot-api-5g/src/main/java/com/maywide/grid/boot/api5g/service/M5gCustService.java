package com.maywide.grid.boot.api5g.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maywide.biz.cons.BizConstant;
import com.maywide.biz.core.pojo.LoginInfo;
import com.maywide.biz.core.pojo.ReturnInfo;
import com.maywide.biz.core.service.CommonService;
import com.maywide.biz.core.service.ParamService;
import com.maywide.biz.market.entity.BizAuthOrder;
import com.maywide.biz.market.entity.CustOrder;
import com.maywide.biz.system.entity.PrvSysparam;
import com.maywide.core.exception.BusinessException;
import com.maywide.core.security.remote.BossHttpClientImpl;
import com.maywide.core.util.CheckUtils;
import com.maywide.core.util.DateUtils;
import com.maywide.grid.boot.api5g.bean.gwAssetQryuserres.GwAssetQryUserResRep;
import com.maywide.grid.boot.api5g.bean.gwAssetQryuserres.GwAssetQryUserResResp;
import com.maywide.grid.boot.api5g.bean.h5AuthIdentify.*;
import com.maywide.grid.boot.api5g.bean.m5gCustService.M5gCustInfo;
import com.maywide.grid.boot.api5g.bean.m5gCustService.M5gCustUserInfo;
import com.maywide.grid.boot.api5g.bean.m5gCustService.M5gServInfo;
import com.maywide.grid.boot.api5g.bean.m5gCustService.assetApplyinoice.ApplyInoiceBo;
import com.maywide.grid.boot.api5g.bean.m5gCustService.assetApplyinoice.AssetApplyinoiceReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.assetApplyinoice.AssetApplyinoiceResp;
import com.maywide.grid.boot.api5g.bean.m5gCustService.assetApplyinoice.GwUnifiedApplyinoiceReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.assetQueErycharge.AssetQueErychargeReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.assetQueErycharge.AssetQueErychargeResp;
import com.maywide.grid.boot.api5g.bean.m5gCustService.assetQueErycharge.QueRychargeInfo;
import com.maywide.grid.boot.api5g.bean.m5gCustService.checkMobilenoPwd.CheckMobilenoPwdReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.checkMobilenoPwd.CheckMobilenoPwdResp;
import com.maywide.grid.boot.api5g.bean.m5gCustService.m5gQueCustinfo.M5gQueCustinfoReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.m5gQueCustinfo.M5gQueCustinfoResp;
import com.maywide.grid.boot.api5g.bean.m5gCustService.machineOwnerRealAuth.MachineOwnerRealAuth;
import com.maywide.grid.boot.api5g.bean.m5gCustService.machineOwnerTextAuth.MachineOwnerTextAuthReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queErycustBill.Erycust;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queErycustBill.QueErycustBillReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queErycustBill.QueErycustBillResp;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queFgbDestroyed.QueFgbDestroyedItem;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queFgbDestroyed.QueFgbDestroyedRep;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queFgbDestroyed.QueFgbDestroyedResp;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queFindFusion.QueFindFusionInfo;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queFindFusion.QueFindFusionReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queFindFusion.QueFindFusionResp;
import com.maywide.grid.boot.api5g.bean.m5gCustService.queM5gInvoiceinfo.*;
import com.maywide.grid.boot.api5g.bean.m5gCustService.querComprehensiveList.*;
import com.maywide.grid.boot.api5g.bean.m5gCustService.relationFileUpload.RelationFileUploadReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.relationFileUpload.RelationFileUploadResp;
import com.maywide.grid.boot.api5g.bean.m5gCustService.replaceUserAuth.ReplaceUserAuthReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.resetPwd.ResetPwdReq;
import com.maywide.grid.boot.api5g.bean.m5gCustService.resetPwd.ResetPwdResp;
import com.maywide.grid.boot.api5g.bean.m5gKhProcessService.entity.BizProcessOrder;
import com.maywide.grid.boot.api5g.bean.m5gKhProcessService.queM5gKhProcess.ProcessBo;
import com.maywide.grid.boot.api5g.bean.queFgbQueryRefund.QueFgbQueryRefundReq;
import com.maywide.grid.boot.api5g.bean.queFgbQueryRefund.QueFgbQueryRefundResp;
import com.maywide.grid.boot.api5g.bean.queFgbQueryRefund.QueryRefund;
import com.maywide.grid.boot.baseapi.sysparam.SysparamCache;
import com.maywide.grid.boot.baseapi.sysparam.SysparamGcodeCons;
import com.maywide.tool.shell.ShellUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Lookup;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.maywide.grid.boot.api5g.service.M5gMobileService.ID_IMG_SIZE;

/**
 * <AUTHOR>
 */
@Service
public class M5gCustService extends CommonService {

    @Value("${photo.upload.sftp.ip:}")
    private String ip;

    @Value("${photo.upload.sftp.port:}")
    private String port;

    @Value("${photo.upload.sftp.user:}")
    private String user;

    @Value("${photo.upload.sftp.passwd:}")
    private String passwd;


    @Autowired
    private ParamService paramService;

    @Lookup
    public ShellUtil getShellUtil() {
        return null;
    }

    @Resource
    private ProcessService processService;

    @Resource
    private M5gMobileService m5gMobileService;


    /**
     * 机主实名非文本
     * @param req
     * @return
     * @throws Exception
     */
    public ReturnInfo machineOwnerRealAuth(MachineOwnerRealAuth req) throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"参数不可为空");

        if (StringUtils.isBlank(req.getIdPersonUrl())) {
            String idPersonUrl = m5gMobileService.base64ToMultipartFile(req.getIdpicImgstr(), loginInfo);
            CheckUtils.checkEmpty(idPersonUrl, "芯片照上传失败，请重新提交！ ");
            req.setIdPersonUrl(idPersonUrl);
        }

        //有效期转换
        if(StringUtils.isNotEmpty(req.getEdate()) && req.getEdate().equals("长期")){
            req.setEdate("2099-12-12");
        }
        //名族转换
        String nationaltypename = req.getNationaltypename();
        nationaltypename = nameExChangeCode(SysparamGcodeCons.FGB_NATIONAL_TYPE, nationaltypename);
        req.setNationaltypename(nationaltypename);

        //性别转换
        String gender = req.getGender();
        gender = nameExChangeCode(SysparamGcodeCons.FGB_GENDER, gender);
        req.setGender(gender);

        //图片转换
        // 读取Sftp图片转成Base64

        req.setFrontpicImgStr(getBase64ImageFromSftp(req.getFrontpicurl(), ID_IMG_SIZE)); //正面图片编码
        req.setBackpicImgStr(getBase64ImageFromSftp(req.getBackpicurl(), ID_IMG_SIZE));//反面图片编码
//        req.setIdpicImgstr(getBase64ImageFromSftp(req.getIdpicImgstr(), ID_IMG_SIZE));


        // 人像照使用阿里活体检测不用获取base64编码
        PrvSysparam data = paramService.getData("REAL_NAME_AUTH_TYPE", "TYPE");
        if (data != null && "4".equals(data.getData())) {
            req.setImgBase64(req.getCertify_id());
            req.setLiveness_type("02");
            req.setImg_watermark_tag("0");
            String txt = "5G开户 广西省-MBOSS " + loginInfo.getLoginname() + " " + DateUtils.formatTime(new Date());
            req.setImgWatermark(txt);
        } else {
            req.setImgBase64(getBase64ImageFromSftp(req.getPicurl(), ID_IMG_SIZE));
            if (StringUtils.isNotBlank(req.getPicurl2())) {
                req.setRecent_img2(getBase64ImageFromSftp(req.getPicurl2(), ID_IMG_SIZE));
            }
        }

        //boss认证
        String response = getBossHttpInfo(req.getBizorderid(), BizConstant.M5gInterface.OWER_CERT_REALNAME_AUTH, buildMachineOwnerRealAuthParam(req, loginInfo), loginInfo);
        String output = getOutput(response, BizConstant.M5gInterface.OWER_CERT_REALNAME_AUTH, returnInfo);
        log.info(output);
        JSONObject outObj = JSONObject.parseObject(output);
        returnInfo.setMessage(outObj.getString("serialno"));
        return returnInfo;
    }

    /**
     * 机主文本身份校验（适用于代办开户机主实名认证）
     * @param req 机主实名认证入参
     *
     * @return
     * @throws Exception
     */
    public ReturnInfo machineOwnerTextAuth(MachineOwnerTextAuthReq req) throws  Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"参数不可为空");
        String resposne = getBossHttpInfo(req.getBizorderid(), BizConstant.M5gInterface.OWER_REALNAME_AUTH, buildReqParam(req, loginInfo), loginInfo);
        String output = getOutput(resposne, BizConstant.M5gInterface.OWER_REALNAME_AUTH, returnInfo);
        log.info(output);
        JSONObject outObj = JSONObject.parseObject(output);
        if (outObj.getString("authResult").equals("N")) {
            throw new BusinessException(outObj.getString("message"));

        }
//        if (!req.getBizorderid().equals(outObj.getString("serialno"))) {
//            throw new BusinessException("机主认证失败,流水号异常");
//        }
        returnInfo.setMessage(outObj.getString("serialno"));
        return returnInfo;
    }


    /**
     * 关系证明图片上传
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo relationFileUpload(RelationFileUploadReq req , RelationFileUploadResp resp)throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        JSONObject uploadImgParams = new JSONObject();
        uploadImgParams.put("img", req.getFaceImg());
        uploadImgParams.put("busi_sence", "14"); //
        uploadImgParams.put("img_type", "1401"); //

        String uploadResponse = getBossHttpInfoJson(req.getBizorderid(), BizConstant.M5gInterface.GW_UPLOAD_IMG , uploadImgParams, loginInfo);
        JSONObject uploadImgResp = JSONObject.parseObject(uploadResponse);
        if ("0".equals(uploadImgResp.getString("status"))) {

            // 获取上传后图片ID
            String imgId = uploadImgResp.getString("output");
            resp.setImgId(imgId);
        }
        return returnInfo;
    }

    /**
     * 收入证明图片上传
     * @return
     * @throws Exception
     */
    public String incomeFileUpload(String imgStr)throws Exception{
        LoginInfo loginInfo = getLoginInfo();
        JSONObject uploadImgParams = new JSONObject();
        uploadImgParams.put("img", imgStr);
        uploadImgParams.put("busi_sence", "15"); //
        uploadImgParams.put("img_type", "1501"); //

        String uploadResponse = getBossHttpInfoJson(BossHttpClientImpl.createRequestid(), BizConstant.M5gInterface.GW_UPLOAD_IMG , uploadImgParams, loginInfo);
        JSONObject uploadImgResp = JSONObject.parseObject(uploadResponse);
        if ("0".equals(uploadImgResp.getString("status"))) {

            // 获取上传后图片ID
            return uploadImgResp.getString("output");
        }
        return "";
    }

    /**
     * 请求
     * @param bizorderid
     * @param servicecode
     * @param req2Boss
     * @param loginInfo
     * @return
     * @throws Exception
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public String getBossHttpInfoJson(String bizorderid, String servicecode,
                                      Object req2Boss, LoginInfo loginInfo) throws Exception{
        String requestContent = JSONObject.toJSONString(req2Boss);
        if(StringUtils.isNotBlank(requestContent)){
            log.info("=========>servicecode:"+servicecode+"=========>"+"requestContent:"+requestContent);
        }
        String response = bossHttpClientService.requestPost(bizorderid,
                servicecode, requestContent, loginInfo);
        if (StringUtils.isEmpty(response)) {
            throw new BusinessException("BOSS接口调用出错，没有返回数据");
        }
        if(StringUtils.isNotBlank(response)){
            log.info("=========>response:"+response+"=========>");
        }
        return response;
    }
    /**
     * 代办人开户认证
     * @param req
     * @return
     * @throws Exception
     */
    public ReturnInfo replaceUserAuth(ReplaceUserAuthReq req) throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"参数不可为空");

        if (StringUtils.isBlank(req.getIdPersonUrl())) {
            String idPersonUrl = m5gMobileService.base64ToMultipartFile(req.getIdpicImgstr(), loginInfo);
            CheckUtils.checkEmpty(idPersonUrl, "芯片照上传失败，请重新提交！ ");
            req.setIdPersonUrl(idPersonUrl);
        }

        //有效期转换
        if(StringUtils.isNotEmpty(req.getEdate()) && req.getEdate().equals("长期")){
            req.setEdate("2099-12-12");
        }
        //名族转换
        String nationaltypename = req.getNationaltypename();
        nationaltypename = nameExChangeCode(SysparamGcodeCons.FGB_NATIONAL_TYPE, nationaltypename);
        req.setNationaltypename(nationaltypename);

        //性别转换
        String gender = req.getGender();
        gender = nameExChangeCode(SysparamGcodeCons.FGB_GENDER, gender);
        req.setGender(gender);

        //图片转换
        // 读取Sftp图片转成Base64

        req.setFrontpicImgStr(getBase64ImageFromSftp(req.getFrontpicurl(), ID_IMG_SIZE)); //正面图片编码
        req.setBackpicImgStr(getBase64ImageFromSftp(req.getBackpicurl(), ID_IMG_SIZE));//反面图片编码
//        req.setIdpicImgstr(getBase64ImageFromSftp(req.getIdpicImgstr(), ID_IMG_SIZE));

        // 人像照使用阿里活体检测不用获取base64编码
        PrvSysparam data = paramService.getData("REAL_NAME_AUTH_TYPE", "TYPE");
        if (data != null && "4".equals(data.getData())) {
            req.setImgBase64(req.getCertify_id());
            req.setLiveness_type("02");
            req.setImg_watermark_tag("0");
            String txt = "5G开户 广西省-MBOSS " + loginInfo.getLoginname() + " " + DateUtils.formatTime(new Date());
            req.setImgWatermark(txt);
        } else {
            req.setImgBase64(getBase64ImageFromSftp(req.getPicurl(), ID_IMG_SIZE));
            if (StringUtils.isNotBlank(req.getPicurl2())) {
                req.setRecent_img2(getBase64ImageFromSftp(req.getPicurl2(), ID_IMG_SIZE));
            }
        }


        //boss认证
        String response = getBossHttpInfo(req.getBizorderid(), BizConstant.M5gInterface.AGENT_REALNAME_AUTH, buildReqParam(req), loginInfo);
        String output = getOutput(response, BizConstant.M5gInterface.OWER_REALNAME_AUTH, returnInfo);
        log.info(output);
        JSONObject outObj = JSONObject.parseObject(output);
        if (outObj.getString("authResult").equals("N")) {
            throw new BusinessException(outObj.getString("message"));

        }
        if (!req.getSerialno().equals(JSONObject.parseObject(output).getString("serialno"))){
            throw new BusinessException("代办人认证失败,流水号错误");
        }
        returnInfo.setMessage(outObj.getString("serialno"));
        return returnInfo;



    }

    private Map buildMachineOwnerRealAuthParam(MachineOwnerRealAuth req, LoginInfo loginInfo) throws Exception {
        if (Objects.isNull(req)){
            return  Collections.EMPTY_MAP;
        }
        HashMap<String, String> param = new HashMap<>();
        param.put("custid",req.getCustId());//客户id
        param.put("certtype","B");//机主A 代办B
        param.put("cardtype","1");//证件类型1-身份证
        param.put("cardno",req.getCardNo());//身份证号
        param.put("name",req.getName());//姓名
        param.put("effectDate",req.getEffectDate());
        param.put("edate",req.getEdate());//有效时间
        param.put("iden_address",req.getIdenAddress());//住址
        param.put("ownertype","1");//证件归属
        param.put("imgBase64", req.getImgBase64()); //现场人像照1
        param.put("frontpicurl", req.getFrontpicurl());//证件正面
        param.put("frontpicImgStr", req.getFrontpicImgStr());//证件正面
        param.put("backpicurl", req.getBackpicurl());//证件反面
        param.put("backpicImgStr", req.getBackpicImgStr());//证件反面
        param.put("idpicImgStr", req.getIdpicImgstr());//证件人像
        param.put("nationaltypename",req.getNationaltypename());//民族
        param.put("gender", req.getGender());//性别
        param.put("opcode","BIZ_FGB_USER_ACTIVE");
        param.put("authType","A");
        param.put("optType","00");
        param.put("idPersonUrl",req.getIdPersonUrl()); //芯片照地址
        param.put("picurl",req.getPicurl()); //现场人像照1上传地址

        if (StringUtils.isNotBlank(req.getPicurl2())) {
            param.put("picurl2", req.getPicurl2()); //现场人像照2上传地址
            param.put("recent_img2", req.getRecent_img2()); //现场人像照2
        }

        String bossSerialno = getBossSerialno(loginInfo, req.getBizorderid());
        param.put("serialno",bossSerialno);

        // 阿里活体检测参数
        param.put("liveness_type", req.getLiveness_type());
        param.put("liveness_biz_no", req.getLiveness_biz_no());
        param.put("imgWatermark", req.getImgWatermark());
        param.put("img_watermark_tag", req.getImg_watermark_tag());

        return param;
    }

    private Map buildReqParam(ReplaceUserAuthReq req) {
        if (Objects.isNull(req)){
            return  Collections.EMPTY_MAP;
        }
        HashMap<String, String> param = new HashMap<>();
        param.put("custid",req.getCustId());//客户id
        param.put("certtype","B");//机主A 代办B
        param.put("cardtype","1");//证件类型1-身份证
        param.put("cardno",req.getCardNo());//身份证号
        param.put("name",req.getName());//姓名
        param.put("effectDate",req.getEffectDate());
        param.put("edate",req.getEdate());//有效时间
        param.put("iden_address",req.getIdenAddress());//住址
        param.put("ownertype","2");//证件归属
        param.put("imgBase64", req.getImgBase64()); //现场人像照
        param.put("frontpicurl", req.getFrontpicurl());//证件正面
        param.put("frontpicImgStr", req.getFrontpicImgStr());//证件正面
        param.put("backpicurl", req.getBackpicurl());//证件反面
        param.put("backpicImgStr", req.getBackpicImgStr());//证件反面
        param.put("idpicImgStr", req.getIdpicImgstr());//证件人像
        param.put("nationaltypename",req.getNationaltypename());//民族
        param.put("gender",req.getGender());//性别
        param.put("hasPriv",req.getHasPriv());
        param.put("linkPhone",req.getLinkPhone());//代办人联系电话
        param.put("serialno",req.getSerialno());
        param.put("opcode","BIZ_FGB_AGENTUSER_NEW");
        param.put("authType","K");
        param.put("optType","01");
        param.put("relationType",req.getRelationType());
        param.put("idPersonUrl",req.getIdPersonUrl()); //芯片照地址
        param.put("picurl",req.getPicurl()); //现场人像照1上传地址

        if (StringUtils.isNotBlank(req.getPicurl2())) {
            param.put("picurl2", req.getPicurl2()); //现场人像照2上传地址
            param.put("recent_img2", req.getRecent_img2()); //现场人像照2
        }

        if (StringUtils.isNotBlank(req.getRelationFile1())){
            param.put("relationFile1",req.getRelationFile1());
        }
        if (StringUtils.isNotBlank(req.getRelationFile2())){
            param.put("relationFile2",req.getRelationFile2());
        }
        if (StringUtils.isNotBlank(req.getRelationFile3())){
            param.put("relationFile3",req.getRelationFile3());
        }


        // 阿里活体检测参数
        param.put("liveness_type", req.getLiveness_type());
        param.put("liveness_biz_no", req.getLiveness_biz_no());
        param.put("imgWatermark", req.getImgWatermark());
        param.put("img_watermark_tag", req.getImg_watermark_tag());

        return param;
    }


    private String getBase64ImageFromSftp(String photoPath, long compressSize) throws BusinessException, IOException {
        String photoBase64;
        InputStream inputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        final ShellUtil shellUtil = this.getShellUtil();
        try {
            String host = null;
            if (StringUtils.isNotBlank(ip)) {
                String[] split = StringUtils.split(ip, ",");
                host = split[0];
            }
            if (host == null) {
                throw new BusinessException("CMMS: 图片服务器IP未配置");
            }
            shellUtil.init(host, Integer.parseInt(port), user, passwd);
            try {
                inputStream = shellUtil.get(photoPath);
                byteArrayOutputStream = new ByteArrayOutputStream();
                // 将读取图片输入流转化成输出流
                IOUtils.copy(inputStream, byteArrayOutputStream);
                // 将图片转成jpg格式
                byte[] bytes = byteArrayOutputStream.toByteArray();
                // 将图片压缩到规定的范围内
//                bytes = PicUtils.compressPicForScale(bytes, compressSize);

                // 将图片输出流转化为base64
                photoBase64 = getBase64Image(bytes, BizConstant.IMAGE_FORMAT.JPG);

            } catch (Exception e) {
                log.info(" sftp read fail. " + e);
//                throw new com.maywide.core.mcr.BusinessException(e.getMessage());
                throw new BusinessException("网络异常，请重试！");
            }
        } catch (Exception e) {
            log.info(" sftp file fail. " + e);
//            throw new BusinessException(e.getMessage());
            throw new BusinessException("网络异常，请重试！");
        } finally {
            try {
                shellUtil.close();
            }catch (Exception e){
                log.error(e.getMessage());
            }
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            }catch(Exception e) {
                log.error(e.getMessage());
            }
            if (byteArrayOutputStream != null) {
                byteArrayOutputStream.close();
            }
        }
        return photoBase64;
    }

    private String getBase64Image(byte[] bytes, String ext) {
        String base64Image = Base64.getEncoder().encodeToString(bytes);
        StringBuffer sb = new StringBuffer();
        sb.append("data:image/").append(ext).append(";").append("base64,").append(base64Image);
        return sb.toString();
    }


    private String nameExChangeCode(String gcode, String mname) throws Exception {
        List<PrvSysparam> sysparamList = SysparamCache.getSysparamList(gcode);
        Iterator<PrvSysparam> iterator = sysparamList.iterator();
        while (iterator.hasNext()) {
            PrvSysparam next = iterator.next();
            String nextMname = next.getMname();
            if (StringUtils.contains(nextMname, mname)) {
                return next.getMcode();
            }
        }
        return null;
    }


    /**
     * 构建boss请求参数
     * @return
     */
    private Map<String,Object> buildReqParam(MachineOwnerTextAuthReq req, LoginInfo loginInfo) throws Exception {
        if (Objects.isNull(req)){
            return Collections.EMPTY_MAP;
        }
        HashMap<String, Object> param = new HashMap<>();
        param.put("custid",req.getCustId());
        param.put("certtype","A"); //机主A，代办B
        param.put("cardtype",1);//证件类型1-身份证
        param.put("cardno",req.getCardNo());//证件号
        param.put("name",req.getName());//机主姓名

        param.put("effectDate",req.getEffectDate());//证件有效期开始时间
        if(StringUtils.isNotEmpty(req.getEdate()) && req.getEdate().equals("长期")){
            req.setEdate("2099-12-12");
        }
        param.put("edate", req.getEdate());//证件有效期结束时间
        param.put("iden_address",req.getIdenAddress());//地址
        param.put("ownertype",1);//证件归属1-机主本人
        param.put("opcode","BIZ_FGB_AGENTUSER_NEW");
        param.put("authType","J");
        param.put("optType","01");

        String bossSerialno = getBossSerialno(loginInfo, req.getBizorderid());
        param.put("serialno",bossSerialno);
        return param;




    }

    /**
     * 3.1.12.	国网客户信息查询接口
     * @param req
     * @param resp
     * @return
     */
    public ReturnInfo m5gQueCustinfo(M5gQueCustinfoReq req, M5gQueCustinfoResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        // 请求参数检查
        Long custId = req.getCustId();
        CheckUtils.checkNull(custId, "客户编号为空,请检查!");
        if (custId.longValue() == -1L) {
            CheckUtils.checkEmpty(req.getAccessNum(), "客户编号或者手机号为空,请检查!");
            req.setCity(loginInfo.getCity());
        }
        M5gCustInfo m5gCustInfo = gwCcQrycustinfo(req.getCustId(), req.getAccessNum(), req.getCity(), req.getBizorderid(), loginInfo);
        // 返回
        BeanUtils.copyProperties(resp, m5gCustInfo);
        // 查询 用户状态翻译
        List<PrvSysparam> statusList = SysparamCache.getSysparamList(BizConstant.SysparamGcode.M5G_SERV_STATE_CODE);
        // 查询国网用户信息
        List<M5gCustUserInfo> userInfos = resp.getUserInfos();
        if (userInfos != null && !userInfos.isEmpty()) {
            userInfos.parallelStream().forEach(userInfo -> {
                Long servId = userInfo.getServId();
                String state = userInfo.getState();
                userInfo.setStateName(state);
                // 翻译 state
                statusList.parallelStream().forEach(prvSysparam -> {
                    String mcode = prvSysparam.getMcode();
                    if (StringUtils.equals(state, mcode)) {
                        userInfo.setStateName(prvSysparam.getMname());
                    }
                });

                try {
                    List<M5gServInfo> servInfos = gwOcFindsubscriberbyaccessnum(custId, servId, req.getBizorderid(), loginInfo);
                    if (servInfos != null && !servInfos.isEmpty()) {
                        M5gServInfo servInfo = servInfos.get(0);
                        userInfo.setServInfo(servInfo);
                    }
                } catch (Exception e) {
                    log.error(String.format("=>[custid=%s&servid=%s]用户信息查询错误", custId, servId), e);
                }
            });
        }

        return returnInfo;
    }

    /**
     * 3.1.12.	国网客户信息查询接口
     * @return
     */
    public M5gCustInfo gwCcQrycustinfo(Long custid, String accessNum, String city, String bizorderid, LoginInfo loginInfo) throws Exception {
        // BOSS 参数
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("cust_id", custid);
        bossreq.put("access_num", accessNum);
        bossreq.put("city", city);

        // 调用BOSS接口
        String output = getBossHttpInfOutput(bizorderid, BizConstant.M5gInterface.GW_CC_QRYCUSTINFO, bossreq, loginInfo);
        // 转成对象
        M5gCustInfo m5gCustInfo = JSON.parseObject(output, M5gCustInfo.class);
        return m5gCustInfo;
    }

    /**
     * 3.1.12.	国网客户信息查询接口
     * @return
     */
    public List<M5gServInfo> gwOcFindsubscriberbyaccessnum(Long custid, Long servid, String bizorderid, LoginInfo loginInfo) throws Exception {

        CheckUtils.checkNull(custid, "客户编号为空,请检查!");
        CheckUtils.checkNull(servid, "用户ID为空,请检查!");

        // BOSS 参数
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("cust_id", custid);
        bossreq.put("serv_id", servid);

        // 调用BOSS接口
        String output = getBossHttpInfOutput(bizorderid, BizConstant.M5gInterface.GW_OC_FINDSUBSCRIBERBYACCESSNUM, bossreq, loginInfo);
        // 转成对象
        List<M5gServInfo> m5gServInfos = JSON.parseObject(output, new TypeReference<ArrayList<M5gServInfo>>() {
        });
        return m5gServInfos;
    }

    /**
     * 3.1.20.	5G开票信息查询
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queM5gInvoiceinfo(QueM5gInvoiceinfoReq req, QueM5gInvoiceinfoResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        List<M5gInvoiceinfo> m5gInvoiceinfos = gwAssetQueryinvoiceinfo(req.getCustid(), req.getServid(), req.getMobileno(), req.getStime(), req.getEtime(),
                req.getSourceType(),req.getBizorderid(), loginInfo,returnInfo);

        List<M5gInvoiceinfoBo> respData = new ArrayList<>();
        // 增加发票状态类型
        if (m5gInvoiceinfos != null && !m5gInvoiceinfos.isEmpty()) {
            List<PrvSysparam> list = SysparamCache.getSysparamList(SysparamGcodeCons.FGB_NOTE_STATE);
            Iterator<M5gInvoiceinfo> iterator = m5gInvoiceinfos.iterator();
            while (iterator.hasNext()) {
                M5gInvoiceinfo next = iterator.next();
                M5gInvoiceinfoBo m5gInvoiceinfoBo = new M5gInvoiceinfoBo();
                BeanUtils.copyProperties(m5gInvoiceinfoBo, next);
                Iterator<PrvSysparam> params = list.iterator();
                while (params.hasNext()) {
                    PrvSysparam param = params.next();
                    String mcode = param.getMcode();
                    String noteState = next.getNoteState();
                    if (StringUtils.equals(noteState, mcode)) {
                        m5gInvoiceinfoBo.setNoteStateName(param.getMname());
                        break;
                    }
                }
                respData.add(m5gInvoiceinfoBo);
            }
        }
        resp.setData(respData);
        return returnInfo;
    }

    /**
     * 20220929
     * 开票信息用户类型
     * 1:个人类型
     * 2:集团类型
     * BOSS目前仅支持个人类型
     */
    private static final String SUBSCRIBER_TYPE_DEFAULT = "1";

    /**
     * 3.1.20.	5G开票信息查询
     * @param custid
     * @param servid
     * @param mobileno
     * @param stime
     * @param etime
     * @param bizorderid
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public List<M5gInvoiceinfo> gwAssetQueryinvoiceinfo(
            Long custid,
            Long servid,
            String mobileno,
            String stime,
            String etime,
            String sourceType,
            String bizorderid,
            LoginInfo loginInfo,ReturnInfo returnInfo) throws Exception {
        CheckUtils.checkNull(custid, "CMMS: 客户编号不能为空. ");
        CheckUtils.checkEmpty(stime, "CMMS: 开始时间不能为空. ");
        CheckUtils.checkEmpty(etime, "CMMS: 结束时间不能为空. ");
        CheckUtils.checkEmpty(sourceType, "CMMS: 发票类型不能为空. ");
        if (servid == null && StringUtils.isBlank(mobileno)) {
            throw new BusinessException("CMMS: 用户编号和手机号不能同时为空. ");
        }

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("custid", custid);
        bossreq.put("stime", stime);
        bossreq.put("etime",etime);
        bossreq.put("servid", servid);
        bossreq.put("mobileno", mobileno);
        bossreq.put("source_type", sourceType);
        bossreq.put("subscriber_type", SUBSCRIBER_TYPE_DEFAULT);

        String servicCode = BizConstant.M5gInterface.GW_ASSET_QUERYINVOICEINFO;
        String response = getBossHttpInfo(bizorderid,servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);
        if(StringUtils.isNotBlank(output)){
            List<M5gInvoiceinfo> list = JSON.parseObject(output, new TypeReference<List<M5gInvoiceinfo>>() {
            });
            return list;
        }
        return new ArrayList<>();

    }

    private String getOutput(String response,String servicecode,ReturnInfo returnInfo)throws Exception{
        String bossRespOutput = null;
        if (StringUtils.isEmpty(response)) {
            throw new BusinessException("BOSS接口调用出错，没有返回数据");
        }
        if(StringUtils.isNotBlank(response)){
            log.info("=========>[{}]response:"+response+"=========>");
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setDateFormat(dateFormat);
        JsonNode nodeTree = objectMapper.readTree(response);
        JsonNode statusNode = nodeTree.get("status");
        JsonNode message = nodeTree.get("message");
        if(null != statusNode){
            String statusStr = statusNode.toString();
            if(statusStr.equals("\"301\"")){
                returnInfo.setCode(Long.valueOf(statusStr.substring(1,statusStr.length()-1)));
                returnInfo.setMessage(message.toString());
                return "";
            }
        }
        parseBossResponse(nodeTree, servicecode);
        JsonNode outputNode = nodeTree.get("output");
        if (null != outputNode) {
            bossRespOutput = outputNode.toString();
        }
        return bossRespOutput;
    }


    /**
     * 3.1.20.	5G开票信息开具
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo assetApplyinoice(AssetApplyinoiceReq req, AssetApplyinoiceResp resp)throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"请求信息不能为空");
        AssetApplyinoiceResp response = gwAssetApplyinoice(req,loginInfo);
        if(response!=null){
            resp.setMessage(response.getMessage());
            resp.setStatus(response.getStatus());
        }
        log.info("response=====[]",response);
        return returnInfo;
    }

    public AssetApplyinoiceResp gwAssetApplyinoice(AssetApplyinoiceReq req,LoginInfo loginInfo)throws Exception{

        CheckUtils.checkNull(req.getServid(), "CMMS: 用户id不能为空. ");
        CheckUtils.checkEmpty(req.getTradeDate(), "CMMS: 开票时间不能为空. ");
        CheckUtils.checkEmpty(req.getPrintid(), "CMMS: 开票流水号不能为空. ");
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("serv_id", req.getServid());
        bossreq.put("trade_date", req.getTradeDate());
        bossreq.put("print_id", req.getPrintid());
        String response = getBossHttpInfo(req.getBizorderid(), BizConstant.M5gInterface.GW_ASSET_APPLYINOICE, bossreq, loginInfo);
        AssetApplyinoiceResp resp = JSON.parseObject(response, new TypeReference<AssetApplyinoiceResp>() {
        });
        return resp;
    }

    /**
     * 3.1.20.	5Gboss 缴费历史查询
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo assetQueErycharge(AssetQueErychargeReq req, AssetQueErychargeResp resp)throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"请求信息不能为空");
        List<QueRychargeInfo> list = gwAssetQueCharge(req,loginInfo,returnInfo);
        if(list!=null && list.size()>0){
            resp.setList(list);
        }
        return returnInfo;
    }

    public List<QueRychargeInfo> gwAssetQueCharge(AssetQueErychargeReq req, LoginInfo loginInfo,ReturnInfo returnInfo)throws Exception{

        CheckUtils.checkNull(req.getServid(), "CMMS: 客户编号不能为空. ");
        CheckUtils.checkEmpty(req.getBeginTime(), "CMMS: 开始时间不能为空. ");
        CheckUtils.checkEmpty(req.getEndTime(), "CMMS: 结束时间不能为空. ");

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("serv_id", req.getServid());
        if(StringUtils.isNotBlank(req.getAccessNum())){
            bossreq.put("access_num", req.getAccessNum());
        }
        if(StringUtils.isNotBlank(req.getCity())){
            bossreq.put("city", req.getCity());
        }
        bossreq.put("begin_time", req.getBeginTime());
        bossreq.put("end_time", req.getEndTime());

//        String output = getBossHttpInfOutput(req.getBizorderid(), BizConstant.M5gInterface.GW_ASSET_QUERYCHARGE, bossreq, loginInfo);
//        List<QueRychargeInfo> list = JSON.parseObject(output, new TypeReference<List<QueRychargeInfo>>() {
//        });


        String servicCode = BizConstant.M5gInterface.GW_ASSET_QUERYCHARGE;
        String response = getBossHttpInfo(req.getBizorderid(),servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);
        if(StringUtils.isNotBlank(output)){
            List<QueRychargeInfo> list = JSON.parseObject(output, new TypeReference<List<QueRychargeInfo>>() {
            });
            return list;
        }

        return new ArrayList<>();
    }

    /**
     * 手机号服务密码验证
     * @return
     */
    public ReturnInfo checkMobilenoPwd(CheckMobilenoPwdReq req, CheckMobilenoPwdResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        // 地市设置默认值
        req.setCity(StringUtils.isBlank(req.getCity()) ? loginInfo.getCity() : req.getCity());

        gwCheckpwdandaccessnum(req, loginInfo);

        return returnInfo;
    }

    /**
     * 3.2.17.	服务密码验证接口
     * @param req
     * @param loginInfo
     * @throws Exception
     */
    public void gwCheckpwdandaccessnum(CheckMobilenoPwdReq req, LoginInfo loginInfo) throws Exception {
        CheckUtils.checkEmpty(req.getAccessNum(), "CMMS:手机号为空");
        CheckUtils.checkEmpty(req.getCustId(), "CMMS:客户编号为空");
        CheckUtils.checkEmpty(req.getCity(), "CMMS:地市不能为空");

        if (StringUtils.isBlank(req.getPassword()) &&
                (StringUtils.isBlank(req.getCardno()) || StringUtils.isBlank(req.getName()))) {
            throw new BusinessException("CMMS:服务密码验证需要服务密码或者证件号码加证件名称");
        }

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("access_num", req.getAccessNum());
        bossreq.put("password", req.getPassword());
        bossreq.put("city", req.getCity());
        bossreq.put("cust_id", req.getCustId());
        bossreq.put("cardno", req.getCardno());
        bossreq.put("name", req.getName());

        getBossHttpInfOutput(req.getBizorderid(), BizConstant.M5gInterface.GW_CHECKPWDANDACCESSNUM, bossreq, loginInfo);

    }


    //历史账单查询

    /**
     *  5Gboss 历史账单查询
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queErycustBill(QueErycustBillReq req, QueErycustBillResp resp)throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"请求信息不能为空");
        List<Erycust> info = gwAssetQueRycustBill(req,loginInfo,returnInfo);
        if(info!=null && info.size()>0){
            resp.setList(info);
        }
        return returnInfo;
    }

    public List<Erycust> gwAssetQueRycustBill(QueErycustBillReq req, LoginInfo loginInfo,ReturnInfo returnInfo)throws Exception{

        CheckUtils.checkNull(req.getServid(), "CMMS: 客户编号不能为空. ");
        CheckUtils.checkEmpty(req.getBeginTime(), "CMMS: 开始时间不能为空. ");
        CheckUtils.checkEmpty(req.getEndTime(), "CMMS: 结束时间不能为空. ");

        Date bTime = DateUtils.parseTime(req.getBeginTime());
        Date eTime = DateUtils.parseTime(req.getEndTime());

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("serv_id", req.getServid());
        bossreq.put("begin_time", DateUtils.getFormatDateString(bTime,DateUtils.FORMAT_YYYYMM));
        bossreq.put("end_time", DateUtils.getFormatDateString(eTime,DateUtils.FORMAT_YYYYMM));

        if(StringUtils.isNotBlank(req.getAccessnum())){
            bossreq.put("access_num", req.getAccessnum());
        }
        if(StringUtils.isNotBlank(req.getCity())){
            bossreq.put("city", req.getCity());
        }
        if(StringUtils.isNotBlank(req.getCustid())){
            bossreq.put("cust_id", req.getCustid());
        }else {
            bossreq.put("cust_id", "-1");
        }
        bossreq.put("subscriber_type", "00");

//        String response = getBossHttpInfOutput(req.getBizorderid(), BizConstant.M5gInterface.GW_ASSET_QUERYCUSTBILL, bossreq, loginInfo);
//        List<Erycust> info = JSON.parseObject(response, new TypeReference<List<Erycust>>() {
//        });


        String servicCode = BizConstant.M5gInterface.GW_ASSET_QUERYCUSTBILL;
        String response = getBossHttpInfo(req.getBizorderid(),servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);
        if(StringUtils.isNotBlank(output)){
            List<Erycust> info = JSON.parseObject(output, new TypeReference<List<Erycust>>() {
            });
            return info;
        }

        return new ArrayList<>();
    }



    //服务密码重置

    /**
     *  5Gboss 服务密码重置
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo resetPwd(ResetPwdReq req, ResetPwdResp resp)throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"请求信息不能为空");
        CheckUtils.checkNull(req.getCustid(), "CMMS: 客户编号不能为空. ");
        CheckUtils.checkEmpty(req.getAccessnum(), "CMMS: 手机号不能为空. ");
//        CheckUtils.checkEmpty(req.getOldpwd(), "CMMS: 旧密码不能为空. ");
        CheckUtils.checkEmpty(req.getNewpwd(), "CMMS: 新密码不能为空. ");
        CheckUtils.checkEmpty(req.getCardno(), "CMMS: 身份证号不能为空. ");
        CheckUtils.checkEmpty(req.getName(), "CMMS: 姓名不能为空. ");

        // 查询是否有配置流程，如果有记录流程并返回下一页
        BizProcessOrder bizProcessOrder = processService.startProcess(req.getProcessReq(), BizConstant.M5gInterface.GW_PASSWD_RESET);

        ResetPwdResp info = gwPasswdReset(req,loginInfo);
        if (info != null && info.getOutput() != null) {
            resp.setSerialno(info.getOutput().getSerialno());
            // 保存业务单
            CustOrder custOrder = register4Custoer(req, BizConstant.M5gInterface.GW_PASSWD_RESET, loginInfo);
            if (bizProcessOrder != null) {
                custOrder.setPcode(bizProcessOrder.getPcode());
                custOrder.setPoid(bizProcessOrder.getPoid());
            }
            getDAO().update(custOrder);
            req.getProcessDataReq().setPaySerialno(info.getOutput().getSerialno());
            // 把boss返回的数据存入当前环节的data
            String data = req.getProcessDataReq().getData();
            JSONObject jsonObject = JSONObject.parseObject(data);
            jsonObject.put("bossResp", info.getOutput());
            req.getProcessDataReq().setData(jsonObject.toJSONString());
            // 保存环节数据
            ProcessBo processBo = processService.saveProcessData(bizProcessOrder, req.getProcessDataReq(), loginInfo);
            if (processBo != null) {
                resp.setNextPage(processBo.getPage());
                resp.setPoid(bizProcessOrder.getPoid());
                resp.setCpcode(bizProcessOrder.getCurrent_step());
            }
        }
        resp.setMessage(info.getMessage());
        resp.setStatus(info.getStatus());
        return returnInfo;
    }

    public ResetPwdResp gwPasswdReset(ResetPwdReq req, LoginInfo loginInfo)throws Exception{
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("cust_id", req.getCustid());
        bossreq.put("access_num", req.getAccessnum());
        bossreq.put("new_pwd", req.getNewpwd());
        bossreq.put("name", req.getName());
        bossreq.put("cardno", req.getCardno());
        if(StringUtils.isNotBlank(req.getOldpwd())){
            bossreq.put("old_pwd", req.getOldpwd());
        }

        String response = getBossHttpInfo(req.getBizorderid(), BizConstant.M5gInterface.GW_PASSWD_RESET, bossreq, loginInfo);
        ResetPwdResp info = JSON.parseObject(response, new TypeReference<ResetPwdResp>() {});

        return info;
    }

    /**
     * 5Gboss 服务密码修改
     *
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
        public ReturnInfo uptPwd(ResetPwdReq req, ResetPwdResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req, "请求信息不能为空");
        CheckUtils.checkNull(req.getCustid(), "CMMS: 客户编号不能为空. ");
        CheckUtils.checkEmpty(req.getAccessnum(), "CMMS: 手机号不能为空. ");
        CheckUtils.checkEmpty(req.getOldpwd(), "CMMS: 旧密码不能为空. ");
        CheckUtils.checkEmpty(req.getNewpwd(), "CMMS: 新密码不能为空. ");
        CheckUtils.checkEmpty(req.getCardno(), "CMMS: 身份证号不能为空. ");

        // 查询是否有配置流程，如果有记录流程并返回下一页
        BizProcessOrder bizProcessOrder = processService.startProcess(req.getProcessReq(), BizConstant.M5gInterface.GW_PASSWD_UPT);

        ResetPwdResp info = gwPasswdUpt(req, loginInfo);
        if (info != null && info.getOutput() != null) {
            resp.setSerialno(info.getOutput().getSerialno());
            // 保存业务单
            CustOrder custOrder = register4Custoer(req, BizConstant.M5gInterface.GW_PASSWD_UPT, loginInfo);
            if (bizProcessOrder != null) {
                custOrder.setPcode(bizProcessOrder.getPcode());
                custOrder.setPoid(bizProcessOrder.getPoid());
            }
            getDAO().update(custOrder);
            req.getProcessDataReq().setPaySerialno(info.getOutput().getSerialno());
            // 把boss返回的数据存入当前环节的data
            String data = req.getProcessDataReq().getData();
            JSONObject jsonObject = JSONObject.parseObject(data);
            jsonObject.put("bossResp", info.getOutput());
            req.getProcessDataReq().setData(jsonObject.toJSONString());
            // 保存环节数据
            ProcessBo processBo = processService.saveProcessData(bizProcessOrder, req.getProcessDataReq(), loginInfo);
            if (processBo != null) {
                resp.setNextPage(processBo.getPage());
                resp.setPoid(bizProcessOrder.getPoid());
                resp.setCpcode(bizProcessOrder.getCurrent_step());
            }
        }
        resp.setMessage(info.getMessage());
        resp.setStatus(info.getStatus());
        return returnInfo;
    }

    public ResetPwdResp gwPasswdUpt(ResetPwdReq req, LoginInfo loginInfo) throws Exception {

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("cust_id", req.getCustid());
        bossreq.put("access_num", req.getAccessnum());
        bossreq.put("new_pwd", req.getNewpwd());
        bossreq.put("old_pwd", req.getOldpwd());
        bossreq.put("cardno", req.getCardno());

        String response = getBossHttpInfo(req.getBizorderid(), BizConstant.M5gInterface.GW_PASSWD_UPT, bossreq, loginInfo);
        ResetPwdResp info = JSON.parseObject(response, new TypeReference<ResetPwdResp>() {});

        return info;
    }

    protected CustOrder register4Custoer(ResetPwdReq req, String opcode, LoginInfo loginInfo) throws Exception {

        CustOrder bizCustOrder = new CustOrder();
        bizCustOrder.setId(Long.parseLong(req.getBizorderid()));
        bizCustOrder.setAreaid(loginInfo.getAreaid());
        bizCustOrder.setCity(loginInfo.getCity());
        bizCustOrder.setCustid(req.getCustid());
        bizCustOrder.setOpcode(opcode);
        bizCustOrder.setOperator(loginInfo.getOperid());
        bizCustOrder.setOprdep(loginInfo.getDeptid());
        bizCustOrder.setOptime(new Date());
        bizCustOrder.setOrdercode(req.getBizorderid());
        bizCustOrder.setDevname(null);
        bizCustOrder.setOrderstatus(BizConstant.BizCustorderOrderstatus.SYNC);
        bizCustOrder.setSyncmode(BizConstant.BizCustorderSyncmode.SYNC);
        bizCustOrder.setDevno(null);
        bizCustOrder.setServid(null);
        bizCustOrder.setName(req.getName());
        bizCustOrder.setPortalOrder(null);

        getDAO().save(bizCustOrder);

        return bizCustOrder;
    }

    public ReturnInfo querComprehensiveList(QuerComprehensiveListReq req, QuerComprehensiveListResp resp)throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"请求信息不能为空");
        List<QueryComprehensiveBossResp> resps = gwAssetQrycdrbil(req,loginInfo,returnInfo);
        if(resps!=null && resps.size()>0){
            resp.setList(resps);
        }
        return returnInfo;
    }

    public List<QueryComprehensiveBossResp> gwAssetQrycdrbil(QuerComprehensiveListReq req, LoginInfo loginInfo,ReturnInfo returnInfo)throws Exception{

        CheckUtils.checkNull(req.getBillType(), "CMMS: 详单类型不能为空. ");
        CheckUtils.checkEmpty(req.getBeginTime(), "CMMS: 开始时间不能为空. ");
        CheckUtils.checkEmpty(req.getEndTime(), "CMMS: 结束时间不能为空. ");

        if(StringUtils.isBlank(req.getAccessNum()) && req.getServid()==null){
            CheckUtils.checkEmpty(null, "CMMS: servid 和 手机号不能同时为空. ");
        }

        Date bTime = DateUtils.parseTime(req.getBeginTime());
        Date eTime = DateUtils.parseTime(req.getEndTime());

        Map<String, Object> bossreq = new HashMap<>();
        if(req.getServid()!=null){
            bossreq.put("serv_id", req.getServid());
        }

        bossreq.put("begin_time", DateUtils.formatDate(bTime));
        bossreq.put("end_time", DateUtils.formatDate(eTime));

        if(StringUtils.isNotBlank(req.getAccessNum())){
            bossreq.put("access_num", req.getAccessNum());
        }

        bossreq.put("bill_type", req.getBillType());
        if(StringUtils.isNotBlank(req.getCustid())){
            bossreq.put("cust_id", req.getCustid());
        }else {
            bossreq.put("cust_id", "-1");
        }
        bossreq.put("subscriber_type", "00");

        String servicCode = BizConstant.M5gInterface.GW_ASSET_QRYCDRBIL;
        String response = getBossHttpInfo(req.getBizorderid(),servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);
        if(StringUtils.isNotBlank(output)){

            QueryComprehensiveBossResp queryComprehensiveBossResp = new QueryComprehensiveBossResp();
            if (req.getBillType().equals("202")) {
                List<VoiceBill> voiceBillList = JSON.parseObject(output, new TypeReference<List<VoiceBill>>() {});
                queryComprehensiveBossResp.setVoiceBillList(voiceBillList);
            } else if (req.getBillType().equals("203")) {
                List<NetBill> netBillList = JSON.parseObject(output, new TypeReference<List<NetBill>>() {});
                queryComprehensiveBossResp.setNetBillList(netBillList);
            } else if(req.getBillType().equals("204")) {
                List<SmsBill> smsBillList = JSON.parseObject(output, new TypeReference<List<SmsBill>>() {});
                queryComprehensiveBossResp.setSmsBillList(smsBillList);
            }
            List<QueryComprehensiveBossResp> info = new ArrayList<>();
            info.add(queryComprehensiveBossResp);
            return info;
        }

        return new ArrayList<>();
    }

    /**
     * 3.2.10移网退费信息查询接口
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queFgbQueryRefundList(QueFgbQueryRefundReq req, QueFgbQueryRefundResp resp)throws Exception{
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(req,"请求信息不能为空");
        List<QueryRefund> resps = queFgbQueryRefund(req,loginInfo,returnInfo);
        if(resps!=null && resps.size()>0){
            resp.setList(resps);
        }
        return returnInfo;
    }

    public List<QueryRefund> queFgbQueryRefund(QueFgbQueryRefundReq req, LoginInfo loginInfo, ReturnInfo returnInfo)throws Exception{

        CheckUtils.checkNull(req.getRefundTypeId(), "CMMS: 退费类型不能为空. ");
        CheckUtils.checkEmpty(req.getBeginTime(), "CMMS: 开始时间不能为空. ");
        CheckUtils.checkEmpty(req.getEndTime(), "CMMS: 结束时间不能为空. ");

        if(StringUtils.isBlank(req.getAccessNum()) && req.getServId()==null){
            CheckUtils.checkEmpty(null, "CMMS: servid 和 手机号不能同时为空. ");
        }

        Date bTime = DateUtils.parseTime(req.getBeginTime());
        Date eTime = DateUtils.parseTime(req.getEndTime());

        Map<String, Object> bossreq = new HashMap<>();
        if(req.getServId()!=null){
            bossreq.put("serv_id", req.getServId());
        }
        if(req.getCustId()!=null){
            bossreq.put("cust_id", req.getCustId());
        }

        bossreq.put("begin_time", bTime);
        bossreq.put("end_time", eTime);

        if(StringUtils.isNotBlank(req.getAccessNum())){
            bossreq.put("access_num", req.getAccessNum());
        }

        if(StringUtils.isNotBlank(req.getCity())){
            bossreq.put("city", req.getCity());
        }

        bossreq.put("refund_type_id", req.getRefundTypeId());

        String servicCode = BizConstant.M5gInterface.QUE_FGB_QUERYREFUND;

        String response = getBossHttpInfo(req.getBizorderid(),servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);

        if(StringUtils.isNotBlank(output)){
            List<QueryRefund> info = JSON.parseObject(output, new TypeReference<List<QueryRefund>>() {});
            return info;
        }

        return new ArrayList<>();
    }


    /**
     * H5人脸识别认证
     * @return
     */
    public ReturnInfo authIdentifyH5(AuthIndetifyH5Req req, AuthIndetifyH5Resp resp)throws Exception{

        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();

        CheckUtils.checkNull(req.getPoid(), "CMMS: 流程编号不能为空. ");
        CheckUtils.checkNull(req.getName(), "CMMS: 姓名不能为空. ");
        CheckUtils.checkNull(req.getCertNo(), "CMMS: 证据号不能为空. ");
        CheckUtils.checkNull(req.getCertType(), "CMMS: 认证方式不能为空. ");

        //查询有效时间内的流程对应的记录
        String sql = "select * from BIZ_AUTH_ORDER t where t.poid=? and t.certno =? and t.certtype=? and t.maxvalidatedatetime > ? and  t.status='0' order by createtime desc ";
        List<BizAuthOrder> list = getDAO().find(sql,BizAuthOrder.class,req.getPoid(),req.getCertNo(),req.getCertType(),System.currentTimeMillis()+5*60*1000);
        if(list!=null&&list.size()>0){
            BizAuthOrder bizAuthOrder = list.get(0);
            QueH5AuthResultResp resp1 = qryH5AuthResult(bizAuthOrder,loginInfo);
            if("0".equals(resp1.getCode())){
                bizAuthOrder.setStatus("2");
                bizAuthOrder.setUpdatetime(new Date());
                getDAO().saveOrUpdate(bizAuthOrder);
            }else {
                resp.setIdentifyUrl(bizAuthOrder.getIdentifyurl());
                resp.setOrderid(bizAuthOrder.getOrderid());
                return returnInfo;
            }
        }
        if(StringUtils.isBlank(req.getCity())){
            req.setCity(loginInfo.getCity());
        }
        req.setChannelCode("CMMS");
        //判断是否在有效时间内，如果是则判断是否已验证了，已验证了创建新的认证记录覆盖原来记录，然后返回新链接
        BizAuthOrder bizAuthOrder = new BizAuthOrder();
        bizAuthOrder.setStatus("0");
        bizAuthOrder.setPoid(req.getPoid());
        bizAuthOrder.setCertno(req.getCertNo());
        bizAuthOrder.setCerttype(req.getCertType());
        bizAuthOrder.setCity(req.getCity());
        bizAuthOrder.setChannelcode(req.getChannelCode());
        bizAuthOrder.setName(req.getName());
        bizAuthOrder.setPhone(req.getPhone());
        bizAuthOrder.setPageredirecturl(req.getPageRedirectUrl());
        bizAuthOrder.setOpertype(req.getOperType());
        bizAuthOrder.setInfoowner(req.getInfoOwner());
        bizAuthOrder.setCreatetime(new Date());

        String servicCode = BizConstant.M5gInterface.H5_AUTH_IDENTIFY_REQ;
        String response = getBossHttpInfo(getBizorderid(),servicCode , req, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);
        if(StringUtils.isNotBlank(output)){
            AuthIndetifyH5Resp  authIndetifyH5Resp = JSON.parseObject(output, new TypeReference<AuthIndetifyH5Resp>() {
            });
            if (authIndetifyH5Resp!=null){
                long maxvalidateDatetime= DateUtils.getDate(authIndetifyH5Resp.getMaxValidateDateTime(),DateUtils.FORMAT_YYYYMMDDHHMM).getTime();
                resp.setIdentifyUrl(authIndetifyH5Resp.getIdentifyUrl());
                resp.setOrderid(authIndetifyH5Resp.getOrderid());
                bizAuthOrder.setOrderid(authIndetifyH5Resp.getOrderid());
                bizAuthOrder.setIdentifyurl(authIndetifyH5Resp.getIdentifyUrl());
                bizAuthOrder.setMaxvalidatedatetime(maxvalidateDatetime);
                getDAO().executeSql("INSERT INTO BIZ_AUTH_ORDER_BAK(AID,POID,NAME,CERTNO,CERTTYPE,PHONE,PAGEREDIRECTURL,MAXVALIDATEDATETIME,IDENTIFYURL,ORDERID,STATUS,CREATETIME,UPDATETIME,CHANNELCODE,CITY,OPERTYPE,INFOOWNER,COMPLETETIME) SELECT AID,POID,NAME,CERTNO,CERTTYPE,PHONE,PAGEREDIRECTURL,MAXVALIDATEDATETIME,IDENTIFYURL,ORDERID,STATUS,CREATETIME,UPDATETIME,CHANNELCODE,CITY,OPERTYPE,INFOOWNER,COMPLETETIME FROM BIZ_AUTH_ORDER T WHERE T.POID=? " ,req.getPoid());
                getDAO().executeSql("DELETE FROM BIZ_AUTH_ORDER T WHERE T.POID=? " ,req.getPoid() );

                getDAO().save(bizAuthOrder);

            }
        }

        return returnInfo;
    }

    /**
     * H5人脸识别认证结果查询
     * @return
     */
    public ReturnInfo qryH5AuthResult(QueH5AuthResultReq req, QueH5AuthResultResp resp)throws Exception{

        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        if((req.getPoid()==null||req.getPoid()<=0)&&StringUtils.isBlank(req.getOrderid())){
            throw new BusinessException("CMMS:查询参数不能为空");
        }
        BizAuthOrder bizAuthOrder = new BizAuthOrder();
        if(req.getPoid()!=null&&req.getPoid()>0) {
            bizAuthOrder.setPoid(req.getPoid());
        }
        if(StringUtils.isNotBlank(req.getOrderid())) {
            bizAuthOrder.setOrderid(req.getOrderid());
        }
        List<BizAuthOrder> list = getDAO().find(bizAuthOrder);
        if(list==null||list.size()<=0){
            resp.setCode("-1");
            return returnInfo;
        }
        bizAuthOrder= list.get(0);
        if(bizAuthOrder!=null&&"2".equals(bizAuthOrder.getStatus())){
            resp.setCode("0");
            return returnInfo;
        }else if(bizAuthOrder!=null&&"3".equals(bizAuthOrder.getStatus())){
            resp.setCode("2"); //2,已核销
            return returnInfo;
        }
        QueH5AuthResultResp resp1 = qryH5AuthResult(bizAuthOrder,loginInfo);
        resp.setCompleteTime(resp1.getCompleteTime());
        resp.setErrMsg(resp1.getErrMsg());
        resp.setCode(resp1.getCode());
        resp.setOrderid(bizAuthOrder.getOrderid());
        if("0".equals(resp.getCode())){
            bizAuthOrder.setStatus("2");
            bizAuthOrder.setUpdatetime(new Date());
            bizAuthOrder.setCompletetime(resp1.getCompleteTime());
            getDAO().saveOrUpdate(bizAuthOrder);
        }
        return returnInfo;
    }

    public QueH5AuthResultResp qryH5AuthResult(BizAuthOrder bizAuthOrder,LoginInfo loginInfo)throws Exception{
        QueH5AuthResultResp  resp = new QueH5AuthResultResp();
        try {
           Map<String,Object> reqBoss = new HashMap<>();
           reqBoss.put("orderId",bizAuthOrder.getOrderid());
           reqBoss.put("name",bizAuthOrder.getName());
           reqBoss.put("certNo",bizAuthOrder.getCertno());

           String servicCode = BizConstant.M5gInterface.H5_AUTH_IDENTIFY_RESULT_QUERY;
           String response = getBossHttpInfo(getBizorderid(),servicCode , reqBoss, loginInfo);
           String output =  getOutput(response,servicCode,null);
           resp = JSON.parseObject(output, new TypeReference<QueH5AuthResultResp>() {
           });
       }catch (Exception e ){
            e.printStackTrace();
            resp.setCode("-1");
        }

        return resp;
    }

    /**
     * 认证类型
     * @return
     */
    public ReturnInfo qryAuthIdetifyType(AuthIndetifyH5Req req, QueAuthINdetifyTypeResp resp)throws Exception{
        LoginInfo loginInfo = getLoginInfo();
        String city = loginInfo.getCity();
        //查询有效时间内的流程对应的记录
        String sql = "SELECT * from  prv_sysparam where gcode = 'AUTH_IDETIFY_DATA'";
        List<PrvSysparam> list = getDAO().find(sql,PrvSysparam.class);

        if(list!=null&&!list.isEmpty()){
            // resp.setType(list.get(0).getData());
            String type = "0";
            PrvSysparam prvSysparam = list.get(0);
            String mcode = prvSysparam.getMcode();
            if ("*".equals(mcode)) {
                type = prvSysparam.getData();
            } else {
                for (String ci : mcode.split(",")) {
                    if (StringUtils.equalsIgnoreCase(city, ci)) {
                        type = prvSysparam.getData();
                    }
                }
            }
            resp.setType(type);
        }else{
            resp.setType("0");//0表示普通认证，1表示h5认证，0~1表示两种都有
        }
        return  initReturnInfo();
    }


    /**
     * 	5G未开票信息查询
     * @param req
    **/
    public ReturnInfo queM5gNotInvoiceinfo(QueM5gInvoiceinfoReq req, QueM5gNotInvoiceinfoResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        QueM5gNotInvoiceinfoResp oupResp = gwAssetQueryNotinvoiceinfo(req, loginInfo, returnInfo);
        if (oupResp != null) {
            resp.setInvoicePoolBalance(oupResp.getInvoicePoolBalance());
            resp.setMonthInvoiceList(oupResp.getMonthInvoiceList() != null && oupResp.getMonthInvoiceList().size() > 0 ? oupResp.getMonthInvoiceList() : new ArrayList<>());
            resp.setReceivedInvoiceList(oupResp.getReceivedInvoiceList() != null && oupResp.getReceivedInvoiceList().size() > 0 ? oupResp.getReceivedInvoiceList() : new ArrayList<>());
            resp.setPayInvoiceList(oupResp.getPayInvoiceList() != null && oupResp.getPayInvoiceList().size() > 0 ? oupResp.getPayInvoiceList() : new ArrayList<>());
        }
        return returnInfo;
    }
    /**
     * 3.1.21.5GBOSS套餐使用量查询
     * @param rep
     * @param resp
     * @return
     * @throws Exception
     */

    public ReturnInfo gwAssetQryuserres(GwAssetQryUserResRep rep, GwAssetQryUserResResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(rep,"请求信息不能为空");
        List<GwAssetQryUserResResp> list = gwAssetQryuserres(rep,loginInfo,returnInfo);
        if(list != null && list.size() > 0) {
            resp.setUserResList(list.get(0).getUserResList());
            resp.setUserExtResList(list.get(0).getUserExtResList());
        }
        return returnInfo;
    }

    /**
     * 3.1.22.	5GBOSS用户商品订购查询
     * @return
     */
    public ReturnInfo gwOcFindfusionprodbyaccessnum(QueFindFusionReq req, QueFindFusionResp resp) throws Exception {
        LoginInfo loginInfo = getLoginInfo();
        ReturnInfo returnInfo = initReturnInfo();
        CheckUtils.checkNull(req.getCustid(), "客户编号为空,请检查!");
        CheckUtils.checkNull(req.getServid(), "用户ID为空,请检查!");
        CheckUtils.checkNull(req.getAccessNum(), "手机号为空,请检查!");
        CheckUtils.checkNull(req.getCity(), "城市为空,请检查!");

        // BOSS 参数
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("cust_id", req.getCustid());
        bossreq.put("serv_id", req.getServid());
        bossreq.put("access_num", req.getAccessNum());
        bossreq.put("city", req.getCity());

        // 调用BOSS接口
        String output = getBossHttpInfOutput(getBizorderid(), BizConstant.M5gInterface.GW_OC_FINDFUSIONPRODBYACCESSNUM, bossreq, loginInfo);
        // 转成对象
        List<QueFindFusionInfo> data = JSON.parseObject(output, new TypeReference<ArrayList<QueFindFusionInfo>>() {});
        resp.setData(data);
        return returnInfo;
    }
    /**
     * 5G未开票信息查询
     * @param loginInfo
     * @return
     * @throws Exception
     */
    public QueM5gNotInvoiceinfoResp gwAssetQueryNotinvoiceinfo(QueM5gInvoiceinfoReq req, LoginInfo loginInfo,ReturnInfo returnInfo) throws Exception {
        CheckUtils.checkNull(req.getCustid(), "CMMS: 客户编号不能为空. ");
        CheckUtils.checkEmpty(req.getStime(), "CMMS: 开始时间不能为空. ");
        CheckUtils.checkEmpty(req.getEtime(), "CMMS: 结束时间不能为空. ");
        if (req.getServid() == null && StringUtils.isBlank(req.getMobileno())) {
            throw new BusinessException("CMMS: 用户编号和手机号不能同时为空. ");
        }
        Date sTime = DateUtils.parseDate(req.getStime());
        Date eTime = DateUtils.parseDate(req.getEtime());
        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("custid", req.getCustid());
        bossreq.put("stime", DateUtils.formatDate(sTime));
        bossreq.put("etime", DateUtils.formatDate(eTime));
        bossreq.put("servid", req.getServid());
        bossreq.put("mobileno", req.getMobileno());
        if(StringUtils.isNotBlank(req.getSourceType())) {
            bossreq.put("source_type", req.getSourceType());
        } else{
            bossreq.put("source_type", "2");
        }
        if(StringUtils.isNotBlank(req.getSubscriberType())) {
            bossreq.put("subscriber_type", req.getSubscriberType());
        } else{
            bossreq.put("subscriber_type", "1");
        }
        if(StringUtils.isNotBlank(req.getGroupid())) {
            bossreq.put("groupid", req.getGroupid());
        }
        String servicCode = BizConstant.M5gInterface.GW_QUERY_NOT_ISSUED_INVOICE;
        String response = getBossHttpInfo(getBizorderid(),servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);
        if(StringUtils.isNotBlank(output)){
            QueM5gNotInvoiceinfoResp  resp = JSON.parseObject(output, new TypeReference<QueM5gNotInvoiceinfoResp>() {
            });
            return resp;
        }
        return null;

    }

    /**
     * 新开具发票
     * @param req
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo gwUnifiedApplyinoice(GwUnifiedApplyinoiceReq req, AssetApplyinoiceResp resp) throws Exception {

        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        if(req.getPrintIdList()==null||req.getPrintIdList().size()<=0){
            throw new BusinessException("CMMS: 开具发票信息列表不能为空");
        }
        CheckUtils.checkEmpty(req.getSourceType(), "CMMS: 发票数据类型不能为空. ");
        CheckUtils.checkEmpty(req.getBuyType(), "CMMS: 发票抬头类型不能为空. ");
        JSONArray printList = new JSONArray();
        for (ApplyInoiceBo applyInoiceBo : req.getPrintIdList()) {
            JSONObject applyInoiceJson = new JSONObject();
            applyInoiceJson.put("print_id",applyInoiceBo.getPrintId());
            applyInoiceJson.put("create_date",applyInoiceBo.getCreateDate());
            applyInoiceJson.put("print_amount",applyInoiceBo.getPrintAmount());
            printList.add(applyInoiceJson);
        }
        JSONObject bossreq = new JSONObject();
        bossreq.put("print_id_list",printList);
        bossreq.put("source_type",req.getSourceType());
        bossreq.put("buy_type",req.getBuyType());
        bossreq.put("customer_name",req.getCustomerName());
        bossreq.put("tax_identify_code",req.getTaxIdentifyCode());
        bossreq.put("tax_address",req.getTaxAddress());
        bossreq.put("tax_phone",req.getTaxPhone());
        bossreq.put("open_bank",req.getOpenBank());
        bossreq.put("bank_account",req.getBankAccount());
        bossreq.put("email",req.getEmail());
        bossreq.put("serv_id",req.getBankAccount());
        bossreq.put("access_num",req.getAccessNum());
        bossreq.put("group_id",req.getGroupId());
        String servicCode = BizConstant.M5gInterface.GW_UNIFIED_APPLYINOICE;
        String response = getBossHttpInfo(getBizorderid(),servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);

        return returnInfo;
    }

    public List<GwAssetQryUserResResp> gwAssetQryuserres(GwAssetQryUserResRep req, LoginInfo loginInfo, ReturnInfo returnInfo)throws Exception{

        if(StringUtils.isBlank(req.getAccessNum()) && req.getServId()==null){
            CheckUtils.checkEmpty(null, "CMMS: servid 和 手机号不能同时为空. ");
        }
        Map<String, Object> bossreq = new HashMap<>();

        if(req.getServId()!=null){
            bossreq.put("serv_id", req.getServId());
        }
        if(req.getAccessNum()!=null){
            bossreq.put("access_num", req.getAccessNum());
        }

        if (StringUtils.isNotBlank(req.getCycleId())) {
            bossreq.put("cycle_id", req.getCycleId());
        }

        if(StringUtils.isNotBlank(req.getCity())){
            bossreq.put("city", req.getCity());
        }

        String servicCode = BizConstant.M5gInterface.GW_ASSET_QRYUSERRES;

        String response = getBossHttpInfo(req.getBizorderid(),servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);

        if(StringUtils.isNotBlank(output)){
            List<GwAssetQryUserResResp> info = JSON.parseObject(output, new TypeReference<List<GwAssetQryUserResResp>>() {});
            return info;
        }

        return new ArrayList<>();
    }

    /**
     * 查询客户已销户手机号
     * @param rep
     * @param resp
     * @return
     * @throws Exception
     */
    public ReturnInfo queFgbDestroyed(QueFgbDestroyedRep rep, QueFgbDestroyedResp resp) throws Exception {
        ReturnInfo returnInfo = initReturnInfo();
        LoginInfo loginInfo = getLoginInfo();
        CheckUtils.checkNull(rep,"请求信息不能为空");
        List<QueFgbDestroyedItem> list = queFgbDestroyed(rep,loginInfo,returnInfo);
        if (list != null) {
            resp.setList(list);
        }
        return returnInfo;
    }

    /**
     * 查询客户已销户手机号
     * @param req
     * @param loginInfo
     * @param returnInfo
     * @return
     * @throws Exception
     */
    public List<QueFgbDestroyedItem> queFgbDestroyed(QueFgbDestroyedRep req, LoginInfo loginInfo, ReturnInfo returnInfo) throws Exception {
        CheckUtils.checkNull(req.getCustId(), "CMMS: 客户编号不能为空. ");

        Map<String, Object> bossreq = new HashMap<>();
        bossreq.put("custid", req.getCustId());

        String servicCode = BizConstant.M5gInterface.QUE_FGB_DESTROYED;
        String response = getBossHttpInfo(getBizorderid(),servicCode , bossreq, loginInfo);
        String output =  getOutput(response,servicCode,returnInfo);
        if(StringUtils.isNotBlank(output)){
            List<QueFgbDestroyedItem>  list = JSON.parseObject(output, new TypeReference<List<QueFgbDestroyedItem>>() {
            });
            return list;
        }
        return null;

    }

}
