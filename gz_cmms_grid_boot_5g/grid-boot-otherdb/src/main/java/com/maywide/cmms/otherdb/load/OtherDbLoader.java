package com.maywide.cmms.otherdb.load;

import com.maywide.biz.core.servlet.SpringBeanUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Order(9)
public class OtherDbLoader implements ApplicationRunner {

    private Logger log = LoggerFactory.getLogger(OtherDbLoader.class);

    @Value("${otherdb.enable:false}")
    private Boolean enable;
    @Value("${otherdb.activedbs:}")
    private List<String> activeDbs;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("==> 第三方数据库是否启用: {}", enable);
        if (enable.booleanValue()) {
            if (activeDbs != null && !activeDbs.isEmpty()) {
                for (String activeDb : activeDbs) {
                    Thread thread = new Thread(new Runnable() {
                        @Override
                        public void run() {
                            activeOtherDb(activeDb);
                        }
                    });
                    thread.setName(activeDb);
                    thread.start();
                }
            }
        }
    }

    private void activeOtherDb(String activeDb) {
        log.info("==> 激活数据库: {}", activeDb);
        switch (activeDb) {
            case ActiveDbCons.REPORT_DB:
                activeReportDb();
                break;
            case ActiveDbCons.BI_DB:
                activeBiDb();
                break;
            case ActiveDbCons.PORTAL_DB:
                activePortalDb();
                break;
            case ActiveDbCons.BI_SYDT_DB:
                activeBiSydtDb();
                break;
            case ActiveDbCons.NC_DB:
                activeNcDb();
                break;
            case ActiveDbCons.BILLING_DB:
                activeBillingDb();
                break;
            case ActiveDbCons.ODS_DB:
                activeOdsDb();
                break;
            case ActiveDbCons.BI_KPI_DB:
                activeBiKpiDb();
                break;
            default:
                log.info("==> Db[{}] is not defined", activeDb);
                break;
        }
    }

    private void activeBiKpiDb() {
        try{
            ClassPathXmlApplicationContext biKpiContext = new ClassPathXmlApplicationContext(
                    new String[] {"classpath:config/xml/bi-kpi-context.xml"});
            SpringBeanUtil.setBiKpiContext(biKpiContext);
        }catch(Exception e){
            log.error("BiKpi数据库连接失败", e);
        }
    }

    private void activeOdsDb() {
        try{
            ClassPathXmlApplicationContext odsContext = new ClassPathXmlApplicationContext(
                    new String[] {"classpath:config/xml/oracle-oss-context.xml"});
            SpringBeanUtil.setOdsContext(odsContext);
        }catch(Exception e){
            log.error("ODS数据库连接失败", e);
        }
    }

    private void activeBillingDb() {
        try{
            ClassPathXmlApplicationContext nccontext = new ClassPathXmlApplicationContext(
                    new String[] {"classpath:config/xml/billing-oracle-context.xml"});
            SpringBeanUtil.setBillingcontext(nccontext);
        }catch(Exception e){
            log.error("BILLING数据库连接失败", e);
        }
    }

    private void activeNcDb() {
        try{
            ClassPathXmlApplicationContext nccontext = new ClassPathXmlApplicationContext(
                    new String[] {"classpath:config/xml/nc-oracle-context.xml"});
            SpringBeanUtil.setNccontext(nccontext);
        }catch(Exception e){
            log.error("NC数据库连接失败", e);
        }
    }

    private void activeBiSydtDb() {
        try{
            ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext(
                    new String[] {"classpath:config/xml/bi-sydt-context.xml"});
            SpringBeanUtil.setBiSydtContext(context);
        }catch(Exception e){
            log.error("bi-sydt数据库连接失败", e);
        }
    }

    private void activePortalDb() {
        try{
            ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext(
                    new String[] {"classpath:config/xml/portal-mysql-context.xml"});
            SpringBeanUtil.setPortalContext(context);
        }catch(Exception e){
            log.error("portal数据库连接失败");
        }
    }

    private void activeBiDb() {
        try{
            ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext(
                    new String[] {"classpath:config/xml/oracle-context.xml"});
            SpringBeanUtil.setContext(context);
        }catch(Exception e){
            log.error("BI数据库连接失败", e);
        }
    }

    private void activeReportDb() {
        try{
            ClassPathXmlApplicationContext dbcontext = new ClassPathXmlApplicationContext(
                    new String[] {"classpath:config/xml/db-oracle-context.xml"});
            SpringBeanUtil.setDBContext(dbcontext);
        }catch(Exception e){
            log.error("报表数据库连接失败", e);
        }
    }

}
