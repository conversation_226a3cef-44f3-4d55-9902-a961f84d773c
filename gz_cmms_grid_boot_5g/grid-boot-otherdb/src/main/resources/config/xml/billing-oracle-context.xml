<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:p="http://www.springframework.org/schema/p"
    xmlns:cache="http://www.springframework.org/schema/cache" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context" xmlns:jdbc="http://www.springframework.org/schema/jdbc"
    xmlns:jee="http://www.springframework.org/schema/jee" xmlns:task="http://www.springframework.org/schema/task"
    xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jpa="http://www.springframework.org/schema/data/jpa"
    xmlns:aop="http://www.springframework.org/schema/aop" xmlns:repository="http://www.springframework.org/schema/data/repository"
    xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd"
    default-lazy-init="false">

    <description>Spring公共配置 </description>

    <bean id="log-filter" class="com.alibaba.druid.filter.logging.CommonsLogFilter">
        <property name="resultSetLogEnabled" value="true" />
        <property name="statementExecutableSqlLogEnable" value="true" />
    </bean>

    <bean id="stat-filter" class="com.alibaba.druid.filter.stat.StatFilter">
        <!--慢SQL统计，如果SQL执行时间超过一定时间则记录为慢SQL  -->
        <property name="slowSqlMillis" value="3000" />
        <!--慢SQL统计日志输出  -->
        <property name="logSlowSql" value="true" />
        <!--合并SQL统计 例如select * from table t where t.id =1，会被变为select * from table t where t.id =？来统计  -->
        <property name="mergeSql" value="true" />
    </bean>

    <bean id="dataSourceSpied"  class="com.alibaba.druid.pool.DruidDataSource">
        <property name="driverClassName" value="oracle.jdbc.driver.OracleDriver" />
        <property name="url" value="*****************************************"/>
        <property name="username" value="nods" />
        <property name="password" value="27Mh63nx"/>
        <property name="filters" value="stat,wall" />
        <property name="maxActive" value="100" />
        <property name="initialSize" value="5" />
        <property name="maxWait" value="60000" />
        <property name="minIdle" value="5" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="300000" />
        <property name="testWhileIdle" value="true" />
        <!--这两项配置会降低连接池的性能
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        -->
        <property name="poolPreparedStatements" value="true" />
        <property name="maxOpenPreparedStatements" value="20"/>
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20" />

        <!-- 超过时间限制是否回收 -->
        <property name="removeAbandoned" value="true" />
        <!-- 超时时间；单位为秒。180秒=3分钟 -->
        <property name="removeAbandonedTimeout" value="1800" />
        <!-- 关闭abanded连接时输出错误日志 -->
        <property name="logAbandoned" value="true" />

        <!-- 配置监控统计拦截的filters -->
        <property name="proxyFilters">
            <list>
                <ref bean="stat-filter" />
                <ref bean="log-filter" />
            </list>
        </property>
    </bean>

	<!-- 定制log4jdbc做一些输出SQL信息的微调 -->
    <bean id="extSlf4jSpyLogDelegator" class="com.maywide.core.dao.log4jdbc.ExtSlf4jSpyLogDelegator" /> 

    <!-- 用log4jdbc对datasouce进行包裹实现完整的SQL语句跟踪 -->
    <bean id="ds2" class="net.sf.log4jdbc.Log4jdbcProxyDataSource" depends-on="extSlf4jSpyLogDelegator">
        <constructor-arg>
            <ref bean="dataSourceSpied" />
        </constructor-arg>
    </bean>



    <!-- JPA Entity Manager 配置 -->
    <bean id="entityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
        <property name="dataSource" ref="dataSourceSpied" />
        <property name="jpaVendorAdapter">
            <bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter" />
        </property>
        <property name="persistenceUnitPostProcessors" ref="persistenceUnitPostProcessors" />
        <property name="packagesToScan" value="com.maywide.core.audit.envers,com.maywide.**.entity" />
        <property name="jpaProperties">
            <props>
                <prop key="hibernate.connection.autocommit">false</prop>
                <prop key="hibernate.physical_naming_strategy">org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy</prop>
                <prop key="hibernate.cache.region.factory_class">org.hibernate.cache.ehcache.SingletonEhCacheRegionFactory</prop>
                <prop key="net.sf.ehcache.configurationResourceName">config/ehcache-config.xml</prop>
                <prop key="hibernate.cache.use_query_cache">true</prop>
                <prop key="hibernate.cache.use_second_level_cache">true</prop>
                <prop key="hibernate.generate_statistics">true</prop>
                <prop key="hibernate.show_sql">true</prop>
                <prop key="hibernate.dialect">org.hibernate.dialect.Oracle10gDialect</prop>
                <prop key="hibernate.connection.SetBigStringTryClob">true</prop>
                <prop key="org.hibernate.envers.do_not_audit_optimistic_locking_field">false</prop>
                <prop key="org.hibernate.envers.global_with_modified_flag">true</prop>
            </props>
        </property>
    </bean>

    <bean id="persistenceUnitPostProcessors" class="com.maywide.core.dao.jpa.ExtPersistenceUnitPostProcessor" />

</beans>