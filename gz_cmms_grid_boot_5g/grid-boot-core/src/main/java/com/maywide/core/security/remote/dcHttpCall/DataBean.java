package com.maywide.core.security.remote.dcHttpCall;

import java.util.List;

public class DataBean {

        private int recordCount;
        private List<List<TheadBean>> thead;
        private List<TbodyBean> tbody;
        private List<Object> dataRows;

        public List<Object> getDataRows() {
            return dataRows;
        }

        public void setDataRows(List<Object> dataRows) {
            this.dataRows = dataRows;
        }

        public int getRecordCount() {
                    return recordCount;
                }

        public void setRecordCount(int recordCount) {
            this.recordCount = recordCount;
        }

        public List<List<TheadBean>> getThead() {
            return thead;
        }

        public void setThead(List<List<TheadBean>> thead) {
            this.thead = thead;
        }

        public List<TbodyBean> getTbody() {
            return tbody;
        }

        public void setTbody(List<TbodyBean> tbody) {
            this.tbody = tbody;
        }

    }