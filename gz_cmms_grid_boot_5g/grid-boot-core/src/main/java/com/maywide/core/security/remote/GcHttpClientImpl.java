package com.maywide.core.security.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.jsonplugin.JSONException;
import com.googlecode.jsonplugin.JSONUtil;
import com.maywide.biz.core.entity.RemotecallLog;
import com.maywide.biz.core.pojo.cmphttpinf.CmpHttpBaseRespBO;
import com.maywide.biz.core.pojo.gchttpinf.GcHttpBaseRespBO;
import com.maywide.core.util.BeanUtil;
import com.maywide.core.util.CheckUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.URIException;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class GcHttpClientImpl {

	private static Logger log = LoggerFactory.getLogger(GcHttpClientImpl.class);

	/**
	 * 执行post请求，返回请求日志对象
	 *
	 * @return
	 * @throws Exception
	 */
	public static RemotecallLog requestPost(String city, String requestid, String path, PostMethod postMethod, Object params) throws Exception {
		CheckUtils.checkNull(postMethod, "执行post请求:post方法实例不能为空");

		// 1.构造HttpClient的实例
		HttpClient httpClient = new HttpClient();
		httpClient.getParams().setContentCharset("UTF-8");
		// 2.记录RemotecallLog
		RemotecallLog retRemotecallLog = new RemotecallLog();
		retRemotecallLog.setCalltime(new Date());
		copyReqInfo2callLog(city, path, requestid, postMethod, retRemotecallLog, params);

		String status = "0";
		String retmsg = "";
		String responseStr = "";
		try {
			// 3.登记请求日志
			// logRequestInfo(remotecallLog);

			// 4.调用远程服务
			int httpCode = httpClient.executeMethod(postMethod);
			log.info("=========>httpCode=" + httpCode);
			if (httpCode == 200) {
				// 5.读取内容
				responseStr = postMethod.getResponseBodyAsString().trim();
				log.info("=========>" + postMethod.getParameter("servicecode") + " responseStr="
						+ responseStr);

			} else if (httpCode == 404) {
				status = "11";
				retmsg = "接口不存在404";
			} else if (httpCode == 500) {
				status = "12";
				retmsg = "接口调用失败500";
			} else {
				status = "13";
				retmsg = "接口调用失败" + httpCode;
			}

			// 6.组装返回信息
			retRemotecallLog.setEndtime(new Date());
			retRemotecallLog.setRetcode(status);
			retRemotecallLog.setRetmsg(retmsg);
			retRemotecallLog.setResponse(responseStr);
			copyBaseRespInfo2callLog(responseStr, retRemotecallLog);

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			// 6.组装返回信息
			retRemotecallLog.setEndtime(new Date());
			retRemotecallLog.setRetcode(status);
			retRemotecallLog.setRetmsg(retmsg);
			copyBaseRespInfo2callLog(responseStr, retRemotecallLog);

			// 7.释放连接
			postMethod.releaseConnection();
		}

		return retRemotecallLog;
	}

	private static void copyBaseRespInfo2callLog(String respJsonStr, RemotecallLog remotecallLog) {
		remotecallLog.setResponse(respJsonStr);
		GcHttpBaseRespBO gcBaseResp = getGcHttpBaseResp(respJsonStr);
		if (null != gcBaseResp) {
			if (StringUtils.isNotBlank(gcBaseResp.getErrcode())) {
				remotecallLog.setRetcode(gcBaseResp.getErrcode());
			}

			if (StringUtils.isNotBlank(gcBaseResp.getResult())) {
				remotecallLog.setRetmsg(gcBaseResp.getResult());
			}
		}
	}

	private static GcHttpBaseRespBO getGcHttpBaseResp(String jsonStr) {
		GcHttpBaseRespBO baseResp = null;
		if (StringUtils.isBlank(jsonStr)) {
			return null;
		}
		try {
			baseResp = (GcHttpBaseRespBO) BeanUtil.jsonToObject(jsonStr, GcHttpBaseRespBO.class);
		} catch (Exception e) {
			e.getStackTrace();
		}
		return baseResp;
	}

	private static void copyReqInfo2callLog(String city, String servicecode, String requestid, PostMethod postMethod, RemotecallLog remotecallLog, Object params)
			throws URIException, JSONException {
		String serviceurl = postMethod.getURI().getURI();
		remotecallLog.setServiceurl(serviceurl);

		remotecallLog.setServicecode(servicecode);
		remotecallLog.setRequestid(requestid);
		remotecallLog.setProtocol("http");

		remotecallLog.setRequest((String) params);
		remotecallLog.setClientcode(city);

	}

}
