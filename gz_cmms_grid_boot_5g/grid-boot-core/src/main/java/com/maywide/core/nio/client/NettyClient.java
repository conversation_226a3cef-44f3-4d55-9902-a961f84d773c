package com.maywide.core.nio.client;

import com.maywide.core.util.PropertyUtil;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;

/**
 * <AUTHOR>
 * @date 2020/3/23 0023
 */
public class NettyClient {

    private static Bootstrap bootstrap;

    private static Channel channel;

    private static int port;

    private static String ip;

    static {
        int port = new Integer(PropertyUtil.getValueFromProperites("sysconfig", "nio.client.port"));
        String ip = PropertyUtil.getValueFromProperites("sysconfig", "nio.client.ip");
        NettyClient.port = port;
        NettyClient.ip = ip;
    }

    /**
     * 初始化客户端
     * @throws Exception
     */
    private static void init() throws Exception {
        NioEventLoopGroup eventLoopGroup = new NioEventLoopGroup();

        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(eventLoopGroup).channel(NioSocketChannel.class)
                .handler(new Clientinitializer());
        NettyClient.bootstrap = bootstrap;
    }

    /**
     * 连接服务端，创建频道
     * @throws Exception
     */
    private static void createChannel() throws Exception {
        if (bootstrap == null) {
            init();
        }
        ChannelFuture channelFuture = bootstrap.connect(ip, port).sync();
        channel = channelFuture.channel();
    }

    /**
     * 获得频道
     * @return
     * @throws Exception
     */
    public static Channel getChannel() throws Exception {
        if (channel == null || !channel.isActive()) {
            createChannel();
        }
        return channel;
    }
}
