package com.maywide.core.log;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.maywide.core.util.RequestIdUtil;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * 日志请求唯一Id
 *
 * <AUTHOR>
 */
public class LogRequestId extends ClassicConverter {

    @Override
    public String convert(ILoggingEvent iLoggingEvent) {
        String threadName = iLoggingEvent.getThreadName();
        String requestId = RequestIdUtil.getRequestId();
        return StringUtils.isNotBlank(requestId) ? requestId : threadName;
    }

}
