package com.maywide.core.security.remote.socket;


public final class Environment {
	public static String BOSS_ENCODING = "GBK";
	//public static final String BOSS_STATUS_SUCCESS = "000000";
	
	public static String UAP_ENCODING = "UTF-8";
	public static final String UAP_STATUS_SUCCESS = "0000";
	
	//public static final String STATUS_CAN_NOT_CONNECT_EXCEPTION = "100"; 
	//public static final String STATUS_TIMEOUT_EXCEPTION = "101"; 
	public static final String STATUS_GENERAL_EXCEPTION = "102"; 
	
	public static final String STATUS_ERROR_FORMAT = "201"; 
	//public static final String STATUS_NO_SUCH_SERVICE = "202"; 
	
	//public static final String STATUS_APPLICATION_EXCEPTION = "400"; 
}
