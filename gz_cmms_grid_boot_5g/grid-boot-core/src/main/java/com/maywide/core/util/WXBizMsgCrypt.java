package com.maywide.core.util;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Base64;

/**
 * 描述：微信参数解密工具
 *
 */
public class WXBizMsgCrypt {
    byte[] aesKey;
    String token;
    String receiveId;

    /**
     * 构造函数
     *
     * @param token          企业微信后台，开发者设置的token
     * @param encodingAesKey 企业微信后台，开发者设置的EncodingAESKey
     * @param receiveId,     不同场景含义不同，详见文档
     */
    public WXBizMsgCrypt(String token, String encodingAesKey, String receiveId) {
        this.token = token;
        this.receiveId = receiveId;
        aesKey = Base64.getDecoder().decode(encodingAesKey + "=");
    }

    /**
     * 验证并获取解密后数据
     *
     * @param msgSignature 签名串，对应URL参数的msg_signature
     * @param timeStamp    时间戳，对应URL参数的timestamp
     * @param nonce        随机串，对应URL参数的nonce
     * @param echoStr      随机串，对应URL参数的echostr
     * @return 解密之后的echoString
     * @throws Exception 执行失败，请查看该异常的错误码和具体的错误信息
     */
    public String verifyAndGetData(String msgSignature, String timeStamp, String nonce, String echoStr)
            throws Exception {
        String signature = getSignature(token, timeStamp, nonce, echoStr);

        if (!signature.equals(msgSignature)) {
            throw new Exception("参数验签不通过");
        }

        return decrypt(echoStr);
    }

    /**
     * 获取参数签名
     *
     * @param token
     * @param timestamp
     * @param nonce
     * @param encrypt
     * @return
     * @throws Exception
     */
    private String getSignature(String token, String timestamp, String nonce, String encrypt) throws Exception {
        String[] array = new String[]{token, timestamp, nonce, encrypt};
        StringBuilder sb = new StringBuilder();
        // 字符串排序
        Arrays.sort(array);
        for (String s : array) {
            sb.append(s);
        }
        String str = sb.toString();
        // SHA1签名生成
        MessageDigest md = MessageDigest.getInstance("SHA-1");
        md.update(str.getBytes());
        byte[] digest = md.digest();

        StringBuilder hexStringBuilder = new StringBuilder();
        String shaHex;
        for (byte b : digest) {
            shaHex = Integer.toHexString(b & 0xFF);
            if (shaHex.length() < 2) {
                hexStringBuilder.append(0);
            }
            hexStringBuilder.append(shaHex);
        }
        return hexStringBuilder.toString();
    }

    /**
     * 对密文进行解密.
     *
     * @param text 需要解密的密文
     * @return 解密得到的明文
     * @throws Exception aes解密失败
     */
    private String decrypt(String text) throws Exception {
        // 设置解密模式为AES的CBC模式
        Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(aesKey, "AES");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(Arrays.copyOfRange(aesKey, 0, 16));
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        // 使用BASE64对密文进行解码
        byte[] encrypted = Base64.getDecoder().decode(text);
        // 解密
        byte[] original = cipher.doFinal(encrypted);
        // 去除补位字符
        byte[] bytes = decode(original);

        // 分离16位随机字符串，网络字节序和receiveId
        byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);
        int xmlLength = recoverNetworkBytesOrder(networkOrder);
        String xmlContent = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), StandardCharsets.UTF_8);
        String fromReceiveId = new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length),
                StandardCharsets.UTF_8);

        // receiveId不相同的情况
        if (!fromReceiveId.equals(receiveId)) {
            throw new Exception("receiveId不相同");
        }
        return xmlContent;
    }

    /**
     * 还原4个字节的网络字节序
     *
     * @param orderBytes
     * @return
     */
    int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;
        int length = 4;
        for (int i = 0; i < length; i++) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 0xff;
        }
        return sourceNumber;
    }

    /**
     * 删除解密后明文的补位字符
     *
     * @param decrypted 解密后的明文
     * @return 删除补位字符后的明文
     */
    private byte[] decode(byte[] decrypted) {
        int pad = decrypted[decrypted.length - 1];
        int min = 1;
        int max = 32;
        if (pad < min || pad > max) {
            pad = 0;
        }
        return Arrays.copyOfRange(decrypted, 0, decrypted.length - pad);
    }
}
