package com.maywide.core.util;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class CommonCollectionUtils<T> {


    //集合中通过不同属性去排序（降序）
    public void listSortByInt(String sortType , Class clazz , List<T> list){
        Collections.sort(list, new Comparator<T>() {
            @Override
            public int compare(T o1, T o2) {
                try {
                    BeanInfo beanInfo = Introspector.getBeanInfo(clazz);
                    PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
                    for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                        String key = propertyDescriptor.getName();
                        if (key.equals(sortType)){
                            Method getter = propertyDescriptor.getReadMethod();
                            int obj1 = Integer.valueOf(String.valueOf(getter.invoke(o1)));
                            int obj2 = Integer.valueOf(String.valueOf(getter.invoke(o2)));
                            if(obj1 == obj2) {
                                return 0;
                            }else if(obj1 > obj2) {
                                return 1;
                            }else{
                                return -1;
                            }
                        }
                    }

                }catch (Exception e){
                    e.printStackTrace();
                }
                return 0;
            }
        });
    }

}
