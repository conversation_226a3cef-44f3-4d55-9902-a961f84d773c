package com.maywide.core.security.remote.dcHttpCall;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.maywide.core.exception.BusinessException;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Created by minch on 2020/8/26 0020 14:53.
 */
public class HttpUtil {

    private static Logger log = LoggerFactory.getLogger(HttpUtil.class);

    public static String getContentPostJsonWithHeader(String url, Object params, Map<String, String> headers) throws Exception {
        String lineSeparator = System.lineSeparator();
        int httpCode = 0;
        String resp = null;
        CloseableHttpClient client = null;
        try {
            client = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            RequestConfig config = RequestConfig.custom().setConnectTimeout(60000).setSocketTimeout(60000).build();
            httpPost.setConfig(config);

            // 添加头文件
            if (headers != null && headers.size() > 0) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    httpPost.addHeader(key, value);
                }
            }

            // 添加body参数
            if (params != null) {
                StringEntity s = new StringEntity(JSON.toJSONString(params), "utf-8");
                s.setContentEncoding("UTF-8");
                s.setContentType("application/json");
                httpPost.setEntity(s);
            }

            CloseableHttpResponse response = client.execute(httpPost);
            httpCode = response.getStatusLine().getStatusCode();

            checkHttpCode(httpCode);

            HttpEntity entity = response.getEntity();

            resp = "";
            if (entity != null) {
                resp = EntityUtils.toString(entity, "utf-8");
            }
        } catch (Exception e) {
            log.info("接口调用失败:", e);
            throw new BusinessException(e.getMessage());
        }  finally {
            String cid = UUID.randomUUID().toString().trim().replaceAll("-", "");
            log.info("http post json请求: {}" +
                            "{}-url: {}{}" +
                            "{}-请求headers: {}{}" +
                            "{}-请求参数: {}{}" +
                            "{}-响应状态: {}{}" +
                            "{}-响应数据: {}",
                    lineSeparator,
                    cid, url, lineSeparator,
                    cid, headers == null ? "{}" : headers.toString(), lineSeparator,
                    cid, JSON.toJSONString(params), lineSeparator,
                    cid, httpCode, lineSeparator,
                    cid, resp);
            // 关闭httpclient
            if (client != null) {
                client.close();
            }
        }

        return resp;
    }

    /**
     * Post 接口调用
     * @param url 接口
     * @param params 参数
     * @return
     * @throws Exception
     */
    public static String getContentPost(String url, Map<String, String> params) throws Exception {

        String resp = "";
        int httpCode = 0;

        CloseableHttpClient client = null;

        try {
            client = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);

            if (params != null) {
                List<NameValuePair> paramList = new ArrayList<NameValuePair>();

                for (Map.Entry<String, String> entry : params.entrySet()) {
                    paramList.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }

                httpPost.setEntity(new UrlEncodedFormEntity(paramList, Charset.forName("utf-8")));
            }
            CloseableHttpResponse response = client.execute(httpPost);
            httpCode = response.getStatusLine().getStatusCode();

            checkHttpCode(httpCode);

            HttpEntity entity = response.getEntity();

            if (entity != null) {
                resp = EntityUtils.toString(entity, "utf-8");
            }
        } catch (Exception e) {
            log.info("接口调用失败:", e);
            throw new BusinessException(e.getMessage());
        } finally {
            if (client != null) {
                client.close();
            }
        }

        return resp;
    }

    private static void checkHttpCode(int httpCode) throws BusinessException {
        if (httpCode == 200) {
            return;
        } else if (httpCode == 404) {
            throw new BusinessException("接口不存在404");
        } else if (httpCode == 500) {
            throw new BusinessException("接口调用失败500");
        } else {
            throw new BusinessException("接口调用失败" + httpCode);
        }
    }


    /**
     * 发送get请求  add by lzt 2022-01-08
     * @param url
     * @param map
     * @return
     */
    public static String getContentGet(String url,Map<String, String> map){

        String resulrStr = null;
        StringBuffer parms = null;

        //创建HttpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //设置请求参数
        if(null != map && !map.isEmpty()){
            if(!url.endsWith("?")){
                url += "?";
            }

            //遍历map
            parms = new StringBuffer();
            for(Map.Entry<String , String> entry:map.entrySet()){
                parms.append("&"+entry.getKey()+"="+entry.getValue());
            }
            parms.deleteCharAt(0);
            url = url + parms.toString();
        }
        //创建GET请求方法的实例，并填充url
        HttpGet httpGet = new HttpGet(url);
        try {
            //发送（执行）请求
            CloseableHttpResponse response = httpClient.execute(httpGet);
            //获取响应头、内容
            int statusCode =  response.getStatusLine().getStatusCode();
            //打印状态码
            System.out.println("执行状态码："+statusCode);
            HttpEntity entity = response.getEntity();
            resulrStr = IOUtils.toString(entity.getContent(),"UTF-8");
            response.close();
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            //关闭连接释放资源
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resulrStr;
    }

    /**
     * get 接口调用
     * @param url 接口
     * @param params 参数
     * @return
     * @throws Exception
     */
    public static String getContentGetJsonWithHeader(String url, Object params, Map<String, String> headers) throws Exception {
        String lineSeparator = System.lineSeparator();
        int httpCode = 0;
        String resp = null;
        CloseableHttpClient client = null;
        try {
            client = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);
            RequestConfig config = RequestConfig.custom().setConnectTimeout(60000).setSocketTimeout(60000).build();
            httpGet.setConfig(config);

            // 添加头文件
            if (headers != null && headers.size() > 0) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    System.out.println("key===="+key+"  value===="+value);
                    httpGet.addHeader(key, value);
                }
            }

            // 添加body参数   ----- get请求方式不用参数
            /*if (params != null) {
                StringEntity s = new StringEntity(JSON.toJSONString(params), "utf-8");
                s.setContentEncoding("UTF-8");
                s.setContentType("application/json");
                httpGet.setEntity(s);
            }*/

            CloseableHttpResponse response = client.execute(httpGet);
            httpCode = response.getStatusLine().getStatusCode();

            checkHttpCode(httpCode);

            HttpEntity entity = response.getEntity();

            resp = "";
            if (entity != null) {
                resp = EntityUtils.toString(entity, "utf-8");
            }
        } catch (Exception e) {
            log.info("接口调用失败:", e);
            throw new BusinessException(e.getMessage());
        }  finally {
            String cid = UUID.randomUUID().toString().trim().replaceAll("-", "");
            log.info("http get json请求: {}" +
                            "{}-url: {}{}" +
                            "{}-请求headers: {}{}" +
                            "{}-请求参数: {}{}" +
                            "{}-响应状态: {}{}" +
                            "{}-响应数据: {}",
                    lineSeparator,
                    cid, url, lineSeparator,
                    cid, headers == null ? "{}" : headers.toString(), lineSeparator,
                    cid, JSON.toJSONString(params), lineSeparator,
                    cid, httpCode, lineSeparator,
                    cid, resp);
            // 关闭httpclient
            if (client != null) {
                client.close();
            }
        }

        return resp;
    }


}
