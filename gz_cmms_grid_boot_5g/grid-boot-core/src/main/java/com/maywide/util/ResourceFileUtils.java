package com.maywide.util;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 配置工具类
 *
 * <AUTHOR>
 * @date 2021/12/01 00:00
 * @since 0.0.1
 */
public class ResourceFileUtils {

    /**
     * 查询全部配置（每行作为一个字符串）
     * @param filePath 文件路径
     * @return List<String>
     */
    public static List<String> readLine(String filePath) {
        List<String> configs = new ArrayList<>();
        try(InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String s;
            while((s = reader.readLine())!=null) {
                configs.add(s);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return configs;
    }

    /**
     * 查询全部配置（每行以 = 号为分隔）
     * @param filePath 文件路径
     * @return Map<String,String>
     */
    public static Map<String,String> readProperties(String filePath) {
        Map<String,String> config = new HashMap<>(16);
        try(InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));) {
            String s;
            while((s = reader.readLine()) != null) {
                // 判断是否有等号
                if(s.contains("=")) {
                    String[] strings = s.split("=");
                    try {
                        config.put(strings[0],strings[1]);
                    }catch (Exception e) {
                        config.put(strings[0],"");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return config;
    }

    /**
     * 查询全部配置内容
     * @param filePath 文件路径
     * @return String
     */
    public static String readContent(String filePath) {
        StringBuilder config = new StringBuilder();
        try(InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(filePath);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));) {
            String s;
            while((s = reader.readLine()) != null) {
                config.append(s).append("\r\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return config.toString();
    }

}
